import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { map, catchError, retry } from 'rxjs/operators';

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  homepage: string | null;
  language: string | null;
  stargazers_count: number;
  forks_count: number;
  updated_at: string;
  created_at: string;
  fork: boolean;
  archived: boolean;
  disabled: boolean;
  private: boolean;
  topics: string[];
  size: number;
}

export interface ProcessedRepository {
  id: number;
  name: string;
  displayName: string;
  description: string;
  githubUrl: string;
  liveUrl: string | null;
  language: string;
  stars: number;
  forks: number;
  lastUpdated: Date;
  created: Date;
  topics: string[];
  imageUrl: string;
  isFeatured: boolean;
}

export interface GitHubApiError {
  message: string;
  status: number;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class GithubService {
  private readonly GITHUB_API_URL = 'https://api.github..com';
  private readonly USERNAME = 'macamisp';
  private readonly FEATURED_REPOS = [
    'ICET-110Hospital-Management-Frontend',
    'Nitro-gass-FRONTEND',
    'simple_weather',
    'possystem'
  ];

  constructor(private http: HttpClient) { }

  /**
   * Fetch all repositories from GitHub API
   */
  getRepositories(): Observable<ProcessedRepository[]> {
    const url = `${this.GITHUB_API_URL}/users/${this.USERNAME}/repos`;

    return this.http.get<GitHubRepository[]>(url).pipe(
      retry(2),
      map(repos => this.processRepositories(repos)),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * Get only featured repositories
   */
  getFeaturedRepositories(): Observable<ProcessedRepository[]> {
    return this.getRepositories().pipe(
      map(repos => repos.filter(repo => repo.isFeatured))
    );
  }

  /**
   * Get repositories by language
   */
  getRepositoriesByLanguage(language: string): Observable<ProcessedRepository[]> {
    return this.getRepositories().pipe(
      map(repos => repos.filter(repo =>
        repo.language?.toLowerCase() === language.toLowerCase()
      ))
    );
  }

  /**
   * Process raw GitHub repositories into our format
   */
  private processRepositories(repos: GitHubRepository[]): ProcessedRepository[] {
    return repos
      .filter(repo => this.shouldIncludeRepository(repo))
      .map(repo => this.transformRepository(repo))
      .sort((a, b) => {
        // Featured repos first, then by stars, then by last updated
        if (a.isFeatured && !b.isFeatured) return -1;
        if (!a.isFeatured && b.isFeatured) return 1;
        if (a.stars !== b.stars) return b.stars - a.stars;
        return b.lastUpdated.getTime() - a.lastUpdated.getTime();
      });
  }

  /**
   * Filter criteria for repositories
   */
  private shouldIncludeRepository(repo: GitHubRepository): boolean {
    return !repo.fork &&
           !repo.archived &&
           !repo.disabled &&
           !repo.private &&
           repo.size > 0 && // Has actual content
           (repo.description !== null || repo.language !== null);
  }

  /**
   * Transform GitHub repo to our format
   */
  private transformRepository(repo: GitHubRepository): ProcessedRepository {
    const displayName = this.formatRepositoryName(repo.name);
    const imageUrl = this.generatePlaceholderImage(repo.name, repo.language);

    return {
      id: repo.id,
      name: repo.name,
      displayName,
      description: repo.description || `${repo.language} project`,
      githubUrl: repo.html_url,
      liveUrl: repo.homepage || null,
      language: repo.language || 'Unknown',
      stars: repo.stargazers_count,
      forks: repo.forks_count,
      lastUpdated: new Date(repo.updated_at),
      created: new Date(repo.created_at),
      topics: repo.topics || [],
      imageUrl,
      isFeatured: this.FEATURED_REPOS.includes(repo.name)
    };
  }

  /**
   * Format repository name for display
   */
  private formatRepositoryName(name: string): string {
    return name
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .replace(/\bFrontend\b/gi, 'Frontend')
      .replace(/\bBackend\b/gi, 'Backend')
      .replace(/\bApi\b/gi, 'API')
      .replace(/\bUi\b/gi, 'UI')
      .replace(/\bPos\b/gi, 'POS');
  }

  /**
   * Generate placeholder image URL
   */
  private generatePlaceholderImage(name: string, language: string | null): string {
    const colors = {
      'JavaScript': '4CAF50',
      'TypeScript': '2196F3',
      'HTML': 'FF5722',
      'CSS': '9C27B0',
      'Java': 'FF9800',
      'Python': '3F51B5',
      'Unknown': '607D8B'
    };

    const color = colors[language as keyof typeof colors] || colors['Unknown'];
    const text = encodeURIComponent(name.replace(/[-_]/g, ' '));

    return `https://via.placeholder.com/400x250/${color}/FFFFFF?text=${text}`;
  }

  /**
   * Handle HTTP errors
   */
  private handleError(error: HttpErrorResponse): Observable<ProcessedRepository[]> {
    const apiError: GitHubApiError = {
      message: error.error?.message || 'Failed to fetch repositories',
      status: error.status,
      timestamp: new Date()
    };

    console.error('GitHub API Error:', apiError);

    // Return empty array as fallback
    return of([]);
  }
}
