<div class="about-container">
  <!-- Background Animation -->
  <app-background-animation
    [particleCount]="20"
    animationType="geometric"
    intensity="low">
  </app-background-animation>

  <div class="section-header">
    <h2 class="section-title">About Me</h2>
    <div class="section-divider"></div>
  </div>

  <div class="about-content">
    <div class="about-text">
      <h3>Who I Am</h3>
      <p>{{ personalInfo.bio }}</p>

      <div class="personal-info">
        <div class="info-item">
          <span class="info-label">Name:</span>
          <span class="info-value">{{ personalInfo.name }}</span>
        </div>
        
        <div class="info-item">
          <span class="info-label">Phone:</span>
          <span class="info-value">{{ personalInfo.phone }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">Location:</span>
          <span class="info-value">{{ personalInfo.location }}</span>
        </div>
        
        <div class="info-item">
          <span class="info-label">WhatsApp:</span>
          <span class="info-value">{{ personalInfo.whatsapp }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">Email:</span>
          <span class="info-value">{{ personalInfo.email }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Languages & Hobbies Section -->
  <div class="additional-info">
    <div class="info-section">
      <h3 class="info-title">Languages</h3>
      <div class="languages-grid">
        <div class="language-item" *ngFor="let language of languages">
          <span class="language-name">{{ language.name }}</span>
          <span class="language-level">{{ language.level }}</span>
        </div>
      </div>
    </div>

    <div class="info-section">
      <h3 class="info-title">Hobbies & Interests</h3>
      <div class="hobbies-grid">
        <div class="hobby-item" *ngFor="let hobby of hobbies">
          <i [class]="hobby.icon"></i>
          <span>{{ hobby.name }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Timeline Section macxami impress-->
  <div class="timeline-container">
    <div class="timeline-section">
      <h3 class="timeline-title">Education</h3>
      <div class="timeline">
        <div class="timeline-item" *ngFor="let edu of education">
          <div class="timeline-dot"></div>
          <div class="timeline-date">{{ edu.period }}</div>
          <div class="timeline-content">
            <h4>{{ edu.degree }}</h4>
            <p>{{ edu.institution }}, {{ edu.location }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="timeline-section">
      <h3 class="timeline-title">Work Experience</h3>
      <div class="timeline">
        <div class="timeline-item" *ngFor="let work of workExperience">
          <div class="timeline-dot"></div>
          <div class="timeline-date">{{ work.period }}</div>
          <div class="timeline-content">
            <h4>{{ work.position }}</h4>
            <p>{{ work.company }}</p>
            <ul class="responsibility-list">
              <li *ngFor="let responsibility of work.responsibilities">{{ responsibility }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="timeline-section fitness-section">
      <h3 class="timeline-title">
        <i class="fas fa-dumbbell fitness-icon"></i>
        Fitness Journey
      </h3>
      <div class="timeline">
        <div class="timeline-item fitness-item" *ngFor="let fitness of fitnessJourney">
          <div class="timeline-dot fitness-dot"></div>
          <div class="timeline-date">{{ fitness.period }}</div>
          <div class="timeline-content">
            <h4>{{ fitness.position }}</h4>
            <p class="fitness-subtitle">{{ fitness.company }}</p>
            <div class="fitness-highlights">
              <div class="highlight-card">
                <i class="fas fa-trophy"></i>
                <span>Strength Training Excellence</span>
              </div>
              <div class="highlight-card">
                <i class="fas fa-heartbeat"></i>
                <span>Endurance Development</span>
              </div>
              <div class="highlight-card">
                <i class="fas fa-target"></i>
                <span>Goal-Oriented Mindset</span>
              </div>
              <div class="highlight-card">
                <i class="fas fa-brain"></i>
                <span>Mental Resilience</span>
              </div>
            </div>
            <ul class="responsibility-list fitness-achievements">
              <li *ngFor="let responsibility of fitness.responsibilities">{{ responsibility }}</li>
            </ul>
            <div class="fitness-connection">
              <h5><i class="fas fa-link"></i> Connection to Development Career</h5>
              <p>The discipline, consistency, and problem-solving mindset developed through fitness training directly translates to my approach in software development - maintaining focus on long-term goals, systematic improvement, and resilience when facing challenges.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Leadership & Other Qualifications -->
    <div class="timeline-section">
      <h3 class="timeline-title">Leadership Experience</h3>
      <div class="timeline">
        <div class="timeline-item" *ngFor="let leader of leadership">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <h4>{{ leader.title }}</h4>
            <p>{{ leader.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="timeline-section">
      <h3 class="timeline-title">Other Qualifications</h3>
      <div class="qualifications-grid">
        <div class="qualification-category" *ngFor="let qual of otherQualifications">
          <h4 class="category-name">{{ qual.category }}</h4>
          <ul class="qualification-list">
            <li *ngFor="let item of qual.items">{{ item }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
