import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PortfolioService, PersonalInfo, SocialLink } from '../../services/portfolio.service';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.scss'
})
export class FooterComponent {
  personalInfo: PersonalInfo;
  socialLinks: SocialLink[];
  currentYear = new Date().getFullYear();

  constructor(private portfolioService: PortfolioService) {
    this.personalInfo = this.portfolioService.getPersonalInfo();
    this.socialLinks = this.portfolioService.getSocialLinks();
  }

  scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
