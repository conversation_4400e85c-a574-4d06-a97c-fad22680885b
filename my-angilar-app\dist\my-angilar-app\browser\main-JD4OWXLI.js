var Zv=Object.defineProperty,Qv=Object.defineProperties;var Jv=Object.getOwnPropertyDescriptors;var Od=Object.getOwnPropertySymbols;var Kv=Object.prototype.hasOwnProperty,Xv=Object.prototype.propertyIsEnumerable;var Pd=(e,t,n)=>t in e?Zv(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,M=(e,t)=>{for(var n in t||={})Kv.call(t,n)&&Pd(e,n,t[n]);if(Od)for(var n of Od(t))Xv.call(t,n)&&Pd(e,n,t[n]);return e},U=(e,t)=>Qv(e,Jv(t));function Ya(e,t){return Object.is(e,t)}var de=null,Zo=!1,Za=1,Ve=Symbol("SIGNAL");function B(e){let t=de;return de=e,t}function Qa(){return de}var Vr={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Ur(e){if(Zo)throw new Error("");if(de===null)return;de.consumerOnSignalRead(e);let t=de.nextProducerIndex++;if(Xo(de),t<de.producerNode.length&&de.producerNode[t]!==e&&jr(de)){let n=de.producerNode[t];Ko(n,de.producerIndexOfThis[t])}de.producerNode[t]!==e&&(de.producerNode[t]=e,de.producerIndexOfThis[t]=jr(de)?Ad(e,de,t):0),de.producerLastReadVersion[t]=e.version}function Td(){Za++}function Ja(e){if(!(jr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Za)){if(!e.producerMustRecompute(e)&&!tc(e)){Wa(e);return}e.producerRecomputeValue(e),Wa(e)}}function Ka(e){if(e.liveConsumerNode===void 0)return;let t=Zo;Zo=!0;try{for(let n of e.liveConsumerNode)n.dirty||ey(n)}finally{Zo=t}}function Xa(){return de?.consumerAllowSignalWrites!==!1}function ey(e){e.dirty=!0,Ka(e),e.consumerMarkedDirty?.(e)}function Wa(e){e.dirty=!1,e.lastCleanEpoch=Za}function Jo(e){return e&&(e.nextProducerIndex=0),B(e)}function ec(e,t){if(B(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(jr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Ko(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function tc(e){Xo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ja(n),r!==n.version))return!0}return!1}function nc(e){if(Xo(e),jr(e))for(let t=0;t<e.producerNode.length;t++)Ko(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Ad(e,t,n){if(Nd(e),e.liveConsumerNode.length===0&&Rd(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Ad(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Ko(e,t){if(Nd(e),e.liveConsumerNode.length===1&&Rd(e))for(let r=0;r<e.producerNode.length;r++)Ko(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Xo(o),o.producerIndexOfThis[r]=t}}function jr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Xo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Nd(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Rd(e){return e.producerNode!==void 0}function rc(e,t){let n=Object.create(ty);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Ja(n),Ur(n),n.value===Qo)throw n.error;return n.value};return r[Ve]=n,r}var Ga=Symbol("UNSET"),qa=Symbol("COMPUTING"),Qo=Symbol("ERRORED"),ty=U(M({},Vr),{value:Ga,dirty:!0,error:null,equal:Ya,kind:"computed",producerMustRecompute(e){return e.value===Ga||e.value===qa},producerRecomputeValue(e){if(e.value===qa)throw new Error("Detected cycle in computations.");let t=e.value;e.value=qa;let n=Jo(e),r,o=!1;try{r=e.computation(),B(null),o=t!==Ga&&t!==Qo&&r!==Qo&&e.equal(t,r)}catch(i){r=Qo,e.error=i}finally{ec(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function ny(){throw new Error}var kd=ny;function Fd(e){kd(e)}function oc(e){kd=e}var ry=null;function ic(e,t){let n=Object.create(ei);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(Ur(n),n.value);return r[Ve]=n,r}function Br(e,t){Xa()||Fd(e),e.equal(e.value,t)||(e.value=t,oy(e))}function sc(e,t){Xa()||Fd(e),Br(e,t(e.value))}var ei=U(M({},Vr),{equal:Ya,value:void 0,kind:"signal"});function oy(e){e.version++,Td(),Ka(e),ry?.()}function ac(e){let t=B(null);try{return e()}finally{B(t)}}var cc;function Hr(){return cc}function _t(e){let t=cc;return cc=e,t}var ti=Symbol("NotFound");function R(e){return typeof e=="function"}function Fn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var ni=Fn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function en(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var X=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(R(r))try{r()}catch(i){t=i instanceof ni?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Ld(i)}catch(s){t=t??[],s instanceof ni?t=[...t,...s.errors]:t.push(s)}}if(t)throw new ni(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Ld(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&en(n,t)}remove(t){let{_finalizers:n}=this;n&&en(n,t),t instanceof e&&t._removeParent(this)}};X.EMPTY=(()=>{let e=new X;return e.closed=!0,e})();var lc=X.EMPTY;function ri(e){return e instanceof X||e&&"closed"in e&&R(e.remove)&&R(e.add)&&R(e.unsubscribe)}function Ld(e){R(e)?e():e.unsubscribe()}var We={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Ln={setTimeout(e,t,...n){let{delegate:r}=Ln;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Ln;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function oi(e){Ln.setTimeout(()=>{let{onUnhandledError:t}=We;if(t)t(e);else throw e})}function $r(){}var jd=uc("C",void 0,void 0);function Vd(e){return uc("E",void 0,e)}function Ud(e){return uc("N",e,void 0)}function uc(e,t,n){return{kind:e,value:t,error:n}}var tn=null;function jn(e){if(We.useDeprecatedSynchronousErrorHandling){let t=!tn;if(t&&(tn={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=tn;if(tn=null,n)throw r}}else e()}function Bd(e){We.useDeprecatedSynchronousErrorHandling&&tn&&(tn.errorThrown=!0,tn.error=e)}var nn=class extends X{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,ri(t)&&t.add(this)):this.destination=uy}static create(t,n,r){return new Vn(t,n,r)}next(t){this.isStopped?fc(Ud(t),this):this._next(t)}error(t){this.isStopped?fc(Vd(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?fc(jd,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},cy=Function.prototype.bind;function dc(e,t){return cy.call(e,t)}var pc=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){ii(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){ii(r)}else ii(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){ii(n)}}},Vn=class extends nn{constructor(t,n,r){super();let o;if(R(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&We.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&dc(t.next,i),error:t.error&&dc(t.error,i),complete:t.complete&&dc(t.complete,i)}):o=t}this.destination=new pc(o)}};function ii(e){We.useDeprecatedSynchronousErrorHandling?Bd(e):oi(e)}function ly(e){throw e}function fc(e,t){let{onStoppedNotification:n}=We;n&&Ln.setTimeout(()=>n(e,t))}var uy={closed:!0,next:$r,error:ly,complete:$r};var Un=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Ce(e){return e}function hc(...e){return gc(e)}function gc(e){return e.length===0?Ce:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var $=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=fy(n)?n:new Vn(n,r,o);return jn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Hd(r),new r((o,i)=>{let s=new Vn({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Un](){return this}pipe(...n){return gc(n)(this)}toPromise(n){return n=Hd(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Hd(e){var t;return(t=e??We.Promise)!==null&&t!==void 0?t:Promise}function dy(e){return e&&R(e.next)&&R(e.error)&&R(e.complete)}function fy(e){return e&&e instanceof nn||dy(e)&&ri(e)}function mc(e){return R(e?.lift)}function H(e){return t=>{if(mc(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function V(e,t,n,r,o){return new vc(e,t,n,r,o)}var vc=class extends nn{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Bn(){return H((e,t)=>{let n=null;e._refCount++;let r=V(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Hn=class extends ${constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,mc(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new X;let n=this.getSubject();t.add(this.source.subscribe(V(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=X.EMPTY)}return t}refCount(){return Bn()(this)}};var $d=Fn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var re=(()=>{class e extends ${constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new si(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new $d}next(n){jn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){jn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){jn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?lc:(this.currentObservers=null,i.push(n),new X(()=>{this.currentObservers=null,en(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new $;return n.source=this,n}}return e.create=(t,n)=>new si(t,n),e})(),si=class extends re{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:lc}};var me=class extends re{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var yc={now(){return(yc.delegate||Date).now()},delegate:void 0};var ai=class extends X{constructor(t,n){super()}schedule(t,n=0){return this}};var zr={setInterval(e,t,...n){let{delegate:r}=zr;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=zr;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var ci=class extends ai{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return zr.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&zr.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,en(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var $n=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};$n.now=yc.now;var li=class extends $n{constructor(t,n=$n.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var py=new li(ci),zd=py;var Ee=new $(e=>e.complete());function ui(e){return e&&R(e.schedule)}function Gd(e){return e[e.length-1]}function di(e){return R(Gd(e))?e.pop():void 0}function Lt(e){return ui(Gd(e))?e.pop():void 0}function Wd(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(h){s(h)}}function c(u){try{l(r.throw(u))}catch(h){s(h)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function qd(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function rn(e){return this instanceof rn?(this.v=e,this):new rn(e)}function Yd(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(p){return function(_){return Promise.resolve(_).then(p,h)}}function a(p,_){r[p]&&(o[p]=function(w){return new Promise(function(T,G){i.push([p,w,T,G])>1||c(p,w)})},_&&(o[p]=_(o[p])))}function c(p,_){try{l(r[p](_))}catch(w){v(i[0][3],w)}}function l(p){p.value instanceof rn?Promise.resolve(p.value.v).then(u,h):v(i[0][2],p)}function u(p){c("next",p)}function h(p){c("throw",p)}function v(p,_){p(_),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Zd(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof qd=="function"?qd(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var fi=e=>e&&typeof e.length=="number"&&typeof e!="function";function pi(e){return R(e?.then)}function hi(e){return R(e[Un])}function gi(e){return Symbol.asyncIterator&&R(e?.[Symbol.asyncIterator])}function mi(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function hy(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var vi=hy();function yi(e){return R(e?.[vi])}function _i(e){return Yd(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield rn(n.read());if(o)return yield rn(void 0);yield yield rn(r)}}finally{n.releaseLock()}})}function Ci(e){return R(e?.getReader)}function ee(e){if(e instanceof $)return e;if(e!=null){if(hi(e))return gy(e);if(fi(e))return my(e);if(pi(e))return vy(e);if(gi(e))return Qd(e);if(yi(e))return yy(e);if(Ci(e))return _y(e)}throw mi(e)}function gy(e){return new $(t=>{let n=e[Un]();if(R(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function my(e){return new $(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function vy(e){return new $(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,oi)})}function yy(e){return new $(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Qd(e){return new $(t=>{Cy(e,t).catch(n=>t.error(n))})}function _y(e){return Qd(_i(e))}function Cy(e,t){var n,r,o,i;return Wd(this,void 0,void 0,function*(){try{for(n=Zd(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Ie(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function bi(e,t=0){return H((n,r)=>{n.subscribe(V(r,o=>Ie(r,e,()=>r.next(o),t),()=>Ie(r,e,()=>r.complete(),t),o=>Ie(r,e,()=>r.error(o),t)))})}function Mi(e,t=0){return H((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Jd(e,t){return ee(e).pipe(Mi(t),bi(t))}function Kd(e,t){return ee(e).pipe(Mi(t),bi(t))}function Xd(e,t){return new $(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function ef(e,t){return new $(n=>{let r;return Ie(n,t,()=>{r=e[vi](),Ie(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>R(r?.return)&&r.return()})}function Di(e,t){if(!e)throw new Error("Iterable cannot be null");return new $(n=>{Ie(n,t,()=>{let r=e[Symbol.asyncIterator]();Ie(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function tf(e,t){return Di(_i(e),t)}function nf(e,t){if(e!=null){if(hi(e))return Jd(e,t);if(fi(e))return Xd(e,t);if(pi(e))return Kd(e,t);if(gi(e))return Di(e,t);if(yi(e))return ef(e,t);if(Ci(e))return tf(e,t)}throw mi(e)}function K(e,t){return t?nf(e,t):ee(e)}function O(...e){let t=Lt(e);return K(e,t)}function zn(e,t){let n=R(e)?e:()=>e,r=o=>o.error(n());return new $(t?o=>t.schedule(r,0,o):r)}function _c(e){return!!e&&(e instanceof $||R(e.lift)&&R(e.subscribe))}var Ct=Fn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function rf(e){return e instanceof Date&&!isNaN(e)}function A(e,t){return H((n,r)=>{let o=0;n.subscribe(V(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:by}=Array;function My(e,t){return by(t)?e(...t):e(t)}function wi(e){return A(t=>My(e,t))}var{isArray:Dy}=Array,{getPrototypeOf:wy,prototype:Ey,keys:Iy}=Object;function Ei(e){if(e.length===1){let t=e[0];if(Dy(t))return{args:t,keys:null};if(xy(t)){let n=Iy(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function xy(e){return e&&typeof e=="object"&&wy(e)===Ey}function Ii(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function on(...e){let t=Lt(e),n=di(e),{args:r,keys:o}=Ei(e);if(r.length===0)return K([],t);let i=new $(Sy(r,t,o?s=>Ii(o,s):Ce));return n?i.pipe(wi(n)):i}function Sy(e,t,n=Ce){return r=>{of(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)of(t,()=>{let l=K(e[c],t),u=!1;l.subscribe(V(r,h=>{i[c]=h,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function of(e,t,n){e?Ie(n,e,t):t()}function sf(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,h=!1,v=()=>{h&&!c.length&&!l&&t.complete()},p=w=>l<r?_(w):c.push(w),_=w=>{i&&t.next(w),l++;let T=!1;ee(n(w,u++)).subscribe(V(t,G=>{o?.(G),i?p(G):t.next(G)},()=>{T=!0},void 0,()=>{if(T)try{for(l--;c.length&&l<r;){let G=c.shift();s?Ie(t,s,()=>_(G)):_(G)}v()}catch(G){t.error(G)}}))};return e.subscribe(V(t,p,()=>{h=!0,v()})),()=>{a?.()}}function oe(e,t,n=1/0){return R(t)?oe((r,o)=>A((i,s)=>t(r,i,o,s))(ee(e(r,o))),n):(typeof t=="number"&&(n=t),H((r,o)=>sf(r,o,e,n)))}function Gn(e=1/0){return oe(Ce,e)}function af(){return Gn(1)}function qn(...e){return af()(K(e,Lt(e)))}function xi(e){return new $(t=>{ee(e()).subscribe(t)})}function Cc(...e){let t=di(e),{args:n,keys:r}=Ei(e),o=new $(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let h=!1;ee(n[u]).subscribe(V(i,v=>{h||(h=!0,l--),a[u]=v},()=>c--,void 0,()=>{(!c||!h)&&(l||i.next(r?Ii(r,a):a),i.complete())}))}});return t?o.pipe(wi(t)):o}function cf(e=0,t,n=zd){let r=-1;return t!=null&&(ui(t)?n=t:r=t),new $(o=>{let i=rf(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function be(e,t){return H((n,r)=>{let o=0;n.subscribe(V(r,i=>e.call(t,i,o++)&&r.next(i)))})}function ke(e){return H((t,n)=>{let r=null,o=!1,i;r=t.subscribe(V(n,void 0,void 0,s=>{i=ee(e(s,ke(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function lf(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(V(s,u=>{let h=l++;c=a?e(c,u,h):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function it(e,t){return R(t)?oe(e,t,1):oe(e,1)}function jt(e){return H((t,n)=>{let r=!1;t.subscribe(V(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function bt(e){return e<=0?()=>Ee:H((t,n)=>{let r=0;t.subscribe(V(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Si(e=Oy){return H((t,n)=>{let r=!1;t.subscribe(V(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Oy(){return new Ct}function Vt(e){return H((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Mt(e,t){let n=arguments.length>=2;return r=>r.pipe(e?be((o,i)=>e(o,i,r)):Ce,bt(1),n?jt(t):Si(()=>new Ct))}function Wn(e){return e<=0?()=>Ee:H((t,n)=>{let r=[];t.subscribe(V(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function bc(e,t){let n=arguments.length>=2;return r=>r.pipe(e?be((o,i)=>e(o,i,r)):Ce,Wn(1),n?jt(t):Si(()=>new Ct))}function Mc(e=1/0){let t;e&&typeof e=="object"?t=e:t={count:e};let{count:n=1/0,delay:r,resetOnSuccess:o=!1}=t;return n<=0?Ce:H((i,s)=>{let a=0,c,l=()=>{let u=!1;c=i.subscribe(V(s,h=>{o&&(a=0),s.next(h)},void 0,h=>{if(a++<n){let v=()=>{c?(c.unsubscribe(),c=null,l()):u=!0};if(r!=null){let p=typeof r=="number"?cf(r):ee(r(h,a)),_=V(s,()=>{_.unsubscribe(),v()},()=>{s.complete()});p.subscribe(_)}else v()}else s.error(h)})),u&&(c.unsubscribe(),c=null,l())};l()})}function Dc(e,t){return H(lf(e,t,arguments.length>=2,!0))}function wc(...e){let t=Lt(e);return H((n,r)=>{(t?qn(e,n,t):qn(e,n)).subscribe(r)})}function xe(e,t){return H((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(V(r,c=>{o?.unsubscribe();let l=0,u=i++;ee(e(c,u)).subscribe(o=V(r,h=>r.next(t?t(c,h,u,l++):h),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Gr(e){return H((t,n)=>{ee(e).subscribe(V(n,()=>n.complete(),$r)),!n.closed&&t.subscribe(n)})}function ve(e,t,n){let r=R(e)||t||n?{next:e,error:t,complete:n}:e;return r?H((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(V(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):Ce}var Jf="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",E=class extends Error{code;constructor(t,n){super(Kf(t,n)),this.code=t}};function Py(e){return`NG0${Math.abs(e)}`}function Kf(e,t){return`${Py(e)}${t?": "+t:""}`}var Xf=Symbol("InputSignalNode#UNSET"),Ty=U(M({},ei),{transformFn:void 0,applyValueToInputSignal(e,t){Br(e,t)}});function ep(e,t){let n=Object.create(Ty);n.value=e,n.transformFn=t?.transform;function r(){if(Ur(n),n.value===Xf){let o=null;throw new E(-950,o)}return n.value}return r[Ve]=n,r}function no(e){return{toString:e}.toString()}var Oi="__parameters__";function Ay(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function tp(e,t,n){return no(()=>{let r=Ay(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let h=c.hasOwnProperty(Oi)?c[Oi]:Object.defineProperty(c,Oi,{value:[]})[Oi];for(;h.length<=u;)h.push(null);return(h[u]=h[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}function J(e){for(let t in e)if(e[t]===J)return t;throw Error("Could not find renamed property on target object.")}function Ny(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Oe(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Oe).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function jc(e,t){return e?t?`${e} ${t}`:e:t||""}var Ry=J({__forward_ref__:J});function It(e){return e.__forward_ref__=It,e.toString=function(){return Oe(this())},e}function Me(e){return np(e)?e():e}function np(e){return typeof e=="function"&&e.hasOwnProperty(Ry)&&e.__forward_ref__===It}function I(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Je(e){return{providers:e.providers||[],imports:e.imports||[]}}function cs(e){return uf(e,op)||uf(e,ip)}function rp(e){return cs(e)!==null}function uf(e,t){return e.hasOwnProperty(t)?e[t]:null}function ky(e){let t=e&&(e[op]||e[ip]);return t||null}function df(e){return e&&(e.hasOwnProperty(ff)||e.hasOwnProperty(Fy))?e[ff]:null}var op=J({\u0275prov:J}),ff=J({\u0275inj:J}),ip=J({ngInjectableDef:J}),Fy=J({ngInjectorDef:J}),D=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=I({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function sp(e){return e&&!!e.\u0275providers}var Ly=J({\u0275cmp:J}),jy=J({\u0275dir:J}),Vy=J({\u0275pipe:J}),Uy=J({\u0275mod:J}),Fi=J({\u0275fac:J}),Zr=J({__NG_ELEMENT_ID__:J}),pf=J({__NG_ENV_ID__:J});function er(e){return typeof e=="string"?e:e==null?"":String(e)}function By(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():er(e)}function ap(e,t){throw new E(-200,e)}function xl(e,t){throw new E(-201,!1)}var L=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(L||{}),Vc;function cp(){return Vc}function Ue(e){let t=Vc;return Vc=e,t}function lp(e,t,n){let r=cs(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&L.Optional)return null;if(t!==void 0)return t;xl(e,"Injector")}var Hy={},sn=Hy,Uc="__NG_DI_FLAG__",Li=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?ti:sn,r)}},ji="ngTempTokenPath",$y="ngTokenPath",zy=/\n/gm,Gy="\u0275",hf="__source";function qy(e,t=L.Default){if(Hr()===void 0)throw new E(-203,!1);if(Hr()===null)return lp(e,void 0,t);{let n=Hr(),r;return n instanceof Li?r=n.injector:r=n,r.get(e,t&L.Optional?null:void 0,t)}}function x(e,t=L.Default){return(cp()||qy)(Me(e),t)}function y(e,t=L.Default){return x(e,ls(t))}function ls(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Bc(e){let t=[];for(let n=0;n<e.length;n++){let r=Me(e[n]);if(Array.isArray(r)){if(r.length===0)throw new E(900,!1);let o,i=L.Default;for(let s=0;s<r.length;s++){let a=r[s],c=Wy(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(x(o,i))}else t.push(x(r))}return t}function up(e,t){return e[Uc]=t,e.prototype[Uc]=t,e}function Wy(e){return e[Uc]}function Yy(e,t,n,r){let o=e[ji];throw t[hf]&&o.unshift(t[hf]),e.message=Zy(`
`+e.message,o,n,r),e[$y]=o,e[ji]=null,e}function Zy(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Gy?e.slice(2):e;let o=Oe(t);if(Array.isArray(t))o=t.map(Oe).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Oe(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(zy,`
  `)}`}var Qy=up(tp("Optional"),8);var Jy=up(tp("SkipSelf"),4);function tr(e,t){let n=e.hasOwnProperty(Fi);return n?e[Fi]:null}function Sl(e,t){e.forEach(n=>Array.isArray(n)?Sl(n,t):t(n))}function dp(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Vi(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Ky(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function Ol(e,t,n){let r=ro(e,t);return r>=0?e[r|1]=n:(r=~r,Ky(e,r,t,n)),r}function Ec(e,t){let n=ro(e,t);if(n>=0)return e[n|1]}function ro(e,t){return Xy(e,t,1)}function Xy(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var cn={},Se=[],Qr=new D(""),fp=new D("",-1),pp=new D(""),Ui=class{get(t,n=sn){if(n===sn){let r=new Error(`NullInjectorError: No provider for ${Oe(t)}!`);throw r.name="NullInjectorError",r}return n}};function hp(e,t){let n=e[Uy]||null;if(!n&&t===!0)throw new Error(`Type ${Oe(e)} does not have '\u0275mod' property.`);return n}function ln(e){return e[Ly]||null}function e0(e){return e[jy]||null}function t0(e){return e[Vy]||null}function dr(e){return{\u0275providers:e}}function n0(...e){return{\u0275providers:gp(!0,e),\u0275fromNgModule:!0}}function gp(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Sl(t,s=>{let a=s;Hc(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&mp(o,i),n}function mp(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Pl(o,i=>{t(i,r)})}}function Hc(e,t,n,r){if(e=Me(e),!e)return!1;let o=null,i=df(e),s=!i&&ln(e);if(!i&&!s){let c=e.ngModule;if(i=df(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Hc(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{Sl(i.imports,u=>{Hc(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&mp(l,t)}if(!a){let l=tr(o)||(()=>new o);t({provide:o,useFactory:l,deps:Se},o),t({provide:pp,useValue:o,multi:!0},o),t({provide:Qr,useValue:()=>x(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;Pl(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function Pl(e,t){for(let n of e)sp(n)&&(n=n.\u0275providers),Array.isArray(n)?Pl(n,t):t(n)}var r0=J({provide:String,useValue:J});function vp(e){return e!==null&&typeof e=="object"&&r0 in e}function o0(e){return!!(e&&e.useExisting)}function i0(e){return!!(e&&e.useFactory)}function nr(e){return typeof e=="function"}function s0(e){return!!e.useClass}var us=new D(""),Ti={},gf={},Ic;function Tl(){return Ic===void 0&&(Ic=new Ui),Ic}var ye=class{},Jr=class extends ye{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,zc(t,s=>this.processProvider(s)),this.records.set(fp,Yn(void 0,this)),o.has("environment")&&this.records.set(ye,Yn(void 0,this));let i=this.records.get(us);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(pp,Se,L.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?ti:sn,r)}destroy(){Wr(this),this._destroyed=!0;let t=B(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),B(t)}}onDestroy(t){return Wr(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Wr(this);let n=_t(this),r=Ue(void 0),o;try{return t()}finally{_t(n),Ue(r)}}get(t,n=sn,r=L.Default){if(Wr(this),t.hasOwnProperty(pf))return t[pf](this);r=ls(r);let o,i=_t(this),s=Ue(void 0);try{if(!(r&L.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=d0(t)&&cs(t);l&&this.injectableDefInScope(l)?c=Yn($c(t),Ti):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&L.Self?Tl():this.parent;return n=r&L.Optional&&n===sn?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[ji]=a[ji]||[]).unshift(Oe(t)),i)throw a;return Yy(a,t,"R3InjectorError",this.source)}else throw a}finally{Ue(s),_t(i)}}resolveInjectorInitializers(){let t=B(null),n=_t(this),r=Ue(void 0),o;try{let i=this.get(Qr,Se,L.Self);for(let s of i)s()}finally{_t(n),Ue(r),B(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Oe(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=Me(t);let n=nr(t)?t:Me(t&&t.provide),r=c0(t);if(!nr(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Yn(void 0,Ti,!0),o.factory=()=>Bc(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=B(null);try{return n.value===gf?ap(Oe(t)):n.value===Ti&&(n.value=gf,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&u0(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{B(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=Me(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function $c(e){let t=cs(e),n=t!==null?t.factory:tr(e);if(n!==null)return n;if(e instanceof D)throw new E(204,!1);if(e instanceof Function)return a0(e);throw new E(204,!1)}function a0(e){if(e.length>0)throw new E(204,!1);let n=ky(e);return n!==null?()=>n.factory(e):()=>new e}function c0(e){if(vp(e))return Yn(void 0,e.useValue);{let t=yp(e);return Yn(t,Ti)}}function yp(e,t,n){let r;if(nr(e)){let o=Me(e);return tr(o)||$c(o)}else if(vp(e))r=()=>Me(e.useValue);else if(i0(e))r=()=>e.useFactory(...Bc(e.deps||[]));else if(o0(e))r=(o,i)=>x(Me(e.useExisting),i!==void 0&&i&L.Optional?L.Optional:void 0);else{let o=Me(e&&(e.useClass||e.provide));if(l0(e))r=()=>new o(...Bc(e.deps));else return tr(o)||$c(o)}return r}function Wr(e){if(e.destroyed)throw new E(205,!1)}function Yn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function l0(e){return!!e.deps}function u0(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function d0(e){return typeof e=="function"||typeof e=="object"&&e instanceof D}function zc(e,t){for(let n of e)Array.isArray(n)?zc(n,t):n&&sp(n)?zc(n.\u0275providers,t):t(n)}function Te(e,t){let n;e instanceof Jr?(Wr(e),n=e):n=new Li(e);let r,o=_t(n),i=Ue(void 0);try{return t()}finally{_t(o),Ue(i)}}function _p(){return cp()!==void 0||Hr()!=null}function f0(e){if(!_p())throw new E(-203,!1)}function p0(e){return typeof e=="function"}var xt=0,z=1,k=2,De=3,Qe=4,Ke=5,Bi=6,Hi=7,Pe=8,rr=9,Dt=10,ie=11,Kr=12,mf=13,fr=14,at=15,or=16,Zn=17,ir=18,ds=19,Cp=20,Ut=21,xc=22,$i=23,Be=24,Kn=25,wt=26,bp=1;var un=7,zi=8,Gi=9,He=10;function Bt(e){return Array.isArray(e)&&typeof e[bp]=="object"}function St(e){return Array.isArray(e)&&e[bp]===!0}function Mp(e){return(e.flags&4)!==0}function pr(e){return e.componentOffset>-1}function Al(e){return(e.flags&1)===1}function ct(e){return!!e.template}function qi(e){return(e[k]&512)!==0}function hr(e){return(e[k]&256)===256}var Gc=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Dp(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var $t=(()=>{let e=()=>wp;return e.ngInherit=!0,e})();function wp(e){return e.type.prototype.ngOnChanges&&(e.setInput=g0),h0}function h0(){let e=Ip(this),t=e?.current;if(t){let n=e.previous;if(n===cn)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function g0(e,t,n,r,o){let i=this.declaredInputs[r],s=Ip(e)||m0(e,{previous:cn,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Gc(l&&l.currentValue,n,c===cn),Dp(e,t,o,n)}var Ep="__ngSimpleChanges__";function Ip(e){return e[Ep]||null}function m0(e,t){return e[Ep]=t}var vf=null;var Z=function(e,t=null,n){vf?.(e,t,n)},xp="svg",v0="math";function lt(e){for(;Array.isArray(e);)e=e[xt];return e}function Sp(e,t){return lt(t[e])}function pt(e,t){return lt(t[e.index])}function Op(e,t){return e.data[t]}function ut(e,t){let n=t[e];return Bt(n)?n:n[xt]}function Nl(e){return(e[k]&128)===128}function y0(e){return St(e[De])}function Wi(e,t){return t==null?null:e[t]}function Pp(e){e[Zn]=0}function Tp(e){e[k]&1024||(e[k]|=1024,Nl(e)&&oo(e))}function _0(e,t){for(;e>0;)t=t[fr],e--;return t}function fs(e){return!!(e[k]&9216||e[Be]?.dirty)}function qc(e){e[Dt].changeDetectionScheduler?.notify(8),e[k]&64&&(e[k]|=1024),fs(e)&&oo(e)}function oo(e){e[Dt].changeDetectionScheduler?.notify(0);let t=dn(e);for(;t!==null&&!(t[k]&8192||(t[k]|=8192,!Nl(t)));)t=dn(t)}function Ap(e,t){if(hr(e))throw new E(911,!1);e[Ut]===null&&(e[Ut]=[]),e[Ut].push(t)}function C0(e,t){if(e[Ut]===null)return;let n=e[Ut].indexOf(t);n!==-1&&e[Ut].splice(n,1)}function dn(e){let t=e[De];return St(t)?t[De]:t}function Np(e){return e[Hi]??=[]}function Rp(e){return e.cleanup??=[]}var j={lFrame:Hp(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Wc=!1;function b0(){return j.lFrame.elementDepthCount}function M0(){j.lFrame.elementDepthCount++}function D0(){j.lFrame.elementDepthCount--}function kp(){return j.bindingsEnabled}function w0(){return j.skipHydrationRootTNode!==null}function E0(e){return j.skipHydrationRootTNode===e}function I0(){j.skipHydrationRootTNode=null}function Y(){return j.lFrame.lView}function we(){return j.lFrame.tView}function ps(e){return j.lFrame.contextLView=e,e[Pe]}function hs(e){return j.lFrame.contextLView=null,e}function Fe(){let e=Fp();for(;e!==null&&e.type===64;)e=e.parent;return e}function Fp(){return j.lFrame.currentTNode}function x0(){let e=j.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function io(e,t){let n=j.lFrame;n.currentTNode=e,n.isParent=t}function Lp(){return j.lFrame.isParent}function S0(){j.lFrame.isParent=!1}function jp(){return Wc}function yf(e){let t=Wc;return Wc=e,t}function O0(){let e=j.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function P0(){return j.lFrame.bindingIndex}function T0(e){return j.lFrame.bindingIndex=e}function gs(){return j.lFrame.bindingIndex++}function Rl(e){let t=j.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function A0(){return j.lFrame.inI18n}function N0(e,t){let n=j.lFrame;n.bindingIndex=n.bindingRootIndex=e,Yc(t)}function R0(){return j.lFrame.currentDirectiveIndex}function Yc(e){j.lFrame.currentDirectiveIndex=e}function k0(e){let t=j.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Vp(e){j.lFrame.currentQueryIndex=e}function F0(e){let t=e[z];return t.type===2?t.declTNode:t.type===1?e[Ke]:null}function Up(e,t,n){if(n&L.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&L.Host);)if(o=F0(i),o===null||(i=i[fr],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=j.lFrame=Bp();return r.currentTNode=t,r.lView=e,!0}function kl(e){let t=Bp(),n=e[z];j.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Bp(){let e=j.lFrame,t=e===null?null:e.child;return t===null?Hp(e):t}function Hp(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function $p(){let e=j.lFrame;return j.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var zp=$p;function Fl(){let e=$p();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function L0(e){return(j.lFrame.contextLView=_0(e,j.lFrame.contextLView))[Pe]}function zt(){return j.lFrame.selectedIndex}function fn(e){j.lFrame.selectedIndex=e}function ms(){let e=j.lFrame;return Op(e.tView,e.selectedIndex)}function so(){j.lFrame.currentNamespace=xp}function Ll(){j0()}function j0(){j.lFrame.currentNamespace=null}function V0(){return j.lFrame.currentNamespace}var Gp=!0;function jl(){return Gp}function Vl(e){Gp=e}function U0(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=wp(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function qp(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function Ai(e,t,n){Wp(e,t,3,n)}function Ni(e,t,n,r){(e[k]&3)===n&&Wp(e,t,n,r)}function Sc(e,t){let n=e[k];(n&3)===t&&(n&=16383,n+=1,e[k]=n)}function Wp(e,t,n,r){let o=r!==void 0?e[Zn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Zn]+=65536),(a<i||i==-1)&&(B0(e,n,t,c),e[Zn]=(e[Zn]&**********)+c+2),c++}function _f(e,t){Z(4,e,t);let n=B(null);try{t.call(e)}finally{B(n),Z(5,e,t)}}function B0(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[k]>>14<e[Zn]>>16&&(e[k]&3)===t&&(e[k]+=16384,_f(a,i)):_f(a,i)}var Xn=-1,pn=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function H0(e){return(e.flags&8)!==0}function $0(e){return(e.flags&16)!==0}function z0(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];q0(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function G0(e){return e===3||e===4||e===6}function q0(e){return e.charCodeAt(0)===64}function Xr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Cf(e,n,o,null,t[++r]):Cf(e,n,o,null,null))}}return e}function Cf(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function Yp(e){return e!==Xn}function Yi(e){return e&32767}function W0(e){return e>>16}function Zi(e,t){let n=W0(e),r=t;for(;n>0;)r=r[fr],n--;return r}var Zc=!0;function bf(e){let t=Zc;return Zc=e,t}var Y0=256,Zp=Y0-1,Qp=5,Z0=0,st={};function Q0(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Zr)&&(r=n[Zr]),r==null&&(r=n[Zr]=Z0++);let o=r&Zp,i=1<<o;t.data[e+(o>>Qp)]|=i}function Qi(e,t){let n=Jp(e,t);if(n!==-1)return n;let r=t[z];r.firstCreatePass&&(e.injectorIndex=t.length,Oc(r.data,e),Oc(t,null),Oc(r.blueprint,null));let o=Ul(e,t),i=e.injectorIndex;if(Yp(o)){let s=Yi(o),a=Zi(o,t),c=a[z].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Oc(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Jp(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Ul(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=nh(o),r===null)return Xn;if(n++,o=o[fr],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Xn}function Qc(e,t,n){Q0(e,t,n)}function Kp(e,t,n){if(n&L.Optional||e!==void 0)return e;xl(t,"NodeInjector")}function Xp(e,t,n,r){if(n&L.Optional&&r===void 0&&(r=null),(n&(L.Self|L.Host))===0){let o=e[rr],i=Ue(void 0);try{return o?o.get(t,r,n&L.Optional):lp(t,r,n&L.Optional)}finally{Ue(i)}}return Kp(r,t,n)}function eh(e,t,n,r=L.Default,o){if(e!==null){if(t[k]&2048&&!(r&L.Self)){let s=t_(e,t,n,r,st);if(s!==st)return s}let i=th(e,t,n,r,st);if(i!==st)return i}return Xp(t,n,r,o)}function th(e,t,n,r,o){let i=X0(n);if(typeof i=="function"){if(!Up(t,e,r))return r&L.Host?Kp(o,n,r):Xp(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&L.Optional))xl(n);else return s}finally{zp()}}else if(typeof i=="number"){let s=null,a=Jp(e,t),c=Xn,l=r&L.Host?t[at][Ke]:null;for((a===-1||r&L.SkipSelf)&&(c=a===-1?Ul(e,t):t[a+8],c===Xn||!Df(r,!1)?a=-1:(s=t[z],a=Yi(c),t=Zi(c,t)));a!==-1;){let u=t[z];if(Mf(i,a,u.data)){let h=J0(a,t,n,s,r,l);if(h!==st)return h}c=t[a+8],c!==Xn&&Df(r,t[z].data[a+8]===l)&&Mf(i,a,t)?(s=u,a=Yi(c),t=Zi(c,t)):a=-1}}return o}function J0(e,t,n,r,o,i){let s=t[z],a=s.data[e+8],c=r==null?pr(a)&&Zc:r!=s&&(a.type&3)!==0,l=o&L.Host&&i===a,u=K0(a,s,n,c,l);return u!==null?Ji(t,s,u,a,o):st}function K0(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,h=r?a:a+u,v=o?a+u:l;for(let p=h;p<v;p++){let _=s[p];if(p<c&&n===_||p>=c&&_.type===n)return p}if(o){let p=s[c];if(p&&ct(p)&&p.type===n)return c}return null}function Ji(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof pn){let a=i;a.resolving&&ap(By(s[n]));let c=bf(a.canSeeViewProviders);a.resolving=!0;let l,u=a.injectImpl?Ue(a.injectImpl):null,h=Up(e,r,L.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&U0(n,s[n],t)}finally{u!==null&&Ue(u),bf(c),a.resolving=!1,zp()}}return i}function X0(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Zr)?e[Zr]:void 0;return typeof t=="number"?t>=0?t&Zp:e_:t}function Mf(e,t,n){let r=1<<e;return!!(n[t+(e>>Qp)]&r)}function Df(e,t){return!(e&L.Self)&&!(e&L.Host&&t)}var an=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return eh(this._tNode,this._lView,t,ls(r),n)}};function e_(){return new an(Fe(),Y())}function mn(e){return no(()=>{let t=e.prototype.constructor,n=t[Fi]||Jc(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Fi]||Jc(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Jc(e){return np(e)?()=>{let t=Jc(Me(e));return t&&t()}:tr(e)}function t_(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[k]&2048&&!qi(s);){let a=th(i,s,n,r|L.Self,st);if(a!==st)return a;let c=i.parent;if(!c){let l=s[Cp];if(l){let u=l.get(n,st,r);if(u!==st)return u}c=nh(s),s=s[fr]}i=c}return o}function nh(e){let t=e[z],n=t.type;return n===2?t.declTNode:n===1?e[Ke]:null}function wf(e,t=null,n=null,r){let o=rh(e,t,n,r);return o.resolveInjectorInitializers(),o}function rh(e,t=null,n=null,r,o=new Set){let i=[n||Se,n0(e)];return r=r||(typeof e=="object"?void 0:Oe(e)),new Jr(i,t||Tl(),r||null,o)}var $e=class e{static THROW_IF_NOT_FOUND=sn;static NULL=new Ui;static create(t,n){if(Array.isArray(t))return wf({name:""},n,t,"");{let r=t.name??"";return wf({name:r},t.parent,t.providers,r)}}static \u0275prov=I({token:e,providedIn:"any",factory:()=>x(fp)});static __NG_ELEMENT_ID__=-1};var n_=new D("");n_.__NG_ELEMENT_ID__=e=>{let t=Fe();if(t===null)throw new E(204,!1);if(t.type&2)return t.value;if(e&L.Optional)return null;throw new E(204,!1)};var oh=!1,gr=(()=>{class e{static __NG_ELEMENT_ID__=r_;static __NG_ENV_ID__=n=>n}return e})(),Kc=class extends gr{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return hr(n)?(t(),()=>{}):(Ap(n,t),()=>C0(n,t))}};function r_(){return new Kc(Y())}var sr=class{},Bl=new D("",{providedIn:"root",factory:()=>!1});var ih=new D(""),sh=new D(""),Ot=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new me(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})();var Xc=class extends re{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,_p()&&(this.destroyRef=y(gr,{optional:!0})??void 0,this.pendingTasks=y(Ot,{optional:!0})??void 0)}emit(t){let n=B(null);try{super.next(t)}finally{B(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof X&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ae=Xc;function Ki(...e){}function ah(e){let t,n;function r(){e=Ki;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Ef(e){return queueMicrotask(()=>e()),()=>{e=Ki}}var Hl="isAngularZone",Xi=Hl+"_ID",o_=0,te=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ae(!1);onMicrotaskEmpty=new ae(!1);onStable=new ae(!1);onError=new ae(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=oh}=t;if(typeof Zone>"u")throw new E(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,a_(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Hl)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new E(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new E(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,i_,Ki,Ki);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},i_={};function $l(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function s_(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){ah(()=>{e.callbackScheduled=!1,el(e),e.isCheckStableRunning=!0,$l(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),el(e)}function a_(e){let t=()=>{s_(e)},n=o_++;e._inner=e._inner.fork({name:"angular",properties:{[Hl]:!0,[Xi]:n,[Xi+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(c_(c))return r.invokeTask(i,s,a,c);try{return If(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),xf(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return If(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!l_(c)&&t(),xf(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,el(e),$l(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function el(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function If(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function xf(e){e._nesting--,$l(e)}var tl=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ae;onMicrotaskEmpty=new ae;onStable=new ae;onError=new ae;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function c_(e){return ch(e,"__ignore_ng_zone__")}function l_(e){return ch(e,"__scheduler_tick__")}function ch(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var dt=class{_console=console;handleError(t){this._console.error("ERROR",t)}},u_=new D("",{providedIn:"root",factory:()=>{let e=y(te),t=y(dt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Sf(e,t){return ep(e,t)}function d_(e){return ep(Xf,e)}var lh=(Sf.required=d_,Sf);function f_(){return vs(Fe(),Y())}function vs(e,t){return new Xe(pt(e,t))}var Xe=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=f_}return e})();function p_(e){return typeof e=="function"&&e[Ve]!==void 0}function ao(e,t){let n=ic(e,t?.equal),r=n[Ve];return n.set=o=>Br(r,o),n.update=o=>sc(r,o),n.asReadonly=h_.bind(n),n}function h_(){let e=this[Ve];if(e.readonlyFn===void 0){let t=()=>this();t[Ve]=e,e.readonlyFn=t}return e.readonlyFn}function uh(e){return p_(e)&&typeof e.set=="function"}function dh(e){return(e.flags&128)===128}var fh=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(fh||{}),ph=new Map,g_=0;function m_(){return g_++}function v_(e){ph.set(e[ds],e)}function nl(e){ph.delete(e[ds])}var Of="__ngContext__";function co(e,t){Bt(t)?(e[Of]=t[ds],v_(t)):e[Of]=t}function hh(e){return mh(e[Kr])}function gh(e){return mh(e[Qe])}function mh(e){for(;e!==null&&!St(e);)e=e[Qe];return e}var rl;function vh(e){rl=e}function y_(){if(rl!==void 0)return rl;if(typeof document<"u")return document;throw new E(210,!1)}var zl=new D("",{providedIn:"root",factory:()=>__}),__="ng",Gl=new D(""),mr=new D("",{providedIn:"platform",factory:()=>"unknown"});var ql=new D("",{providedIn:"root",factory:()=>y_().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var C_="h",b_="b";var yh=!1,M_=new D("",{providedIn:"root",factory:()=>yh});var Wl=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Wl||{}),vr=new D(""),Pf=new Set;function yr(e){Pf.has(e)||(Pf.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var _h=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=D_}return e})();function D_(){return new _h(Y(),Fe())}var Qn=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Qn||{}),Ch=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})(),w_=[Qn.EarlyRead,Qn.Write,Qn.MixedReadWrite,Qn.Read],E_=(()=>{class e{ngZone=y(te);scheduler=y(sr);errorHandler=y(dt,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){y(vr,{optional:!0})}execute(){let n=this.sequences.size>0;n&&Z(16),this.executing=!0;for(let r of w_)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&Z(17)}register(n){let{view:r}=n;r!==void 0?((r[Kn]??=[]).push(n),oo(r),r[k]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Wl.AFTER_NEXT_RENDER,n):n()}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})(),ol=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[Kn];t&&(this.view[Kn]=t.filter(n=>n!==this))}};function Yl(e,t){!t?.injector&&f0(Yl);let n=t?.injector??y($e);return yr("NgAfterNextRender"),x_(e,n,t,!0)}function I_(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function x_(e,t,n,r){let o=t.get(Ch);o.impl??=t.get(E_);let i=t.get(vr,null,{optional:!0}),s=n?.phase??Qn.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(gr):null,c=t.get(_h,null,{optional:!0}),l=new ol(o.impl,I_(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(l),l}var S_=(e,t,n,r)=>{};function O_(e,t,n,r){S_(e,t,n,r)}var P_=()=>null;function bh(e,t,n=!1){return P_(e,t,n)}function Mh(e,t){let n=e.contentQueries;if(n!==null){let r=B(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Vp(i),a.contentQueries(2,t[s],s)}}}finally{B(r)}}}function il(e,t,n){Vp(0);let r=B(null);try{t(e,n)}finally{B(r)}}function Dh(e,t,n){if(Mp(t)){let r=B(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{B(r)}}}var ft=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(ft||{});var es=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Jf})`}};function ys(e){return e instanceof es?e.changingThisBreaksApplicationSecurity:e}function T_(e,t){let n=A_(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Jf})`)}return n===t}function A_(e){return e instanceof es&&e.getTypeName()||null}var N_=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function R_(e){return e=String(e),e.match(N_)?e:"unsafe:"+e}var wh=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(wh||{});function he(e){let t=k_();return t?t.sanitize(wh.URL,e)||"":T_(e,"URL")?ys(e):R_(er(e))}function k_(){let e=Y();return e&&e[Dt].sanitizer}function Eh(e){return e.ownerDocument.defaultView}function Ih(e){return e instanceof Function?e():e}function F_(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var xh="ng-template";function L_(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&F_(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Zl(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Zl(e){return e.type===4&&e.value!==xh}function j_(e,t,n){let r=e.type===4&&!n?xh:e.value;return t===r}function V_(e,t,n){let r=4,o=e.attrs,i=o!==null?H_(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Ye(r)&&!Ye(c))return!1;if(s&&Ye(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!j_(e,c,n)||c===""&&t.length===1){if(Ye(r))return!1;s=!0}}else if(r&8){if(o===null||!L_(e,o,c,n)){if(Ye(r))return!1;s=!0}}else{let l=t[++a],u=U_(c,o,Zl(e),n);if(u===-1){if(Ye(r))return!1;s=!0;continue}if(l!==""){let h;if(u>i?h="":h=o[u+1].toLowerCase(),r&2&&l!==h){if(Ye(r))return!1;s=!0}}}}return Ye(r)||s}function Ye(e){return(e&1)===0}function U_(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return $_(t,e)}function B_(e,t,n=!1){for(let r=0;r<t.length;r++)if(V_(e,t[r],n))return!0;return!1}function H_(e){for(let t=0;t<e.length;t++){let n=e[t];if(G0(n))return t}return e.length}function $_(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Tf(e,t){return e?":not("+t.trim()+")":t}function z_(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ye(s)&&(t+=Tf(i,o),o=""),r=s,i=i||!Ye(r);n++}return o!==""&&(t+=Tf(i,o)),t}function G_(e){return e.map(z_).join(",")}function q_(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Ye(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var ze={};function W_(e,t){return e.createText(t)}function Y_(e,t,n){e.setValue(t,n)}function Sh(e,t,n){return e.createElement(t,n)}function ts(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Oh(e,t,n){e.appendChild(t,n)}function Af(e,t,n,r,o){r!==null?ts(e,t,n,r,o):Oh(e,t,n)}function Z_(e,t,n){e.removeChild(null,t,n)}function Q_(e,t,n){e.setAttribute(t,"style",n)}function J_(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Ph(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&z0(e,t,r),o!==null&&J_(e,t,o),i!==null&&Q_(e,t,i)}function Ql(e,t,n,r,o,i,s,a,c,l,u){let h=wt+r,v=h+o,p=K_(h,v),_=typeof l=="function"?l():l;return p[z]={type:e,blueprint:p,template:n,queries:null,viewQuery:a,declTNode:t,data:p.slice().fill(null,h),bindingStartIndex:h,expandoStartIndex:v,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:_,incompleteFirstPass:!1,ssrId:u}}function K_(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:ze);return n}function X_(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Ql(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Jl(e,t,n,r,o,i,s,a,c,l,u){let h=t.blueprint.slice();return h[xt]=o,h[k]=r|4|128|8|64|1024,(l!==null||e&&e[k]&2048)&&(h[k]|=2048),Pp(h),h[De]=h[fr]=e,h[Pe]=n,h[Dt]=s||e&&e[Dt],h[ie]=a||e&&e[ie],h[rr]=c||e&&e[rr]||null,h[Ke]=i,h[ds]=m_(),h[Bi]=u,h[Cp]=l,h[at]=t.type==2?e[at]:h,h}function eC(e,t,n){let r=pt(t,e),o=X_(n),i=e[Dt].rendererFactory,s=Kl(e,Jl(e,o,null,Th(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Th(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Ah(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Kl(e,t){return e[Kr]?e[mf][Qe]=t:e[Kr]=t,e[mf]=t,t}function g(e=1){Nh(we(),Y(),zt()+e,!1)}function Nh(e,t,n,r){if(!r)if((t[k]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Ai(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Ni(t,i,0,n)}fn(n)}var _s=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(_s||{});function sl(e,t,n,r){let o=B(null);try{let[i,s,a]=e.inputs[n],c=null;(s&_s.SignalBased)!==0&&(c=t[i][Ve]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Dp(t,c,i,r)}finally{B(o)}}function Rh(e,t,n,r,o){let i=zt(),s=r&2;try{fn(-1),s&&t.length>wt&&Nh(e,t,wt,!1),Z(s?2:0,o),n(r,o)}finally{fn(i),Z(s?3:1,o)}}function Xl(e,t,n){sC(e,t,n),(n.flags&64)===64&&aC(e,t,n)}function kh(e,t,n=pt){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function tC(e,t,n,r){let i=r.get(M_,yh)||n===ft.ShadowDom,s=e.selectRootElement(t,i);return nC(s),s}function nC(e){rC(e)}var rC=()=>null;function oC(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function eu(e,t,n,r,o,i,s,a){if(!a&&tu(t,e,n,r,o)){pr(t)&&iC(n,t.index);return}if(t.type&3){let c=pt(t,n);r=oC(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function iC(e,t){let n=ut(t,e);n[k]&16||(n[k]|=64)}function sC(e,t,n){let r=n.directiveStart,o=n.directiveEnd;pr(n)&&eC(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Qi(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Ji(t,e,s,n);if(co(c,t),i!==null&&dC(t,s-r,c,a,n,i),ct(a)){let l=ut(n.index,t);l[Pe]=Ji(t,e,s,n)}}}function aC(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=R0();try{fn(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];Yc(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&cC(c,l)}}finally{fn(-1),Yc(s)}}function cC(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Fh(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];B_(t,i.selectors,!1)&&(r??=[],ct(i)?r.unshift(i):r.push(i))}return r}function lC(e,t,n,r,o,i){let s=pt(e,t);uC(t[ie],s,i,e.value,n,r,o)}function uC(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?er(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function dC(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];sl(r,n,c,l)}}function fC(e,t){let n=e[rr],r=n?n.get(dt,null):null;r&&r.handleError(t)}function tu(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],h=t.data[l];sl(h,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];sl(u,l,r,o),a=!0}return a}function pC(e,t){let n=ut(t,e),r=n[z];hC(r,n);let o=n[xt];o!==null&&n[Bi]===null&&(n[Bi]=bh(o,n[rr])),Z(18),nu(r,n,n[Pe]),Z(19,n[Pe])}function hC(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function nu(e,t,n){kl(t);try{let r=e.viewQuery;r!==null&&il(1,r,n);let o=e.template;o!==null&&Rh(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[ir]?.finishViewCreation(e),e.staticContentQueries&&Mh(e,t),e.staticViewQueries&&il(2,e.viewQuery,n);let i=e.components;i!==null&&gC(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[k]&=-5,Fl()}}function gC(e,t){for(let n=0;n<t.length;n++)pC(e,t[n])}function mC(e,t,n,r){let o=B(null);try{let i=t.tView,a=e[k]&4096?4096:16,c=Jl(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[or]=l;let u=e[ir];return u!==null&&(c[ir]=u.createEmbeddedView(i)),nu(i,c,n),c}finally{B(o)}}function Nf(e,t){return!t||t.firstChild===null||dh(e)}var vC;function ru(e,t){return vC(e,t)}var Et=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Et||{});function Lh(e){return(e.flags&32)===32}function Jn(e,t,n,r,o){if(r!=null){let i,s=!1;St(r)?i=r:Bt(r)&&(s=!0,r=r[xt]);let a=lt(r);e===0&&n!==null?o==null?Oh(t,n,a):ts(t,n,a,o||null,!0):e===1&&n!==null?ts(t,n,a,o||null,!0):e===2?Z_(t,a,s):e===3&&t.destroyNode(a),i!=null&&OC(t,e,i,n,o)}}function yC(e,t){jh(e,t),t[xt]=null,t[Ke]=null}function _C(e,t,n,r,o,i){r[xt]=o,r[Ke]=t,Cs(e,r,n,1,o,i)}function jh(e,t){t[Dt].changeDetectionScheduler?.notify(9),Cs(e,t,t[ie],2,null,null)}function CC(e){let t=e[Kr];if(!t)return Pc(e[z],e);for(;t;){let n=null;if(Bt(t))n=t[Kr];else{let r=t[He];r&&(n=r)}if(!n){for(;t&&!t[Qe]&&t!==e;)Bt(t)&&Pc(t[z],t),t=t[De];t===null&&(t=e),Bt(t)&&Pc(t[z],t),n=t&&t[Qe]}t=n}}function ou(e,t){let n=e[Gi],r=n.indexOf(t);n.splice(r,1)}function Vh(e,t){if(hr(t))return;let n=t[ie];n.destroyNode&&Cs(e,t,n,3,null,null),CC(t)}function Pc(e,t){if(hr(t))return;let n=B(null);try{t[k]&=-129,t[k]|=256,t[Be]&&nc(t[Be]),MC(e,t),bC(e,t),t[z].type===1&&t[ie].destroy();let r=t[or];if(r!==null&&St(t[De])){r!==t[De]&&ou(r,t);let o=t[ir];o!==null&&o.detachView(e)}nl(t)}finally{B(n)}}function bC(e,t){let n=e.cleanup,r=t[Hi];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Hi]=null);let o=t[Ut];if(o!==null){t[Ut]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[$i];if(i!==null){t[$i]=null;for(let s of i)s.destroy()}}function MC(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof pn)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];Z(4,a,c);try{c.call(a)}finally{Z(5,a,c)}}else{Z(4,o,i);try{i.call(o)}finally{Z(5,o,i)}}}}}function DC(e,t,n){return wC(e,t.parent,n)}function wC(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[xt];if(pr(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===ft.None||o===ft.Emulated)return null}return pt(r,n)}function EC(e,t,n){return xC(e,t,n)}function IC(e,t,n){return e.type&40?pt(e,n):null}var xC=IC,Rf;function iu(e,t,n,r){let o=DC(e,r,t),i=t[ie],s=r.parent||t[Ke],a=EC(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Af(i,o,n[c],a,!1);else Af(i,o,n,a,!1);Rf!==void 0&&Rf(i,r,t,n,o)}function Yr(e,t){if(t!==null){let n=t.type;if(n&3)return pt(t,e);if(n&4)return al(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Yr(e,r);{let o=e[t.index];return St(o)?al(-1,o):lt(o)}}else{if(n&128)return Yr(e,t.next);if(n&32)return ru(t,e)()||lt(e[t.index]);{let r=Uh(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=dn(e[at]);return Yr(o,r)}else return Yr(e,t.next)}}}return null}function Uh(e,t){if(t!==null){let r=e[at][Ke],o=t.projection;return r.projection[o]}return null}function al(e,t){let n=He+e+1;if(n<t.length){let r=t[n],o=r[z].firstChild;if(o!==null)return Yr(r,o)}return t[un]}function su(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&co(lt(a),r),n.flags|=2),!Lh(n))if(c&8)su(e,t,n.child,r,o,i,!1),Jn(t,e,o,a,i);else if(c&32){let l=ru(n,r),u;for(;u=l();)Jn(t,e,o,u,i);Jn(t,e,o,a,i)}else c&16?SC(e,t,r,n,o,i):Jn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Cs(e,t,n,r,o,i){su(n,r,e.firstChild,t,o,i,!1)}function SC(e,t,n,r,o,i){let s=n[at],c=s[Ke].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];Jn(t,e,o,u,i)}else{let l=c,u=s[De];dh(r)&&(l.flags|=128),su(e,t,l,u,o,i,!0)}}function OC(e,t,n,r,o){let i=n[un],s=lt(n);i!==s&&Jn(t,e,r,i,o);for(let a=He;a<n.length;a++){let c=n[a];Cs(c[z],c,e,t,r,i)}}function PC(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Et.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Et.Important),e.setStyle(n,r,o,i))}}function ns(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(lt(i)),St(i)&&TC(i,r);let s=n.type;if(s&8)ns(e,t,n.child,r);else if(s&32){let a=ru(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Uh(t,n);if(Array.isArray(a))r.push(...a);else{let c=dn(t[at]);ns(c[z],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function TC(e,t){for(let n=He;n<e.length;n++){let r=e[n],o=r[z].firstChild;o!==null&&ns(r[z],r,o,t)}e[un]!==e[xt]&&t.push(e[un])}function Bh(e){if(e[Kn]!==null){for(let t of e[Kn])t.impl.addSequence(t);e[Kn].length=0}}var Hh=[];function AC(e){return e[Be]??NC(e)}function NC(e){let t=Hh.pop()??Object.create(kC);return t.lView=e,t}function RC(e){e.lView[Be]!==e&&(e.lView=null,Hh.push(e))}var kC=U(M({},Vr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{oo(e.lView)},consumerOnSignalRead(){this.lView[Be]=this}});function FC(e){let t=e[Be]??Object.create(LC);return t.lView=e,t}var LC=U(M({},Vr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=dn(e.lView);for(;t&&!$h(t[z]);)t=dn(t);t&&Tp(t)},consumerOnSignalRead(){this.lView[Be]=this}});function $h(e){return e.type!==2}function zh(e){if(e[$i]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[$i])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[k]&8192)}}var jC=100;function Gh(e,t=!0,n=0){let o=e[Dt].rendererFactory,i=!1;i||o.begin?.();try{VC(e,n)}catch(s){throw t&&fC(e,s),s}finally{i||o.end?.()}}function VC(e,t){let n=jp();try{yf(!0),cl(e,t);let r=0;for(;fs(e);){if(r===jC)throw new E(103,!1);r++,cl(e,1)}}finally{yf(n)}}function UC(e,t,n,r){if(hr(t))return;let o=t[k],i=!1,s=!1;kl(t);let a=!0,c=null,l=null;i||($h(e)?(l=AC(t),c=Jo(l)):Qa()===null?(a=!1,l=FC(t),c=Jo(l)):t[Be]&&(nc(t[Be]),t[Be]=null));try{Pp(t),T0(e.bindingStartIndex),n!==null&&Rh(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let p=e.preOrderCheckHooks;p!==null&&Ai(t,p,null)}else{let p=e.preOrderHooks;p!==null&&Ni(t,p,0,null),Sc(t,0)}if(s||BC(t),zh(t),qh(t,0),e.contentQueries!==null&&Mh(e,t),!i)if(u){let p=e.contentCheckHooks;p!==null&&Ai(t,p)}else{let p=e.contentHooks;p!==null&&Ni(t,p,1),Sc(t,1)}$C(e,t);let h=e.components;h!==null&&Yh(t,h,0);let v=e.viewQuery;if(v!==null&&il(2,v,r),!i)if(u){let p=e.viewCheckHooks;p!==null&&Ai(t,p)}else{let p=e.viewHooks;p!==null&&Ni(t,p,2),Sc(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[xc]){for(let p of t[xc])p();t[xc]=null}i||(Bh(t),t[k]&=-73)}catch(u){throw i||oo(t),u}finally{l!==null&&(ec(l,c),a&&RC(l)),Fl()}}function qh(e,t){for(let n=hh(e);n!==null;n=gh(n))for(let r=He;r<n.length;r++){let o=n[r];Wh(o,t)}}function BC(e){for(let t=hh(e);t!==null;t=gh(t)){if(!(t[k]&2))continue;let n=t[Gi];for(let r=0;r<n.length;r++){let o=n[r];Tp(o)}}}function HC(e,t,n){Z(18);let r=ut(t,e);Wh(r,n),Z(19,r[Pe])}function Wh(e,t){Nl(e)&&cl(e,t)}function cl(e,t){let r=e[z],o=e[k],i=e[Be],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&tc(i)),s||=!1,i&&(i.dirty=!1),e[k]&=-9217,s)UC(r,e,r.template,e[Pe]);else if(o&8192){zh(e),qh(e,1);let a=r.components;a!==null&&Yh(e,a,1),Bh(e)}}function Yh(e,t,n){for(let r=0;r<t.length;r++)HC(e,t[r],n)}function $C(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)fn(~o);else{let i=o,s=n[++r],a=n[++r];N0(s,i);let c=t[i];Z(24,c),a(2,c),Z(25,c)}}}finally{fn(-1)}}function au(e,t){let n=jp()?64:1088;for(e[Dt].changeDetectionScheduler?.notify(t);e;){e[k]|=n;let r=dn(e);if(qi(e)&&!r)return e;e=r}return null}function Zh(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function zC(e,t,n,r=!0){let o=t[z];if(GC(o,t,e,n),r){let s=al(n,e),a=t[ie],c=a.parentNode(e[un]);c!==null&&_C(o,e[Ke],a,t,c,s)}let i=t[Bi];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function ll(e,t){if(e.length<=He)return;let n=He+t,r=e[n];if(r){let o=r[or];o!==null&&o!==e&&ou(o,r),t>0&&(e[n-1][Qe]=r[Qe]);let i=Vi(e,He+t);yC(r[z],r);let s=i[ir];s!==null&&s.detachView(i[z]),r[De]=null,r[Qe]=null,r[k]&=-129}return r}function GC(e,t,n,r){let o=He+r,i=n.length;r>0&&(n[o-1][Qe]=t),r<i-He?(t[Qe]=n[o],dp(n,He+r,t)):(n.push(t),t[Qe]=null),t[De]=n;let s=t[or];s!==null&&n!==s&&Qh(s,t);let a=t[ir];a!==null&&a.insertView(e),qc(t),t[k]|=128}function Qh(e,t){let n=e[Gi],r=t[De];if(Bt(r))e[k]|=2;else{let o=r[De][at];t[at]!==o&&(e[k]|=2)}n===null?e[Gi]=[t]:n.push(t)}var eo=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[z];return ns(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[Pe]}set context(t){this._lView[Pe]=t}get destroyed(){return hr(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[De];if(St(t)){let n=t[zi],r=n?n.indexOf(this):-1;r>-1&&(ll(t,r),Vi(n,r))}this._attachedToViewContainer=!1}Vh(this._lView[z],this._lView)}onDestroy(t){Ap(this._lView,t)}markForCheck(){au(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[k]&=-129}reattach(){qc(this._lView),this._lView[k]|=128}detectChanges(){this._lView[k]|=1024,Gh(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new E(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=qi(this._lView),n=this._lView[or];n!==null&&!t&&ou(n,this._lView),jh(this._lView[z],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new E(902,!1);this._appRef=t;let n=qi(this._lView),r=this._lView[or];r!==null&&!n&&Qh(r,this._lView),qc(this._lView)}};var bs=(()=>{class e{static __NG_ELEMENT_ID__=YC}return e})(),qC=bs,WC=class extends qC{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=mC(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new eo(o)}};function YC(){return ZC(Fe(),Y())}function ZC(e,t){return e.type&4?new WC(t,e,vs(e,t)):null}function cu(e,t,n,r,o){let i=e.data[t];if(i===null)i=QC(e,t,n,r,o),A0()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=x0();i.injectorIndex=s===null?-1:s.injectorIndex}return io(i,!0),i}function QC(e,t,n,r,o){let i=Fp(),s=Lp(),a=s?i:i&&i.parent,c=e.data[t]=KC(e,a,n,t,r,o);return JC(e,c,i,s),c}function JC(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function KC(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return w0()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var PA=new RegExp(`^(\\d+)*(${b_}|${C_})*(.*)`);var XC=()=>null;function kf(e,t){return XC(e,t)}var eb=class{},Jh=class{},ul=class{resolveComponentFactory(t){throw Error(`No component factory found for ${Oe(t)}.`)}},Ms=class{static NULL=new ul},ar=class{},Gt=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>tb()}return e})();function tb(){let e=Y(),t=Fe(),n=ut(t.index,e);return(Bt(n)?n:e)[ie]}var nb=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:()=>null})}return e})();var Tc={},dl=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=ls(r);let o=this.injector.get(t,Tc,r);return o!==Tc||n===Tc?o:this.parentInjector.get(t,n,r)}};function Ff(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=jc(o,a);else if(i==2){let c=a,l=t[++s];r=jc(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function P(e,t=L.Default){let n=Y();if(n===null)return x(e,t);let r=Fe();return eh(r,n,Me(e),t)}function Kh(){let e="invalid";throw new Error(e)}function Xh(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,l=null,u=ob(s);u===null?a=s:[a,c,l]=u,ab(e,t,n,a,i,c,l)}i!==null&&r!==null&&rb(n,r,i)}function rb(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new E(-301,!1);r.push(t[o],i)}}function ob(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&ct(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,ib(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function ib(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function sb(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function ab(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let v=0;v<a;v++){let p=r[v];!c&&ct(p)&&(c=!0,sb(e,n,v)),Qc(Qi(n,t),e,p.type)}pb(n,e.data.length,a);for(let v=0;v<a;v++){let p=r[v];p.providersResolver&&p.providersResolver(p)}let l=!1,u=!1,h=Ah(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let v=0;v<a;v++){let p=r[v];if(n.mergedAttrs=Xr(n.mergedAttrs,p.hostAttrs),lb(e,n,t,h,p),fb(h,p,o),s!==null&&s.has(p)){let[w,T]=s.get(p);n.directiveToIndex.set(p.type,[h,w+n.directiveStart,T+n.directiveStart])}else(i===null||!i.has(p))&&n.directiveToIndex.set(p.type,h);p.contentQueries!==null&&(n.flags|=4),(p.hostBindings!==null||p.hostAttrs!==null||p.hostVars!==0)&&(n.flags|=64);let _=p.type.prototype;!l&&(_.ngOnChanges||_.ngOnInit||_.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(_.ngOnChanges||_.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),h++}cb(e,n,i)}function cb(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Lf(0,t,o,r),Lf(1,t,o,r),Vf(t,r,!1);else{let i=n.get(o);jf(0,t,i,r),jf(1,t,i,r),Vf(t,r,!0)}}}function Lf(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),eg(t,i)}}function jf(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),eg(t,s)}}function eg(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Vf(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Zl(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function lb(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=tr(o.type,!0)),s=new pn(i,ct(o),P);e.blueprint[r]=s,n[r]=s,ub(e,t,r,Ah(e,n,o.hostVars,ze),o)}function ub(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;db(s)!=a&&s.push(a),s.push(n,r,i)}}function db(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function fb(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;ct(t)&&(n[""]=e)}}function pb(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function tg(e,t,n,r,o,i,s,a){let c=t.consts,l=Wi(c,s),u=cu(t,e,2,r,l);return i&&Xh(t,n,u,Wi(c,a),o),u.mergedAttrs=Xr(u.mergedAttrs,u.attrs),u.attrs!==null&&Ff(u,u.attrs,!1),u.mergedAttrs!==null&&Ff(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function ng(e,t){qp(e,t),Mp(t)&&e.queries.elementEnd(t)}var rs=class extends Ms{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=ln(t);return new cr(n,this.ngModule)}};function hb(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&_s.SignalBased)!==0};return o&&(i.transform=o),i})}function gb(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function mb(e,t,n){let r=t instanceof ye?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new dl(n,r):n}function vb(e){let t=e.get(ar,null);if(t===null)throw new E(407,!1);let n=e.get(nb,null),r=e.get(sr,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function yb(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Sh(t,n,n==="svg"?xp:n==="math"?v0:null)}var cr=class extends Jh{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=hb(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=gb(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=G_(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){Z(22);let i=B(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:q_(this.componentDef.selectors[0]),c=Ql(0,null,null,1,0,null,null,null,null,[a],null),l=mb(s,o||this.ngModule,t),u=vb(l),h=u.rendererFactory.createRenderer(null,s),v=r?tC(h,r,s.encapsulation,l):yb(s,h),p=Jl(null,c,null,512|Th(s),null,null,u,h,l,null,bh(v,l,!0));p[wt]=v,kl(p);let _=null;try{let w=tg(wt,c,p,"#host",()=>[this.componentDef],!0,0);v&&(Ph(h,v,w),co(v,p)),Xl(c,p,w),Dh(c,w,p),ng(c,w),n!==void 0&&_b(w,this.ngContentSelectors,n),_=ut(w.index,p),p[Pe]=_[Pe],nu(c,p,null)}catch(w){throw _!==null&&nl(_),nl(p),w}finally{Z(23),Fl()}return new fl(this.componentType,p)}finally{B(i)}}},fl=class extends eb{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=Op(n[z],wt),this.location=vs(this._tNode,n),this.instance=ut(this._tNode.index,n)[Pe],this.hostView=this.changeDetectorRef=new eo(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=tu(r,o[z],o,t,n);this.previousInputValues.set(t,n);let s=ut(r.index,o);au(s,1)}get injector(){return new an(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function _b(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var _r=(()=>{class e{static __NG_ELEMENT_ID__=Cb}return e})();function Cb(){let e=Fe();return Mb(e,Y())}var bb=_r,rg=class extends bb{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return vs(this._hostTNode,this._hostLView)}get injector(){return new an(this._hostTNode,this._hostLView)}get parentInjector(){let t=Ul(this._hostTNode,this._hostLView);if(Yp(t)){let n=Zi(t,this._hostLView),r=Yi(t),o=n[z].data[r+8];return new an(o,n)}else return new an(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Uf(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-He}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=kf(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Nf(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!p0(t),a;if(s)a=n;else{let _=n||{};a=_.index,r=_.injector,o=_.projectableNodes,i=_.environmentInjector||_.ngModuleRef}let c=s?t:new cr(ln(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let w=(s?l:this.parentInjector).get(ye,null);w&&(i=w)}let u=ln(c.componentType??{}),h=kf(this._lContainer,u?.id??null),v=h?.firstChild??null,p=c.create(l,o,v,i);return this.insertImpl(p.hostView,a,Nf(this._hostTNode,h)),p}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(y0(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[De],l=new rg(c,c[Ke],c[De]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return zC(s,o,i,r),t.attachToViewContainerRef(),dp(Ac(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Uf(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=ll(this._lContainer,n);r&&(Vi(Ac(this._lContainer),n),Vh(r[z],r))}detach(t){let n=this._adjustIndex(t,-1),r=ll(this._lContainer,n);return r&&Vi(Ac(this._lContainer),n)!=null?new eo(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Uf(e){return e[zi]}function Ac(e){return e[zi]||(e[zi]=[])}function Mb(e,t){let n,r=t[e.index];return St(r)?n=r:(n=Zh(r,t,null,e),t[e.index]=n,Kl(t,n)),wb(n,t,e,r),new rg(n,e,t)}function Db(e,t){let n=e[ie],r=n.createComment(""),o=pt(t,e),i=n.parentNode(o);return ts(n,i,r,n.nextSibling(o),!1),r}var wb=xb,Eb=()=>!1;function Ib(e,t,n){return Eb(e,t,n)}function xb(e,t,n,r){if(e[un])return;let o;n.type&8?o=lt(r):o=Db(t,n),e[un]=o}var lr=class{},lu=class{};var pl=class extends lr{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new rs(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=hp(t);this._bootstrapComponents=Ih(i.bootstrap),this._r3Injector=rh(t,n,[{provide:lr,useValue:this},{provide:Ms,useValue:this.componentFactoryResolver},...r],Oe(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},hl=class extends lu{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new pl(this.moduleType,t,[])}};var os=class extends lr{injector;componentFactoryResolver=new rs(this);instance=null;constructor(t){super();let n=new Jr([...t.providers,{provide:lr,useValue:this},{provide:Ms,useValue:this.componentFactoryResolver}],t.parent||Tl(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function lo(e,t,n=null){return new os({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var Sb=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=gp(!1,n.type),o=r.length>0?lo([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=I({token:e,providedIn:"environment",factory:()=>new e(x(ye))})}return e})();function ne(e){return no(()=>{let t=og(e),n=U(M({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===fh.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(Sb).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||ft.Emulated,styles:e.styles||Se,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&yr("NgStandalone"),ig(n);let r=e.dependencies;return n.directiveDefs=Bf(r,!1),n.pipeDefs=Bf(r,!0),n.id=Nb(n),n})}function Ob(e){return ln(e)||e0(e)}function Pb(e){return e!==null}function et(e){return no(()=>({type:e.type,bootstrap:e.bootstrap||Se,declarations:e.declarations||Se,imports:e.imports||Se,exports:e.exports||Se,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Tb(e,t){if(e==null)return cn;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=_s.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function Ab(e){if(e==null)return cn;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function ce(e){return no(()=>{let t=og(e);return ig(t),t})}function og(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||cn,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Se,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Tb(e.inputs,t),outputs:Ab(e.outputs),debugInfo:null}}function ig(e){e.features?.forEach(t=>t(e))}function Bf(e,t){if(!e)return null;let n=t?t0:Ob;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(Pb)}function Nb(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Rb(e){return Object.getPrototypeOf(e.prototype).constructor}function tt(e){let t=Rb(e.type),n=!0,r=[e];for(;t;){let o;if(ct(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new E(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Nc(e.inputs),s.declaredInputs=Nc(e.declaredInputs),s.outputs=Nc(e.outputs);let a=o.hostBindings;a&&Vb(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&Lb(e,c),l&&jb(e,l),kb(e,o),Ny(e.outputs,o.outputs),ct(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===tt&&(n=!1)}}t=Object.getPrototypeOf(t)}Fb(r)}function kb(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function Fb(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Xr(o.hostAttrs,n=Xr(n,o.hostAttrs))}}function Nc(e){return e===cn?{}:e===Se?[]:e}function Lb(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function jb(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function Vb(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function sg(e){return Bb(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Ub(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Bb(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Hb(e,t,n){return e[t]=n}function $b(e,t){return e[t]}function Ht(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function zb(e,t,n,r){let o=Ht(e,t,n);return Ht(e,t+1,r)||o}function Gb(e,t,n,r,o,i,s,a,c){let l=t.consts,u=cu(t,e,4,s||null,a||null);kp()&&Xh(t,n,u,Wi(l,c),Fh),u.mergedAttrs=Xr(u.mergedAttrs,u.attrs),qp(t,u);let h=u.tView=Ql(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),h.queries=t.queries.embeddedTView(u)),u}function qb(e,t,n,r,o,i,s,a,c,l){let u=n+wt,h=t.firstCreatePass?Gb(u,t,e,r,o,i,s,a,c):t.data[u];io(h,!1);let v=Wb(t,e,h,n);jl()&&iu(t,e,v,h),co(v,e);let p=Zh(v,e,v,h);return e[u]=p,Kl(e,p),Ib(p,h,e),Al(h)&&Xl(t,e,h),c!=null&&kh(e,h,l),h}function N(e,t,n,r,o,i,s,a){let c=Y(),l=we(),u=Wi(l.consts,i);return qb(c,l,e,t,n,r,o,u,s,a),N}var Wb=Yb;function Yb(e,t,n,r){return Vl(!0),t[ie].createComment("")}var ag=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var cg=new D("");var Zb=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:()=>new gl})}return e})(),gl=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function vn(e){return!!e&&typeof e.then=="function"}function lg(e){return!!e&&typeof e.subscribe=="function"}var ug=new D("");function uu(e){return dr([{provide:ug,multi:!0,useValue:e}])}var dg=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=y(ug,{optional:!0})??[];injector=y($e);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Te(this.injector,o);if(vn(i))n.push(i);else if(lg(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ds=new D("");function Qb(){oc(()=>{throw new E(600,!1)})}function Jb(e){return e.isBoundToModule}var Kb=10;var hn=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=y(u_);afterRenderManager=y(Ch);zonelessEnabled=y(Bl);rootEffectScheduler=y(Zb);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new re;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=y(Ot).hasPendingTasks.pipe(A(n=>!n));constructor(){y(vr,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=y(ye);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=$e.NULL){Z(10);let i=n instanceof Jh;if(!this._injector.get(dg).done){let p="";throw new E(405,p)}let a;i?a=n:a=this._injector.get(Ms).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=Jb(a)?void 0:this._injector.get(lr),l=r||a.selector,u=a.create(o,[],l,c),h=u.location.nativeElement,v=u.injector.get(cg,null);return v?.registerApplication(h),u.onDestroy(()=>{this.detachView(u.hostView),Ri(this.components,u),v?.unregisterApplication(h)}),this._loadComponent(u),Z(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){Z(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Wl.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new E(101,!1);let n=B(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,B(n),this.afterTick.next(),Z(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(ar,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<Kb;)Z(14),this.synchronizeOnce(),Z(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)Xb(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>fs(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Ri(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Ds,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Ri(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new E(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ri(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Xb(e,t,n,r){if(!n&&!fs(e))return;Gh(e,t,n&&!r?0:1)}function yn(e,t,n,r){let o=Y(),i=gs();if(Ht(o,i,t)){let s=we(),a=ms();lC(a,o,e,t,n,r)}return yn}function fg(e,t,n,r){return Ht(e,gs(),n)?t+er(n)+r:ze}function eM(e,t,n,r,o,i){let s=P0(),a=zb(e,s,n,o);return Rl(2),a?t+er(n)+r+er(o)+i:ze}function Pi(e,t){return e<<17|t<<2}function gn(e){return e>>17&32767}function tM(e){return(e&2)==2}function nM(e,t){return e&131071|t<<17}function ml(e){return e|2}function ur(e){return(e&131068)>>2}function Rc(e,t){return e&-131069|t<<2}function rM(e){return(e&1)===1}function vl(e){return e|1}function oM(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=gn(s),c=ur(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let h=n;u=h[1],(u===null||ro(h,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let v=gn(e[a+1]);e[r+1]=Pi(v,a),v!==0&&(e[v+1]=Rc(e[v+1],r)),e[a+1]=nM(e[a+1],r)}else e[r+1]=Pi(a,0),a!==0&&(e[a+1]=Rc(e[a+1],r)),a=r;else e[r+1]=Pi(c,0),a===0?a=r:e[c+1]=Rc(e[c+1],r),c=r;l&&(e[r+1]=ml(e[r+1])),Hf(e,u,r,!0),Hf(e,u,r,!1),iM(t,u,e,r,i),s=Pi(a,c),i?t.classBindings=s:t.styleBindings=s}function iM(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&ro(i,t)>=0&&(n[r+1]=vl(n[r+1]))}function Hf(e,t,n,r){let o=e[n+1],i=t===null,s=r?gn(o):ur(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];sM(c,t)&&(a=!0,e[s+1]=r?vl(l):ml(l)),s=r?gn(l):ur(l)}a&&(e[n+1]=r?ml(o):vl(o))}function sM(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?ro(e,t)>=0:!1}var Ze={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function aM(e){return e.substring(Ze.key,Ze.keyEnd)}function cM(e){return lM(e),pg(e,hg(e,0,Ze.textEnd))}function pg(e,t){let n=Ze.textEnd;return n===t?-1:(t=Ze.keyEnd=uM(e,Ze.key=t,n),hg(e,t,n))}function lM(e){Ze.key=0,Ze.keyEnd=0,Ze.value=0,Ze.valueEnd=0,Ze.textEnd=e.length}function hg(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function uM(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function b(e,t,n){let r=Y(),o=gs();if(Ht(r,o,t)){let i=we(),s=ms();eu(i,s,r,e,t,r[ie],n,!1)}return b}function yl(e,t,n,r,o){tu(t,e,n,o?"class":"style",r)}function Ae(e,t,n){return gg(e,t,n,!1),Ae}function fe(e,t){return gg(e,t,null,!0),fe}function Ge(e){fM(yM,dM,e,!0)}function dM(e,t){for(let n=cM(t);n>=0;n=pg(t,n))Ol(e,aM(t),!0)}function gg(e,t,n,r){let o=Y(),i=we(),s=Rl(2);if(i.firstUpdatePass&&vg(i,e,s,r),t!==ze&&Ht(o,s,t)){let a=i.data[zt()];yg(i,a,o,o[ie],e,o[s+1]=CM(t,n),r,s)}}function fM(e,t,n,r){let o=we(),i=Rl(2);o.firstUpdatePass&&vg(o,null,i,r);let s=Y();if(n!==ze&&Ht(s,i,n)){let a=o.data[zt()];if(_g(a,r)&&!mg(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=jc(c,n||"")),yl(o,a,s,n,r)}else _M(o,a,s,s[ie],s[i+1],s[i+1]=vM(e,t,n),r,i)}}function mg(e,t){return t>=e.expandoStartIndex}function vg(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[zt()],s=mg(e,n);_g(i,r)&&t===null&&!s&&(t=!1),t=pM(o,i,t,r),oM(o,i,t,n,s,r)}}function pM(e,t,n,r){let o=k0(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=kc(null,e,t,n,r),n=to(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=kc(o,e,t,n,r),i===null){let c=hM(e,t,r);c!==void 0&&Array.isArray(c)&&(c=kc(null,e,t,c[1],r),c=to(c,t.attrs,r),gM(e,t,r,c))}else i=mM(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function hM(e,t,n){let r=n?t.classBindings:t.styleBindings;if(ur(r)!==0)return e[gn(r)]}function gM(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[gn(o)]=r}function mM(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=to(r,s,n)}return to(r,t.attrs,n)}function kc(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=to(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function to(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Ol(e,s,n?!0:t[++i]))}return e===void 0?null:e}function vM(e,t,n){if(n==null||n==="")return Se;let r=[],o=ys(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function yM(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&Ol(e,r,n)}function _M(e,t,n,r,o,i,s,a){o===ze&&(o=Se);let c=0,l=0,u=0<o.length?o[0]:null,h=0<i.length?i[0]:null;for(;u!==null||h!==null;){let v=c<o.length?o[c+1]:void 0,p=l<i.length?i[l+1]:void 0,_=null,w;u===h?(c+=2,l+=2,v!==p&&(_=h,w=p)):h===null||u!==null&&u<h?(c+=2,_=u):(l+=2,_=h,w=p),_!==null&&yg(e,t,n,r,_,w,s,a),u=c<o.length?o[c]:null,h=l<i.length?i[l]:null}}function yg(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=rM(l)?$f(c,t,n,o,ur(l),s):void 0;if(!is(u)){is(i)||tM(l)&&(i=$f(c,null,n,o,a,s));let h=Sp(zt(),n);PC(r,s,h,o,i)}}function $f(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,h=u===null,v=n[o+1];v===ze&&(v=h?Se:void 0);let p=h?Ec(v,r):u===r?v:void 0;if(l&&!is(p)&&(p=Ec(c,r)),is(p)&&(a=p,s))return a;let _=e[o+1];o=s?gn(_):ur(_)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Ec(c,r))}return a}function is(e){return e!==void 0}function CM(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Oe(ys(e)))),e}function _g(e,t){return(e.flags&(t?8:16))!==0}function d(e,t,n,r){let o=Y(),i=we(),s=wt+e,a=o[ie],c=i.firstCreatePass?tg(s,i,o,t,Fh,kp(),n,r):i.data[s],l=bM(i,o,c,a,t,e);o[s]=l;let u=Al(c);return io(c,!0),Ph(a,l,c),!Lh(c)&&jl()&&iu(i,o,l,c),(b0()===0||u)&&co(l,o),M0(),u&&(Xl(i,o,c),Dh(i,c,o)),r!==null&&kh(o,c),d}function f(){let e=Fe();Lp()?S0():(e=e.parent,io(e,!1));let t=e;E0(t)&&I0(),D0();let n=we();return n.firstCreatePass&&ng(n,t),t.classesWithoutHost!=null&&H0(t)&&yl(n,t,Y(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&$0(t)&&yl(n,t,Y(),t.stylesWithoutHost,!1),f}function C(e,t,n,r){return d(e,t,n,r),f(),C}var bM=(e,t,n,r,o,i)=>(Vl(!0),Sh(r,o,V0()));function ws(){return Y()}var ss="en-US";var MM=ss;function DM(e){typeof e=="string"&&(MM=e.toLowerCase().replace(/_/g,"-"))}function zf(e,t,n){return function r(o){if(o===Function)return n;let i=pr(e)?ut(e.index,t):t;au(i,5);let s=t[Pe],a=Gf(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=Gf(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Gf(e,t,n,r){let o=B(null);try{return Z(6,t,n),n(r)!==!1}catch(i){return wM(e,i),!1}finally{Z(7,t,n),B(o)}}function wM(e,t){let n=e[rr],r=n?n.get(dt,null):null;r&&r.handleError(t)}function qf(e,t,n,r,o,i){let s=t[n],a=t[z],l=a.data[n].outputs[r],u=s[l],h=a.firstCreatePass?Rp(a):null,v=Np(t),p=u.subscribe(i),_=v.length;v.push(i,p),h&&h.push(o,e.index,_,-(_+1))}function q(e,t,n,r){let o=Y(),i=we(),s=Fe();return Cg(i,o,o[ie],s,e,t,r),q}function EM(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Hi],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Cg(e,t,n,r,o,i,s){let a=Al(r),l=e.firstCreatePass?Rp(e):null,u=Np(t),h=!0;if(r.type&3||s){let v=pt(r,t),p=s?s(v):v,_=u.length,w=s?G=>s(lt(G[r.index])):r.index,T=null;if(!s&&a&&(T=EM(e,t,o,r.index)),T!==null){let G=T.__ngLastListenerFn__||T;G.__ngNextListenerFn__=i,T.__ngLastListenerFn__=i,h=!1}else{i=zf(r,t,i),O_(t,p,o,i);let G=n.listen(p,o,i);u.push(i,G),l&&l.push(o,w,_,_+1)}}else i=zf(r,t,i);if(h){let v=r.outputs?.[o],p=r.hostDirectiveOutputs?.[o];if(p&&p.length)for(let _=0;_<p.length;_+=2){let w=p[_],T=p[_+1];qf(r,t,w,T,o,i)}if(v&&v.length)for(let _ of v)qf(r,t,_,o,o,i)}}function Q(e=1){return L0(e)}function du(e,t,n){return bg(e,"",t,"",n),du}function bg(e,t,n,r,o){let i=Y(),s=fg(i,t,n,r);if(s!==ze){let a=we(),c=ms();eu(a,c,i,e,s,i[ie],o,!1)}return bg}function m(e,t=""){let n=Y(),r=we(),o=e+wt,i=r.firstCreatePass?cu(r,o,1,t,null):r.data[o],s=IM(r,n,i,t,e);n[o]=s,jl()&&iu(r,n,s,i),io(i,!1)}var IM=(e,t,n,r,o)=>(Vl(!0),W_(t[ie],r));function S(e){return Pt("",e,""),S}function Pt(e,t,n){let r=Y(),o=fg(r,e,t,n);return o!==ze&&Mg(r,zt(),o),Pt}function _n(e,t,n,r,o){let i=Y(),s=eM(i,e,t,n,r,o);return s!==ze&&Mg(i,zt(),s),_n}function Mg(e,t,n){let r=Sp(t,e);Y_(e[ie],r,n)}function Es(e,t,n){uh(t)&&(t=t());let r=Y(),o=gs();if(Ht(r,o,t)){let i=we(),s=ms();eu(i,s,r,e,t,r[ie],n,!1)}return Es}function fu(e,t){let n=uh(e);return n&&e.set(t),n}function Is(e,t){let n=Y(),r=we(),o=Fe();return Cg(r,n,n[ie],o,e,t),Is}function xM(e,t,n){let r=we();if(r.firstCreatePass){let o=ct(e);_l(n,r.data,r.blueprint,o,!0),_l(t,r.data,r.blueprint,o,!1)}}function _l(e,t,n,r,o){if(e=Me(e),Array.isArray(e))for(let i=0;i<e.length;i++)_l(e[i],t,n,r,o);else{let i=we(),s=Y(),a=Fe(),c=nr(e)?e:Me(e.provide),l=yp(e),u=a.providerIndexes&1048575,h=a.directiveStart,v=a.providerIndexes>>20;if(nr(e)||!e.multi){let p=new pn(l,o,P),_=Lc(c,t,o?u:u+v,h);_===-1?(Qc(Qi(a,s),i,c),Fc(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(p),s.push(p)):(n[_]=p,s[_]=p)}else{let p=Lc(c,t,u+v,h),_=Lc(c,t,u,u+v),w=p>=0&&n[p],T=_>=0&&n[_];if(o&&!T||!o&&!w){Qc(Qi(a,s),i,c);let G=PM(o?OM:SM,n.length,o,r,l);!o&&T&&(n[_].providerFactory=G),Fc(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(G),s.push(G)}else{let G=Dg(n[o?_:p],l,!o&&r);Fc(i,e,p>-1?p:_,G)}!o&&r&&T&&n[_].componentProviders++}}}function Fc(e,t,n,r){let o=nr(t),i=s0(t);if(o||i){let c=(i?Me(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function Dg(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Lc(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function SM(e,t,n,r,o){return Cl(this.multi,[])}function OM(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=Ji(r,r[z],this.providerFactory.index,o);s=c.slice(0,a),Cl(i,s);for(let l=a;l<c.length;l++)s.push(c[l])}else s=[],Cl(i,s);return s}function Cl(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function PM(e,t,n,r,o){let i=new pn(e,n,P);return i.multi=[],i.index=t,i.componentProviders=0,Dg(i,o,r&&!n),i}function Cn(e,t=[]){return n=>{n.providersResolver=(r,o)=>xM(r,o?o(e):e,t)}}function pu(e,t,n){let r=O0()+e,o=Y();return o[r]===ze?Hb(o,r,n?t.call(n):t()):$b(o,r)}var bl=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},wg=(()=>{class e{compileModuleSync(n){return new hl(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=hp(n),i=Ih(o.declarations).reduce((s,a)=>{let c=ln(a);return c&&s.push(new cr(c)),s},[]);return new bl(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var TM=(()=>{class e{zone=y(te);changeDetectionScheduler=y(sr);applicationRef=y(hn);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),AM=new D("",{factory:()=>!1});function Eg({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new te(U(M({},xg()),{scheduleInRootZone:n})),[{provide:te,useFactory:e},{provide:Qr,multi:!0,useFactory:()=>{let r=y(TM,{optional:!0});return()=>r.initialize()}},{provide:Qr,multi:!0,useFactory:()=>{let r=y(NM);return()=>{r.initialize()}}},t===!0?{provide:ih,useValue:!0}:[],{provide:sh,useValue:n??oh}]}function Ig(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=Eg({ngZoneFactory:()=>{let o=xg(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&yr("NgZone_CoalesceEvent"),new te(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return dr([{provide:AM,useValue:!0},{provide:Bl,useValue:!1},r])}function xg(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var NM=(()=>{class e{subscription=new X;initialized=!1;zone=y(te);pendingTasks=y(Ot);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{te.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{te.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var RM=(()=>{class e{appRef=y(hn);taskService=y(Ot);ngZone=y(te);zonelessEnabled=y(Bl);tracing=y(vr,{optional:!0});disableScheduling=y(ih,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new X;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Xi):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(y(sh,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof tl||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Ef:ah;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Xi+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Ef(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function kM(){return typeof $localize<"u"&&$localize.locale||ss}var hu=new D("",{providedIn:"root",factory:()=>y(hu,L.Optional|L.SkipSelf)||kM()});var Ml=new D(""),FM=new D("");function qr(e){return!e.moduleRef}function LM(e){let t=qr(e)?e.r3Injector:e.moduleRef.injector,n=t.get(te);return n.run(()=>{qr(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(dt,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),qr(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Ml);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Ml);s.add(i),e.moduleRef.onDestroy(()=>{Ri(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return VM(r,n,()=>{let i=t.get(dg);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(hu,ss);if(DM(s||ss),!t.get(FM,!0))return qr(e)?t.get(hn):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(qr(e)){let c=t.get(hn);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return jM(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function jM(e,t){let n=e.injector.get(hn);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new E(-403,!1);t.push(e)}function VM(e,t,n){try{let r=n();return vn(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var ki=null;function UM(e=[],t){return $e.create({name:t,providers:[{provide:us,useValue:"platform"},{provide:Ml,useValue:new Set([()=>ki=null])},...e]})}function BM(e=[]){if(ki)return ki;let t=UM(e);return ki=t,Qb(),HM(t),t}function HM(e){let t=e.get(Gl,null);Te(e,()=>{t?.forEach(n=>n())})}var bn=(()=>{class e{static __NG_ELEMENT_ID__=$M}return e})();function $M(e){return zM(Fe(),Y(),(e&16)===16)}function zM(e,t,n){if(pr(e)&&!n){let r=ut(e.index,t);return new eo(r,r)}else if(e.type&175){let r=t[at];return new eo(r,t)}return null}var Dl=class{constructor(){}supports(t){return sg(t)}create(t){return new wl(t)}},GM=(e,t)=>t,wl=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||GM}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Wf(r,o,i)?n:r,a=Wf(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let v=0;v<l;v++){let p=v<i.length?i[v]:i[v]=0,_=p+v;u<=_&&_<l&&(i[v]=p+1)}let h=s.previousIndex;i[h]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!sg(t))throw new E(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,Ub(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new El(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new as),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new as),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},El=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Il=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},as=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Il,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Wf(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function Yf(){return new gu([new Dl])}var gu=(()=>{class e{factories;static \u0275prov=I({token:e,providedIn:"root",factory:Yf});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Yf()),deps:[[e,new Jy,new Qy]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new E(901,!1)}}return e})();function Sg(e){Z(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=BM(r),i=[Eg({}),{provide:sr,useExisting:RM},...n||[]],s=new os({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return LM({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{Z(9)}}function xs(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Tt(e){return ac(e)}function uo(e,t){return rc(e,t?.equal)}var Zf=class{[Ve];constructor(t){this[Ve]=t}destroy(){this[Ve].destroy()}};function Og(e){let t=ln(e);if(!t)return null;let n=new cr(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var le=new D("");var Ag=null;function qe(){return Ag}function mu(e){Ag??=e}var fo=class{},po=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>y(Ng),providedIn:"platform"})}return e})(),vu=new D(""),Ng=(()=>{class e extends po{_location;_history;_doc=y(le);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return qe().getBaseHref(this._doc)}onPopState(n){let r=qe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=qe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Ss(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Pg(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function nt(e){return e&&e[0]!=="?"?`?${e}`:e}var At=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>y(Ps),providedIn:"root"})}return e})(),Os=new D(""),Ps=(()=>{class e extends At{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??y(le).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Ss(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+nt(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+nt(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+nt(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(x(po),x(Os,8))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),qt=(()=>{class e{_subject=new re;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=YM(Pg(Tg(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+nt(r))}normalize(n){return e.stripTrailingSlash(WM(this._basePath,Tg(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+nt(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+nt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=nt;static joinWithSlash=Ss;static stripTrailingSlash=Pg;static \u0275fac=function(r){return new(r||e)(x(At))};static \u0275prov=I({token:e,factory:()=>qM(),providedIn:"root"})}return e})();function qM(){return new qt(x(At))}function WM(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Tg(e){return e.replace(/\/index.html$/,"")}function YM(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var yu=(()=>{class e extends At{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Ss(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+nt(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+nt(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(x(po),x(Os,8))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();var Ts=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Ne=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Ts(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Rg(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Rg(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(P(_r),P(bs),P(gu))};static \u0275dir=ce({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Rg(e,t){e.context.$implicit=t.item}var Mn=(()=>{class e{_viewContainer;_context=new As;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){kg(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){kg(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(P(_r),P(bs))};static \u0275dir=ce({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),As=class{$implicit=null;ngIf=null};function kg(e,t){if(e&&!e.createEmbeddedView)throw new E(2020,!1)}var ge=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=et({type:e});static \u0275inj=Je({})}return e})();function ho(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var _u="browser",Fg="server";function Ns(e){return e===Fg}var Dn=class{};var Lg=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:()=>new Cu(y(le),window)})}return e})(),Cu=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=JM(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function JM(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Fs=new D(""),wu=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new E(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(x(Fs),x(te))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),go=class{_doc;constructor(t){this._doc=t}manager},Rs="ng-app-id";function jg(e){for(let t of e)t.remove()}function Vg(e,t){let n=t.createElement("style");return n.textContent=e,n}function KM(e,t,n,r){let o=e.head?.querySelectorAll(`style[${Rs}="${t}"],link[${Rs}="${t}"]`);if(o)for(let i of o)i.removeAttribute(Rs),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function Mu(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Eu=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Ns(i),KM(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,Vg);r?.forEach(o=>this.addUsage(o,this.external,Mu))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(jg(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])jg(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,Vg(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,Mu(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(Rs,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(x(le),x(zl),x(ql,8),x(mr))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),bu={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Iu=/%COMP%/g;var Bg="%COMP%",XM=`_nghost-${Bg}`,eD=`_ngcontent-${Bg}`,tD=!0,nD=new D("",{providedIn:"root",factory:()=>tD});function rD(e){return eD.replace(Iu,e)}function oD(e){return XM.replace(Iu,e)}function Hg(e,t){return t.map(n=>n.replace(Iu,e))}var xu=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,l=null,u=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=u,this.platformIsServer=Ns(a),this.defaultRenderer=new mo(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===ft.ShadowDom&&(r=U(M({},r),{encapsulation:ft.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof ks?o.applyToHost(n):o instanceof vo&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,h=this.platformIsServer,v=this.tracingService;switch(r.encapsulation){case ft.Emulated:i=new ks(c,l,r,this.appId,u,s,a,h,v);break;case ft.ShadowDom:return new Du(c,l,n,r,s,a,this.nonce,h,v);default:i=new vo(c,l,r,u,s,a,h,v);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(x(wu),x(Eu),x(zl),x(nD),x(le),x(mr),x(te),x(ql),x(vr,8))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),mo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(bu[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Ug(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Ug(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new E(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=bu[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=bu[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(Et.DashCase|Et.Important)?t.style.setProperty(n,r,o&Et.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&Et.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=qe().getGlobalEventTarget(this.doc,t),!t))throw new E(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Ug(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Du=class extends mo{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,c,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=o.styles;u=Hg(o.id,u);for(let v of u){let p=document.createElement("style");a&&p.setAttribute("nonce",a),p.textContent=v,this.shadowRoot.appendChild(p)}let h=o.getExternalStyles?.();if(h)for(let v of h){let p=Mu(v,i);a&&p.setAttribute("nonce",a),this.shadowRoot.appendChild(p)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},vo=class extends mo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let u=r.styles;this.styles=l?Hg(l,u):u,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},ks=class extends vo{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,l){let u=o+"-"+r.id;super(t,n,r,i,s,a,c,l,u),this.contentAttr=rD(u),this.hostAttr=oD(u)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var Ls=class e extends fo{supportsDOMEvents=!0;static makeCurrent(){mu(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=iD();return n==null?null:sD(n)}resetBaseElement(){yo=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return ho(document.cookie,t)}},yo=null;function iD(){return yo=yo||document.head.querySelector("base"),yo?yo.getAttribute("href"):null}function sD(e){return new URL(e,document.baseURI).pathname}var aD=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),zg=(()=>{class e extends go{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(x(le))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),$g=["alt","control","meta","shift"],cD={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},lD={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Gg=(()=>{class e extends go{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>qe().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),$g.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=cD[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),$g.forEach(s=>{if(s!==o){let a=lD[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(x(le))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();function Su(e,t){return Sg(M({rootComponent:e},uD(t)))}function uD(e){return{appProviders:[...gD,...e?.providers??[]],platformProviders:hD}}function dD(){Ls.makeCurrent()}function fD(){return new dt}function pD(){return vh(document),document}var hD=[{provide:mr,useValue:_u},{provide:Gl,useValue:dD,multi:!0},{provide:le,useFactory:pD}];var gD=[{provide:us,useValue:"root"},{provide:dt,useFactory:fD},{provide:Fs,useClass:zg,multi:!0,deps:[le]},{provide:Fs,useClass:Gg,multi:!0,deps:[le]},xu,Eu,wu,{provide:ar,useExisting:xu},{provide:Dn,useClass:aD},[]];var br=class{},_o=class{},Wt=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Vs=class{encodeKey(t){return qg(t)}encodeValue(t){return qg(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function mD(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var vD=/%(\d[a-f0-9])/gi,yD={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function qg(e){return encodeURIComponent(e).replace(vD,(t,n)=>yD[n]??t)}function js(e){return`${e}`}var Nt=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Vs,t.fromString){if(t.fromObject)throw new E(2805,!1);this.map=mD(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(js):[js(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(js(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(js(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Us=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function _D(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Wg(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Yg(e){return typeof Blob<"u"&&e instanceof Blob}function Zg(e){return typeof FormData<"u"&&e instanceof FormData}function CD(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Qg="Content-Type",Jg="Accept",Xg="X-Request-URL",em="text/plain",tm="application/json",bD=`${tm}, ${em}, */*`,Cr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(_D(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new Wt,this.context??=new Us,!this.params)this.params=new Nt,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Wg(this.body)||Yg(this.body)||Zg(this.body)||CD(this.body)?this.body:this.body instanceof Nt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Zg(this.body)?null:Yg(this.body)?this.body.type||null:Wg(this.body)?null:typeof this.body=="string"?em:this.body instanceof Nt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?tm:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,l=t.headers||this.headers,u=t.params||this.params,h=t.context??this.context;return t.setHeaders!==void 0&&(l=Object.keys(t.setHeaders).reduce((v,p)=>v.set(p,t.setHeaders[p]),l)),t.setParams&&(u=Object.keys(t.setParams).reduce((v,p)=>v.set(p,t.setParams[p]),u)),new e(n,r,s,{params:u,headers:l,context:h,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},En=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(En||{}),Mr=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new Wt,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Bs=class e extends Mr{constructor(t={}){super(t)}type=En.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Co=class e extends Mr{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=En.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},bo=class extends Mr{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},MD=200,DD=204;function Ou(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var $s=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Cr)i=n;else{let c;o.headers instanceof Wt?c=o.headers:c=new Wt(o.headers);let l;o.params&&(o.params instanceof Nt?l=o.params:l=new Nt({fromObject:o.params})),i=new Cr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:l,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=O(i).pipe(it(c=>this.handler.handle(c)));if(n instanceof Cr||o.observe==="events")return s;let a=s.pipe(be(c=>c instanceof Co));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(A(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new E(2806,!1);return c.body}));case"blob":return a.pipe(A(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new E(2807,!1);return c.body}));case"text":return a.pipe(A(c=>{if(c.body!==null&&typeof c.body!="string")throw new E(2808,!1);return c.body}));case"json":default:return a.pipe(A(c=>c.body))}case"response":return a;default:throw new E(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new Nt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Ou(o,r))}post(n,r,o={}){return this.request("POST",n,Ou(o,r))}put(n,r,o={}){return this.request("PUT",n,Ou(o,r))}static \u0275fac=function(r){return new(r||e)(x(br))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();var wD=new D("");function nm(e,t){return t(e)}function ED(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function ID(e,t,n){return(r,o)=>Te(n,()=>t(r,i=>e(i,o)))}var rm=new D(""),Tu=new D(""),om=new D(""),Au=new D("",{providedIn:"root",factory:()=>!0});function xD(){let e=null;return(t,n)=>{e===null&&(e=(y(rm,{optional:!0})??[]).reduceRight(ED,nm));let r=y(Ot);if(y(Au)){let i=r.add();return e(t,n).pipe(Vt(()=>r.remove(i)))}else return e(t,n)}}var Hs=(()=>{class e extends br{backend;injector;chain=null;pendingTasks=y(Ot);contributeToStability=y(Au);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Tu),...this.injector.get(om,[])]));this.chain=r.reduceRight((o,i)=>ID(o,i,this.injector),nm)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Vt(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(x(_o),x(ye))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();var SD=/^\)\]\}',?\n/,OD=RegExp(`^${Xg}:`,"m");function PD(e){return"responseURL"in e&&e.responseURL?e.responseURL:OD.test(e.getAllResponseHeaders())?e.getResponseHeader(Xg):null}var Pu=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new E(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?K(r.\u0275loadImpl()):O(null)).pipe(xe(()=>new $(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((w,T)=>s.setRequestHeader(w,T.join(","))),n.headers.has(Jg)||s.setRequestHeader(Jg,bD),!n.headers.has(Qg)){let w=n.detectContentTypeHeader();w!==null&&s.setRequestHeader(Qg,w)}if(n.responseType){let w=n.responseType.toLowerCase();s.responseType=w!=="json"?w:"text"}let a=n.serializeBody(),c=null,l=()=>{if(c!==null)return c;let w=s.statusText||"OK",T=new Wt(s.getAllResponseHeaders()),G=PD(s)||n.url;return c=new Bs({headers:T,status:s.status,statusText:w,url:G}),c},u=()=>{let{headers:w,status:T,statusText:G,url:Yo}=l(),ue=null;T!==DD&&(ue=typeof s.response>"u"?s.responseText:s.response),T===0&&(T=ue?MD:0);let za=T>=200&&T<300;if(n.responseType==="json"&&typeof ue=="string"){let Wv=ue;ue=ue.replace(SD,"");try{ue=ue!==""?JSON.parse(ue):null}catch(Yv){ue=Wv,za&&(za=!1,ue={error:Yv,text:ue})}}za?(i.next(new Co({body:ue,headers:w,status:T,statusText:G,url:Yo||void 0})),i.complete()):i.error(new bo({error:ue,headers:w,status:T,statusText:G,url:Yo||void 0}))},h=w=>{let{url:T}=l(),G=new bo({error:w,status:s.status||0,statusText:s.statusText||"Unknown Error",url:T||void 0});i.error(G)},v=!1,p=w=>{v||(i.next(l()),v=!0);let T={type:En.DownloadProgress,loaded:w.loaded};w.lengthComputable&&(T.total=w.total),n.responseType==="text"&&s.responseText&&(T.partialText=s.responseText),i.next(T)},_=w=>{let T={type:En.UploadProgress,loaded:w.loaded};w.lengthComputable&&(T.total=w.total),i.next(T)};return s.addEventListener("load",u),s.addEventListener("error",h),s.addEventListener("timeout",h),s.addEventListener("abort",h),n.reportProgress&&(s.addEventListener("progress",p),a!==null&&s.upload&&s.upload.addEventListener("progress",_)),s.send(a),i.next({type:En.Sent}),()=>{s.removeEventListener("error",h),s.removeEventListener("abort",h),s.removeEventListener("load",u),s.removeEventListener("timeout",h),n.reportProgress&&(s.removeEventListener("progress",p),a!==null&&s.upload&&s.upload.removeEventListener("progress",_)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(x(Dn))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),im=new D(""),TD="XSRF-TOKEN",AD=new D("",{providedIn:"root",factory:()=>TD}),ND="X-XSRF-TOKEN",RD=new D("",{providedIn:"root",factory:()=>ND}),Mo=class{},kD=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=ho(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(x(le),x(AD))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();function FD(e,t){let n=e.url.toLowerCase();if(!y(im)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=y(Mo).getToken(),o=y(RD);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var Nu=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Nu||{});function LD(e,t){return{\u0275kind:e,\u0275providers:t}}function Ru(...e){let t=[$s,Pu,Hs,{provide:br,useExisting:Hs},{provide:_o,useFactory:()=>y(wD,{optional:!0})??y(Pu)},{provide:Tu,useValue:FD,multi:!0},{provide:im,useValue:!0},{provide:Mo,useClass:kD}];for(let n of e)t.push(...n.\u0275providers);return dr(t)}var Kg=new D("");function ku(){return LD(Nu.LegacyInterceptors,[{provide:Kg,useFactory:xD},{provide:Tu,useExisting:Kg,multi:!0}])}var sm=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(x(le))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var F="primary",ko=Symbol("RouteTitle"),Uu=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Sn(e){return new Uu(e)}function hm(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function UD(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!ht(e[n],t[n]))return!1;return!0}function ht(e,t){let n=e?Bu(e):void 0,r=t?Bu(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!gm(e[o],t[o]))return!1;return!0}function Bu(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function gm(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function mm(e){return e.length>0?e[e.length-1]:null}function Jt(e){return _c(e)?e:vn(e)?K(Promise.resolve(e)):O(e)}var BD={exact:ym,subset:_m},vm={exact:HD,subset:$D,ignored:()=>!0};function am(e,t,n){return BD[n.paths](e.root,t.root,n.matrixParams)&&vm[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function HD(e,t){return ht(e,t)}function ym(e,t,n){if(!In(e.segments,t.segments)||!qs(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!ym(e.children[r],t.children[r],n))return!1;return!0}function $D(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>gm(e[n],t[n]))}function _m(e,t,n){return Cm(e,t,t.segments,n)}function Cm(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!In(o,n)||t.hasChildren()||!qs(o,n,r))}else if(e.segments.length===n.length){if(!In(e.segments,n)||!qs(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!_m(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!In(e.segments,o)||!qs(e.segments,o,r)||!e.children[F]?!1:Cm(e.children[F],t,i,r)}}function qs(e,t,n){return t.every((r,o)=>vm[n](e[o].parameters,r.parameters))}var mt=class{root;queryParams;fragment;_queryParamMap;constructor(t=new W([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Sn(this.queryParams),this._queryParamMap}toString(){return qD.serialize(this)}},W=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ws(this)}},Yt=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Sn(this.parameters),this._parameterMap}toString(){return Mm(this)}};function zD(e,t){return In(e,t)&&e.every((n,r)=>ht(n.parameters,t[r].parameters))}function In(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function GD(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===F&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==F&&(n=n.concat(t(o,r)))}),n}var On=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>new Zt,providedIn:"root"})}return e})(),Zt=class{parse(t){let n=new $u(t);return new mt(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Do(t.root,!0)}`,r=ZD(t.queryParams),o=typeof t.fragment=="string"?`#${WD(t.fragment)}`:"";return`${n}${r}${o}`}},qD=new Zt;function Ws(e){return e.segments.map(t=>Mm(t)).join("/")}function Do(e,t){if(!e.hasChildren())return Ws(e);if(t){let n=e.children[F]?Do(e.children[F],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==F&&r.push(`${o}:${Do(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=GD(e,(r,o)=>o===F?[Do(e.children[F],!1)]:[`${o}:${Do(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[F]!=null?`${Ws(e)}/${n[0]}`:`${Ws(e)}/(${n.join("//")})`}}function bm(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function zs(e){return bm(e).replace(/%3B/gi,";")}function WD(e){return encodeURI(e)}function Hu(e){return bm(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Ys(e){return decodeURIComponent(e)}function cm(e){return Ys(e.replace(/\+/g,"%20"))}function Mm(e){return`${Hu(e.path)}${YD(e.parameters)}`}function YD(e){return Object.entries(e).map(([t,n])=>`;${Hu(t)}=${Hu(n)}`).join("")}function ZD(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${zs(n)}=${zs(o)}`).join("&"):`${zs(n)}=${zs(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var QD=/^[^\/()?;#]+/;function Fu(e){let t=e.match(QD);return t?t[0]:""}var JD=/^[^\/()?;=#]+/;function KD(e){let t=e.match(JD);return t?t[0]:""}var XD=/^[^=?&#]+/;function ew(e){let t=e.match(XD);return t?t[0]:""}var tw=/^[^&#]+/;function nw(e){let t=e.match(tw);return t?t[0]:""}var $u=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new W([],{}):new W([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[F]=new W(t,n)),r}parseSegment(){let t=Fu(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new E(4009,!1);return this.capture(t),new Yt(Ys(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=KD(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=Fu(this.remaining);o&&(r=o,this.capture(r))}t[Ys(n)]=Ys(r)}parseQueryParam(t){let n=ew(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=nw(this.remaining);s&&(r=s,this.capture(r))}let o=cm(n),i=cm(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Fu(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new E(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=F);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[F]:new W([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new E(4011,!1)}};function Dm(e){return e.segments.length>0?new W([],{[F]:e}):e}function wm(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=wm(o);if(r===F&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new W(e.segments,t);return rw(n)}function rw(e){if(e.numberOfChildren===1&&e.children[F]){let t=e.children[F];return new W(e.segments.concat(t.segments),t.children)}return e}function xr(e){return e instanceof mt}function Em(e,t,n=null,r=null){let o=Im(e);return xm(o,t,n,r)}function Im(e){let t;function n(i){let s={};for(let c of i.children){let l=n(c);s[c.outlet]=l}let a=new W(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=Dm(r);return t??o}function xm(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Lu(o,o,o,n,r);let i=ow(t);if(i.toRoot())return Lu(o,o,new W([],{}),n,r);let s=iw(i,o,e),a=s.processChildren?Eo(s.segmentGroup,s.index,i.commands):Om(s.segmentGroup,s.index,i.commands);return Lu(o,s.segmentGroup,a,n,r)}function Qs(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function xo(e){return typeof e=="object"&&e!=null&&e.outlets}function Lu(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(u=>`${u}`):`${l}`});let s;e===t?s=n:s=Sm(e,t,n);let a=Dm(wm(s));return new mt(a,i,o)}function Sm(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Sm(i,t,n)}),new W(e.segments,r)}var Js=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Qs(r[0]))throw new E(4003,!1);let o=r.find(xo);if(o&&o!==mm(r))throw new E(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function ow(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Js(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Js(n,t,r)}var Er=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function iw(e,t,n){if(e.isAbsolute)return new Er(t,!0,0);if(!n)return new Er(t,!1,NaN);if(n.parent===null)return new Er(n,!0,0);let r=Qs(e.commands[0])?0:1,o=n.segments.length-1+r;return sw(n,o,e.numberOfDoubleDots)}function sw(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new E(4005,!1);o=r.segments.length}return new Er(r,!1,o-i)}function aw(e){return xo(e[0])?e[0].outlets:{[F]:e}}function Om(e,t,n){if(e??=new W([],{}),e.segments.length===0&&e.hasChildren())return Eo(e,t,n);let r=cw(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new W(e.segments.slice(0,r.pathIndex),{});return i.children[F]=new W(e.segments.slice(r.pathIndex),e.children),Eo(i,0,o)}else return r.match&&o.length===0?new W(e.segments,{}):r.match&&!e.hasChildren()?zu(e,t,n):r.match?Eo(e,0,o):zu(e,t,n)}function Eo(e,t,n){if(n.length===0)return new W(e.segments,{});{let r=aw(n),o={};if(Object.keys(r).some(i=>i!==F)&&e.children[F]&&e.numberOfChildren===1&&e.children[F].segments.length===0){let i=Eo(e.children[F],t,n);return new W(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Om(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new W(e.segments,o)}}function cw(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(xo(a))break;let c=`${a}`,l=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!um(c,l,s))return i;r+=2}else{if(!um(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function zu(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(xo(i)){let c=lw(i.outlets);return new W(r,c)}if(o===0&&Qs(n[0])){let c=e.segments[t];r.push(new Yt(c.path,lm(n[0]))),o++;continue}let s=xo(i)?i.outlets[F]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Qs(a)?(r.push(new Yt(s,lm(a))),o+=2):(r.push(new Yt(s,{})),o++)}return new W(r,{})}function lw(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=zu(new W([],{}),0,r))}),t}function lm(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function um(e,t,n){return e==n.path&&ht(t,n.parameters)}var Zs="imperative",pe=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(pe||{}),je=class{id;url;constructor(t,n){this.id=t,this.url=n}},Qt=class extends je{type=pe.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},ot=class extends je{urlAfterRedirects;type=pe.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Re=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(Re||{}),Sr=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Sr||{}),gt=class extends je{reason;code;type=pe.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},vt=class extends je{reason;code;type=pe.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},Or=class extends je{error;target;type=pe.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},So=class extends je{urlAfterRedirects;state;type=pe.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ks=class extends je{urlAfterRedirects;state;type=pe.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Xs=class extends je{urlAfterRedirects;state;shouldActivate;type=pe.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ea=class extends je{urlAfterRedirects;state;type=pe.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ta=class extends je{urlAfterRedirects;state;type=pe.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},na=class{route;type=pe.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},ra=class{route;type=pe.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},oa=class{snapshot;type=pe.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ia=class{snapshot;type=pe.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},sa=class{snapshot;type=pe.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},aa=class{snapshot;type=pe.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Pr=class{routerEvent;position;anchor;type=pe.Scroll;constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},Oo=class{},Tr=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function uw(e,t){return e.providers&&!e._injector&&(e._injector=lo(e.providers,t,`Route: ${e.path}`)),e._injector??t}function rt(e){return e.outlet||F}function dw(e,t){let n=e.filter(r=>rt(r)===t);return n.push(...e.filter(r=>rt(r)!==t)),n}function Fo(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var ca=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Fo(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Pn(this.rootInjector)}},Pn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new ca(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(x(ye))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),la=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Gu(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Gu(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=qu(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return qu(t,this._root).map(n=>n.value)}};function Gu(e,t){if(e===t.value)return t;for(let n of t.children){let r=Gu(e,n);if(r)return r}return null}function qu(e,t){if(e===t.value)return[t];for(let n of t.children){let r=qu(e,n);if(r.length)return r.unshift(t),r}return[]}var Le=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function wr(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var Po=class extends la{snapshot;constructor(t,n){super(t),this.snapshot=n,ed(this,t)}toString(){return this.snapshot.toString()}};function Pm(e){let t=fw(e),n=new me([new Yt("",{})]),r=new me({}),o=new me({}),i=new me({}),s=new me(""),a=new Rt(n,r,i,s,o,F,e,t.root);return a.snapshot=t.root,new Po(new Le(a,[]),t)}function fw(e){let t={},n={},r={},o="",i=new xn([],t,r,o,n,F,e,null,{});return new To("",new Le(i,[]))}var Rt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(A(l=>l[ko]))??O(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(A(t=>Sn(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(A(t=>Sn(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function ua(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:M(M({},t.params),e.params),data:M(M({},t.data),e.data),resolve:M(M(M(M({},e.data),t.data),o?.data),e._resolvedData)}:r={params:M({},e.params),data:M({},e.data),resolve:M(M({},e.data),e._resolvedData??{})},o&&Am(o)&&(r.resolve[ko]=o.title),r}var xn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[ko]}constructor(t,n,r,o,i,s,a,c,l){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Sn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Sn(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},To=class extends la{url;constructor(t,n){super(n),this.url=t,ed(this,n)}toString(){return Tm(this._root)}};function ed(e,t){t.value._routerState=e,t.children.forEach(n=>ed(e,n))}function Tm(e){let t=e.children.length>0?` { ${e.children.map(Tm).join(", ")} } `:"";return`${e.value}${t}`}function ju(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,ht(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),ht(t.params,n.params)||e.paramsSubject.next(n.params),UD(t.url,n.url)||e.urlSubject.next(n.url),ht(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Wu(e,t){let n=ht(e.params,t.params)&&zD(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Wu(e.parent,t.parent))}function Am(e){return typeof e.title=="string"||e.title===null}var Nm=new D(""),Lo=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=F;activateEvents=new ae;deactivateEvents=new ae;attachEvents=new ae;detachEvents=new ae;routerOutletData=lh(void 0);parentContexts=y(Pn);location=y(_r);changeDetector=y(bn);inputBinder=y(jo,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new E(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new E(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new E(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new E(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Yu(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=ce({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[$t]})}return e})(),Yu=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===Rt?this.route:t===Pn?this.childContexts:t===Nm?this.outletData:this.parent.get(t,n)}},jo=new D(""),td=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=on([r.queryParams,r.params,r.data]).pipe(xe(([i,s,a],c)=>(a=M(M(M({},i),s),a),c===0?O(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=Og(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),nd=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=ne({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&C(0,"router-outlet")},dependencies:[Lo],encapsulation:2})}return e})();function rd(e){let t=e.children&&e.children.map(rd),n=t?U(M({},e),{children:t}):M({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==F&&(n.component=nd),n}function pw(e,t,n){let r=Ao(e,t._root,n?n._root:void 0);return new Po(r,t)}function Ao(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=hw(e,t,n);return new Le(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>Ao(e,a)),s}}let r=gw(t.value),o=t.children.map(i=>Ao(e,i));return new Le(r,o)}}function hw(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Ao(e,r,o);return Ao(e,r)})}function gw(e){return new Rt(new me(e.url),new me(e.params),new me(e.queryParams),new me(e.fragment),new me(e.data),e.outlet,e.component,e)}var Ar=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},Rm="ngNavigationCancelingError";function da(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=xr(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=km(!1,Re.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function km(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[Rm]=!0,n.cancellationCode=t,n}function mw(e){return Fm(e)&&xr(e.url)}function Fm(e){return!!e&&e[Rm]}var vw=(e,t,n,r)=>A(o=>(new Zu(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Zu=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),ju(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=wr(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=wr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=wr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=wr(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new aa(i.value.snapshot))}),t.children.length&&this.forwardEvent(new ia(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(ju(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),ju(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},fa=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Ir=class{component;route;constructor(t,n){this.component=t,this.route=n}};function yw(e,t,n){let r=e._root,o=t?t._root:null;return wo(r,o,n,[r.value])}function _w(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Rr(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!rp(e)?e:t.get(e):r}function wo(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=wr(t);return e.children.forEach(s=>{Cw(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Io(a,n.getContext(s),o)),o}function Cw(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=bw(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new fa(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?wo(e,t,a?a.children:null,r,o):wo(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Ir(a.outlet.component,s))}else s&&Io(t,a,o),o.canActivateChecks.push(new fa(r)),i.component?wo(e,null,a?a.children:null,r,o):wo(e,null,n,r,o);return o}function bw(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!In(e.url,t.url);case"pathParamsOrQueryParamsChange":return!In(e.url,t.url)||!ht(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Wu(e,t)||!ht(e.queryParams,t.queryParams);case"paramsChange":default:return!Wu(e,t)}}function Io(e,t,n){let r=wr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?Io(s,t.children.getContext(i),n):Io(s,null,n):Io(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new Ir(t.outlet.component,o)):n.canDeactivateChecks.push(new Ir(null,o)):n.canDeactivateChecks.push(new Ir(null,o))}function Vo(e){return typeof e=="function"}function Mw(e){return typeof e=="boolean"}function Dw(e){return e&&Vo(e.canLoad)}function ww(e){return e&&Vo(e.canActivate)}function Ew(e){return e&&Vo(e.canActivateChild)}function Iw(e){return e&&Vo(e.canDeactivate)}function xw(e){return e&&Vo(e.canMatch)}function Lm(e){return e instanceof Ct||e?.name==="EmptyError"}var Gs=Symbol("INITIAL_VALUE");function Nr(){return xe(e=>on(e.map(t=>t.pipe(bt(1),wc(Gs)))).pipe(A(t=>{for(let n of t)if(n!==!0){if(n===Gs)return Gs;if(n===!1||Sw(n))return n}return!0}),be(t=>t!==Gs),bt(1)))}function Sw(e){return xr(e)||e instanceof Ar}function Ow(e,t){return oe(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?O(U(M({},n),{guardsResult:!0})):Pw(s,r,o,e).pipe(oe(a=>a&&Mw(a)?Tw(r,i,e,t):O(a)),A(a=>U(M({},n),{guardsResult:a})))})}function Pw(e,t,n,r){return K(e).pipe(oe(o=>Fw(o.component,o.route,n,t,r)),Mt(o=>o!==!0,!0))}function Tw(e,t,n,r){return K(t).pipe(it(o=>qn(Nw(o.route.parent,r),Aw(o.route,r),kw(e,o.path,n),Rw(e,o.route,n))),Mt(o=>o!==!0,!0))}function Aw(e,t){return e!==null&&t&&t(new sa(e)),O(!0)}function Nw(e,t){return e!==null&&t&&t(new oa(e)),O(!0)}function Rw(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return O(!0);let o=r.map(i=>xi(()=>{let s=Fo(t)??n,a=Rr(i,s),c=ww(a)?a.canActivate(t,e):Te(s,()=>a(t,e));return Jt(c).pipe(Mt())}));return O(o).pipe(Nr())}function kw(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>_w(s)).filter(s=>s!==null).map(s=>xi(()=>{let a=s.guards.map(c=>{let l=Fo(s.node)??n,u=Rr(c,l),h=Ew(u)?u.canActivateChild(r,e):Te(l,()=>u(r,e));return Jt(h).pipe(Mt())});return O(a).pipe(Nr())}));return O(i).pipe(Nr())}function Fw(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return O(!0);let s=i.map(a=>{let c=Fo(t)??o,l=Rr(a,c),u=Iw(l)?l.canDeactivate(e,t,n,r):Te(c,()=>l(e,t,n,r));return Jt(u).pipe(Mt())});return O(s).pipe(Nr())}function Lw(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return O(!0);let i=o.map(s=>{let a=Rr(s,e),c=Dw(a)?a.canLoad(t,n):Te(e,()=>a(t,n));return Jt(c)});return O(i).pipe(Nr(),jm(r))}function jm(e){return hc(ve(t=>{if(typeof t!="boolean")throw da(e,t)}),A(t=>t===!0))}function jw(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return O(!0);let i=o.map(s=>{let a=Rr(s,e),c=xw(a)?a.canMatch(t,n):Te(e,()=>a(t,n));return Jt(c)});return O(i).pipe(Nr(),jm(r))}var No=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},Ro=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function Dr(e){return zn(new No(e))}function Vw(e){return zn(new E(4e3,!1))}function Uw(e){return zn(km(!1,Re.GuardRejected))}var Qu=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return O(r);if(o.numberOfChildren>1||!o.children[F])return Vw(`${t.redirectTo}`);o=o.children[F]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:l,routeConfig:u,url:h,outlet:v,params:p,data:_,title:w}=o,T=Te(i,()=>a({params:p,data:_,queryParams:c,fragment:l,routeConfig:u,url:h,outlet:v,title:w}));if(T instanceof mt)throw new Ro(T);n=T}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new Ro(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new mt(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new W(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new E(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},Ju={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Bw(e,t,n,r,o){let i=Vm(e,t,n);return i.matched?(r=uw(t,r),jw(r,t,n,o).pipe(A(s=>s===!0?i:M({},Ju)))):O(i)}function Vm(e,t,n){if(t.path==="**")return Hw(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?M({},Ju):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||hm)(n,e,t);if(!o)return M({},Ju);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?M(M({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function Hw(e){return{matched:!0,parameters:e.length>0?mm(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function dm(e,t,n,r){return n.length>0&&Gw(e,n,r)?{segmentGroup:new W(t,zw(r,new W(n,e.children))),slicedSegments:[]}:n.length===0&&qw(e,n,r)?{segmentGroup:new W(e.segments,$w(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new W(e.segments,e.children),slicedSegments:n}}function $w(e,t,n,r){let o={};for(let i of n)if(ha(e,t,i)&&!r[rt(i)]){let s=new W([],{});o[rt(i)]=s}return M(M({},r),o)}function zw(e,t){let n={};n[F]=t;for(let r of e)if(r.path===""&&rt(r)!==F){let o=new W([],{});n[rt(r)]=o}return n}function Gw(e,t,n){return n.some(r=>ha(e,t,r)&&rt(r)!==F)}function qw(e,t,n){return n.some(r=>ha(e,t,r))}function ha(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function Ww(e,t,n){return t.length===0&&!e.children[n]}var Ku=class{};function Yw(e,t,n,r,o,i,s="emptyOnly"){return new Xu(e,t,n,r,o,s,i).recognize()}var Zw=31,Xu=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Qu(this.urlSerializer,this.urlTree)}noMatchError(t){return new E(4002,`'${t.segmentGroup}'`)}recognize(){let t=dm(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(A(({children:n,rootSnapshot:r})=>{let o=new Le(r,n),i=new To("",o),s=Em(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new xn([],Object.freeze({}),Object.freeze(M({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),F,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,F,n).pipe(A(r=>({children:r,rootSnapshot:n})),ke(r=>{if(r instanceof Ro)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof No?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(A(s=>s instanceof Le?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return K(i).pipe(it(s=>{let a=r.children[s],c=dw(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Dc((s,a)=>(s.push(...a),s)),jt(null),bc(),oe(s=>{if(s===null)return Dr(r);let a=Um(s);return Qw(a),O(a)}))}processSegment(t,n,r,o,i,s,a){return K(n).pipe(it(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(ke(l=>{if(l instanceof No)return O(null);throw l}))),Mt(c=>!!c),ke(c=>{if(Lm(c))return Ww(r,o,i)?O(new Ku):Dr(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return rt(r)!==s&&(s===F||!ha(o,i,r))?Dr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):Dr(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:u,positionalParamSegments:h,remainingSegments:v}=Vm(n,o,i);if(!c)return Dr(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Zw&&(this.allowRedirects=!1));let p=new xn(i,l,Object.freeze(M({},this.urlTree.queryParams)),this.urlTree.fragment,fm(o),rt(o),o.component??o._loadedComponent??null,o,pm(o)),_=ua(p,a,this.paramsInheritanceStrategy);p.params=Object.freeze(_.params),p.data=Object.freeze(_.data);let w=this.applyRedirects.applyRedirectCommands(u,o.redirectTo,h,p,t);return this.applyRedirects.lineralizeSegments(o,w).pipe(oe(T=>this.processSegment(t,r,n,T.concat(v),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=Bw(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(xe(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(xe(({routes:l})=>{let u=r._loadedInjector??t,{parameters:h,consumedSegments:v,remainingSegments:p}=c,_=new xn(v,h,Object.freeze(M({},this.urlTree.queryParams)),this.urlTree.fragment,fm(r),rt(r),r.component??r._loadedComponent??null,r,pm(r)),w=ua(_,s,this.paramsInheritanceStrategy);_.params=Object.freeze(w.params),_.data=Object.freeze(w.data);let{segmentGroup:T,slicedSegments:G}=dm(n,v,p,l);if(G.length===0&&T.hasChildren())return this.processChildren(u,l,T,_).pipe(A(ue=>new Le(_,ue)));if(l.length===0&&G.length===0)return O(new Le(_,[]));let Yo=rt(r)===i;return this.processSegment(u,l,T,G,Yo?F:i,!0,_).pipe(A(ue=>new Le(_,ue instanceof Le?[ue]:[])))}))):Dr(n)))}getChildConfig(t,n,r){return n.children?O({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?O({routes:n._loadedRoutes,injector:n._loadedInjector}):Lw(t,n,r,this.urlSerializer).pipe(oe(o=>o?this.configLoader.loadChildren(t,n).pipe(ve(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):Uw(n))):O({routes:[],injector:t})}};function Qw(e){e.sort((t,n)=>t.value.outlet===F?-1:n.value.outlet===F?1:t.value.outlet.localeCompare(n.value.outlet))}function Jw(e){let t=e.value.routeConfig;return t&&t.path===""}function Um(e){let t=[],n=new Set;for(let r of e){if(!Jw(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=Um(r.children);t.push(new Le(r.value,o))}return t.filter(r=>!n.has(r))}function fm(e){return e.data||{}}function pm(e){return e.resolve||{}}function Kw(e,t,n,r,o,i){return oe(s=>Yw(e,t,n,r,s.extractedUrl,o,i).pipe(A(({state:a,tree:c})=>U(M({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function Xw(e,t){return oe(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return O(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of Bm(c))s.add(l);let a=0;return K(s).pipe(it(c=>i.has(c)?eE(c,r,e,t):(c.data=ua(c,c.parent,e).resolve,O(void 0))),ve(()=>a++),Wn(1),oe(c=>a===s.size?O(n):Ee))})}function Bm(e){let t=e.children.map(n=>Bm(n)).flat();return[e,...t]}function eE(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Am(o)&&(i[ko]=o.title),tE(i,e,t,r).pipe(A(s=>(e._resolvedData=s,e.data=ua(e,e.parent,n).resolve,null)))}function tE(e,t,n,r){let o=Bu(e);if(o.length===0)return O({});let i={};return K(o).pipe(oe(s=>nE(e[s],t,n,r).pipe(Mt(),ve(a=>{if(a instanceof Ar)throw da(new Zt,a);i[s]=a}))),Wn(1),A(()=>i),ke(s=>Lm(s)?Ee:zn(s)))}function nE(e,t,n,r){let o=Fo(t)??r,i=Rr(e,o),s=i.resolve?i.resolve(t,n):Te(o,()=>i(t,n));return Jt(s)}function Vu(e){return xe(t=>{let n=e(t);return n?K(n).pipe(A(()=>t)):O(t)})}var od=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===F);return r}getResolvedTitleForRoute(n){return n.data[ko]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>y(Hm),providedIn:"root"})}return e})(),Hm=(()=>{class e extends od{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(x(sm))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Tn=new D("",{providedIn:"root",factory:()=>({})}),An=new D(""),ga=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=y(wg);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return O(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=Jt(n.loadComponent()).pipe(A(zm),ve(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),Vt(()=>{this.componentLoaders.delete(n)})),o=new Hn(r,()=>new re).pipe(Bn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return O({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=$m(r,this.compiler,n,this.onLoadEndListener).pipe(Vt(()=>{this.childrenLoaders.delete(r)})),s=new Hn(i,()=>new re).pipe(Bn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function $m(e,t,n,r){return Jt(e.loadChildren()).pipe(A(zm),oe(o=>o instanceof lu||Array.isArray(o)?O(o):K(t.compileModuleAsync(o))),A(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(An,[],{optional:!0,self:!0}).flat()),{routes:s.map(rd),injector:i}}))}function rE(e){return e&&typeof e=="object"&&"default"in e}function zm(e){return rE(e)?e.default:e}var ma=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>y(oE),providedIn:"root"})}return e})(),oE=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),id=new D(""),sd=new D("");function Gm(e,t,n){let r=e.get(sd),o=e.get(le);return e.get(te).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(l=>setTimeout(l));let i,s=new Promise(l=>{i=l}),a=o.startViewTransition(()=>(i(),iE(e))),{onViewTransitionCreated:c}=r;return c&&Te(e,()=>c({transition:a,from:t,to:n})),s})}function iE(e){return new Promise(t=>{Yl({read:()=>setTimeout(t)},{injector:e})})}var ad=new D(""),va=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new re;transitionAbortSubject=new re;configLoader=y(ga);environmentInjector=y(ye);destroyRef=y(gr);urlSerializer=y(On);rootContexts=y(Pn);location=y(qt);inputBindingEnabled=y(jo,{optional:!0})!==null;titleStrategy=y(od);options=y(Tn,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=y(ma);createViewTransition=y(id,{optional:!0});navigationErrorHandler=y(ad,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>O(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new na(o)),r=o=>this.events.next(new ra(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(U(M({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new me(null),this.transitions.pipe(be(r=>r!==null),xe(r=>{let o=!1,i=!1;return O(r).pipe(xe(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Re.SupersededByNewNavigation),Ee;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?U(M({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let l="";return this.events.next(new vt(s.id,this.urlSerializer.serialize(s.rawUrl),l,Sr.IgnoredSameUrlNavigation)),s.resolve(!1),Ee}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return O(s).pipe(xe(l=>(this.events.next(new Qt(l.id,this.urlSerializer.serialize(l.extractedUrl),l.source,l.restoredState)),l.id!==this.navigationId?Ee:Promise.resolve(l))),Kw(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),ve(l=>{r.targetSnapshot=l.targetSnapshot,r.urlAfterRedirects=l.urlAfterRedirects,this.currentNavigation=U(M({},this.currentNavigation),{finalUrl:l.urlAfterRedirects});let u=new So(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(u)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:l,extractedUrl:u,source:h,restoredState:v,extras:p}=s,_=new Qt(l,this.urlSerializer.serialize(u),h,v);this.events.next(_);let w=Pm(this.rootComponentType).snapshot;return this.currentTransition=r=U(M({},s),{targetSnapshot:w,urlAfterRedirects:u,extras:U(M({},p),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,O(r)}else{let l="";return this.events.next(new vt(s.id,this.urlSerializer.serialize(s.extractedUrl),l,Sr.IgnoredByUrlHandlingStrategy)),s.resolve(!1),Ee}}),ve(s=>{let a=new Ks(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),A(s=>(this.currentTransition=r=U(M({},s),{guards:yw(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),Ow(this.environmentInjector,s=>this.events.next(s)),ve(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw da(this.urlSerializer,s.guardsResult);let a=new Xs(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),be(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",Re.GuardRejected),!1)),Vu(s=>{if(s.guards.canActivateChecks.length!==0)return O(s).pipe(ve(a=>{let c=new ea(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),xe(a=>{let c=!1;return O(a).pipe(Xw(this.paramsInheritanceStrategy,this.environmentInjector),ve({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",Re.NoDataFromResolver)}}))}),ve(a=>{let c=new ta(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),Vu(s=>{let a=c=>{let l=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&l.push(this.configLoader.loadComponent(c.routeConfig).pipe(ve(u=>{c.component=u}),A(()=>{})));for(let u of c.children)l.push(...a(u));return l};return on(a(s.targetSnapshot.root)).pipe(jt(null),bt(1))}),Vu(()=>this.afterPreactivation()),xe(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?K(c).pipe(A(()=>r)):O(r)}),A(s=>{let a=pw(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=U(M({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),ve(()=>{this.events.next(new Oo)}),vw(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),bt(1),ve({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new ot(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),Gr(this.transitionAbortSubject.pipe(ve(s=>{throw s}))),Vt(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",Re.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),ke(s=>{if(this.destroyed)return r.resolve(!1),Ee;if(i=!0,Fm(s))this.events.next(new gt(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),mw(s)?this.events.next(new Tr(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new Or(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=Te(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof Ar){let{message:l,cancellationCode:u}=da(this.urlSerializer,c);this.events.next(new gt(r.id,this.urlSerializer.serialize(r.extractedUrl),l,u)),this.events.next(new Tr(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return Ee}))}))}cancelNavigationTransition(n,r,o){let i=new gt(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function sE(e){return e!==Zs}var qm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>y(aE),providedIn:"root"})}return e})(),pa=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},aE=(()=>{class e extends pa{static \u0275fac=(()=>{let n;return function(o){return(n||(n=mn(e)))(o||e)}})();static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Wm=(()=>{class e{urlSerializer=y(On);options=y(Tn,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=y(qt);urlHandlingStrategy=y(ma);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new mt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof mt?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=Pm(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>y(cE),providedIn:"root"})}return e})(),cE=(()=>{class e extends Wm{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof Qt?this.updateStateMemento():n instanceof vt?this.commitTransition(r):n instanceof So?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Oo?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof gt&&(n.code===Re.GuardRejected||n.code===Re.NoDataFromResolver)?this.restoreHistory(r):n instanceof Or?this.restoreHistory(r,!0):n instanceof ot&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=M(M({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=M(M({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=mn(e)))(o||e)}})();static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ya(e,t){e.events.pipe(be(n=>n instanceof ot||n instanceof gt||n instanceof Or||n instanceof vt),A(n=>n instanceof ot||n instanceof vt?0:(n instanceof gt?n.code===Re.Redirect||n.code===Re.SupersededByNewNavigation:!1)?2:1),be(n=>n!==2),bt(1)).subscribe(()=>{t()})}var lE={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},uE={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},kt=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=y(ag);stateManager=y(Wm);options=y(Tn,{optional:!0})||{};pendingTasks=y(Ot);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=y(va);urlSerializer=y(On);location=y(qt);urlHandlingStrategy=y(ma);_events=new re;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=y(qm);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=y(An,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!y(jo,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new X;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof gt&&r.code!==Re.Redirect&&r.code!==Re.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof ot)this.navigated=!0;else if(r instanceof Tr){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=M({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||sE(o.source)},s);this.scheduleNavigation(a,Zs,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}fE(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Zs,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=M({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(rd),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,u=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":u=M(M({},this.currentUrlTree.queryParams),i);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=i||null}u!==null&&(u=this.removeEmptyProps(u));let h;try{let v=o?o.snapshot:this.routerState.snapshot.root;h=Im(v)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),h=this.currentUrlTree.root}return xm(h,n,u,l??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=xr(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Zs,null,r)}navigate(n,r={skipLocationChange:!1}){return dE(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=M({},lE):r===!1?o=M({},uE):o=r,xr(n))return am(this.currentUrlTree,n,o);let i=this.parseUrl(n);return am(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((h,v)=>{a=h,c=v});let u=this.pendingTasks.add();return ya(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(h=>Promise.reject(h))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function dE(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new E(4008,!1)}function fE(e){return!(e instanceof Oo)&&!(e instanceof Tr)}var Uo=class{};var Ym=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,r,o,i){this.router=n,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(be(n=>n instanceof ot),it(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=lo(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return K(o).pipe(Gn())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=O(null);let i=o.pipe(oe(s=>s===null?O(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return K([i,s]).pipe(Gn())}else return i})}static \u0275fac=function(r){return new(r||e)(x(kt),x(ye),x(Uo),x(ga))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Zm=new D(""),pE=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Qt?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof ot?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof vt&&n.code===Sr.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Pr&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Pr(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){Kh()};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();function ld(e,...t){return dr([{provide:An,multi:!0,useValue:e},[],{provide:Rt,useFactory:Qm,deps:[kt]},{provide:Ds,multi:!0,useFactory:Jm},t.map(n=>n.\u0275providers)])}function Qm(e){return e.routerState.root}function Bo(e,t){return{\u0275kind:e,\u0275providers:t}}function Jm(){let e=y($e);return t=>{let n=e.get(hn);if(t!==n.components[0])return;let r=e.get(kt),o=e.get(Km);e.get(ud)===1&&r.initialNavigation(),e.get(tv,null,L.Optional)?.setUpPreloading(),e.get(Zm,null,L.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var Km=new D("",{factory:()=>new re}),ud=new D("",{providedIn:"root",factory:()=>1});function Xm(){let e=[{provide:ud,useValue:0},uu(()=>{let t=y($e);return t.get(vu,Promise.resolve()).then(()=>new Promise(r=>{let o=t.get(kt),i=t.get(Km);ya(o,()=>{r(!0)}),t.get(va).afterPreactivation=()=>(r(!0),i.closed?O(void 0):i),o.initialNavigation()}))})];return Bo(2,e)}function ev(){let e=[uu(()=>{y(kt).setUpLocationChangeListener()}),{provide:ud,useValue:2}];return Bo(3,e)}var tv=new D("");function nv(e){return Bo(0,[{provide:tv,useExisting:Ym},{provide:Uo,useExisting:e}])}function rv(){return Bo(8,[td,{provide:jo,useExisting:td}])}function ov(e){yr("NgRouterViewTransitions");let t=[{provide:id,useValue:Gm},{provide:sd,useValue:M({skipNextTransition:!!e?.skipInitialTransition},e)}];return Bo(9,t)}var iv=[qt,{provide:On,useClass:Zt},kt,Pn,{provide:Rt,useFactory:Qm,deps:[kt]},ga,[]],dd=(()=>{class e{constructor(){}static forRoot(n,r){return{ngModule:e,providers:[iv,[],{provide:An,multi:!0,useValue:n},[],r?.errorHandler?{provide:ad,useValue:r.errorHandler}:[],{provide:Tn,useValue:r||{}},r?.useHash?gE():mE(),hE(),r?.preloadingStrategy?nv(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?vE(r):[],r?.bindToComponentInputs?rv().\u0275providers:[],r?.enableViewTransitions?ov().\u0275providers:[],yE()]}}static forChild(n){return{ngModule:e,providers:[{provide:An,multi:!0,useValue:n}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=et({type:e});static \u0275inj=Je({})}return e})();function hE(){return{provide:Zm,useFactory:()=>{let e=y(Lg),t=y(te),n=y(Tn),r=y(va),o=y(On);return n.scrollOffset&&e.setOffset(n.scrollOffset),new pE(o,r,e,t,n)}}}function gE(){return{provide:At,useClass:yu}}function mE(){return{provide:At,useClass:Ps}}function vE(e){return[e.initialNavigation==="disabled"?ev().\u0275providers:[],e.initialNavigation==="enabledBlocking"?Xm().\u0275providers:[]]}var cd=new D("");function yE(){return[{provide:cd,useFactory:Jm},{provide:Ds,multi:!0,useExisting:cd}]}var _a=class e{constructor(t){this.http=t}GITHUB_API_URL="https://.api.github.com";USERNAME="macamisp";FEATURED_REPOS=["ICET-110Hospital-Management-Frontend","Nitro-gass-FRONTEND","simple_weather","possystem"];getRepositories(){let t=`${this.GITHUB_API_URL}/users/${this.USERNAME}/repos`;return this.http.get(t).pipe(Mc(2),A(n=>this.processRepositories(n)),ke(this.handleError.bind(this)))}getFeaturedRepositories(){return this.getRepositories().pipe(A(t=>t.filter(n=>n.isFeatured)))}getRepositoriesByLanguage(t){return this.getRepositories().pipe(A(n=>n.filter(r=>r.language?.toLowerCase()===t.toLowerCase())))}processRepositories(t){return t.filter(n=>this.shouldIncludeRepository(n)).map(n=>this.transformRepository(n)).sort((n,r)=>n.isFeatured&&!r.isFeatured?-1:!n.isFeatured&&r.isFeatured?1:n.stars!==r.stars?r.stars-n.stars:r.lastUpdated.getTime()-n.lastUpdated.getTime())}shouldIncludeRepository(t){return!t.fork&&!t.archived&&!t.disabled&&!t.private&&t.size>0&&(t.description!==null||t.language!==null)}transformRepository(t){let n=this.formatRepositoryName(t.name),r=this.generatePlaceholderImage(t.name,t.language);return{id:t.id,name:t.name,displayName:n,description:t.description||`${t.language} project`,githubUrl:t.html_url,liveUrl:t.homepage||null,language:t.language||"Unknown",stars:t.stargazers_count,forks:t.forks_count,lastUpdated:new Date(t.updated_at),created:new Date(t.created_at),topics:t.topics||[],imageUrl:r,isFeatured:this.FEATURED_REPOS.includes(t.name)}}formatRepositoryName(t){return t.replace(/[-_]/g," ").replace(/\b\w/g,n=>n.toUpperCase()).replace(/\bFrontend\b/gi,"Frontend").replace(/\bBackend\b/gi,"Backend").replace(/\bApi\b/gi,"API").replace(/\bUi\b/gi,"UI").replace(/\bPos\b/gi,"POS")}generatePlaceholderImage(t,n){let r={JavaScript:"4CAF50",TypeScript:"2196F3",HTML:"FF5722",CSS:"9C27B0",Java:"FF9800",Python:"3F51B5",Unknown:"607D8B"},o=r[n]||r.Unknown,i=encodeURIComponent(t.replace(/[-_]/g," "));return`https://via.placeholder.com/400x250/${o}/FFFFFF?text=${i}`}handleError(t){let n={message:t.error?.message||"Failed to fetch repositories",status:t.status,timestamp:new Date};return console.error("GitHub API Error:",n),O([])}static \u0275fac=function(n){return new(n||e)(x($s))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})};var _e=class e{constructor(t){this.githubService=t}personalInfo={name:"Sachin Prabashwara Samarawickrama",title:"Trainee Full Stack Developer",email:"<EMAIL>",phone:"+94788179855",whatsapp:"+94788179855",location:"Panadura, Sri Lanka",dateOfBirth:"February 8, 2004",linkedin:"https://www.linkedin.com/in/sachin-samarawickrama-*********/",github:"https://github.com/macamisp",facebook:"https://www.facebook.com/profile.php?id=100069446774540",instagram:"https://www.instagram.com/macami_impres/",threads:"https://www.threads.com/@macami_impres?xmt=AQGzSQbxGWB_x5fu5c2M7HjJLu0rVs6W7XoMbkST3RdUPDs",telegram:"https://t.me/MACAMI_1X1080SP991",bio:"Software Engineer Trainee with expertise in full-stack development and website design. Passionate about emerging technologies, specializing in object-oriented programming and modern development frameworks. Combines technical skills with business acumen through successful projects. Strong leadership qualities with experience in organizing events and coordinating teams."};skills=[{name:"Java",level:85,category:"Backend"},{name:"JavaScript",level:80,category:"Frontend"},{name:"TypeScript",level:75,category:"Frontend"},{name:"HTML5",level:90,category:"Frontend"},{name:"CSS3",level:85,category:"Frontend"},{name:"SCSS",level:80,category:"Frontend"},{name:"Angular",level:80,category:"Frontend"},{name:"Spring Boot",level:75,category:"Backend"},{name:"Git",level:85,category:"Tools"},{name:"GitHub",level:85,category:"Tools"},{name:"Adobe Photoshop",level:80,category:"Design"},{name:"Adobe Premiere Pro",level:75,category:"Design"},{name:"Adobe After Effects",level:70,category:"Design"},{name:"MS Office",level:85,category:"Productivity"},{name:"MS Excel",level:80,category:"Productivity"},{name:"MS Word",level:90,category:"Productivity"},{name:"MS PowerPoint",level:85,category:"Productivity"}];projects=[{id:"we-care",title:"WE CARE - Hospital Management System",description:"Frontend development for hospital management system ensuring high-quality and user-friendly interfaces. Built with modern web technologies for optimal performance.",technologies:["HTML","CSS","TypeScript","Bootstrap","Angular"],githubUrl:"https://github.com/macamisp/ICET-110Hospital-Management-Frontend",imageUrl:"/Image_fx.jpg",featured:!0},{id:"nitro-gass",title:"NITRO GASS",description:"Full-stack web application using simple HTML, CSS, and JS to fetch data from a pre-made API and loading data using Spring Boot backend and Angular frontend.",technologies:["HTML","CSS","TypeScript","Boostrap","Spring Boot","Angular"],githubUrl:"https://github.com/macamisp/Nitro-gas-FRONTEND",imageUrl:"/Image_fx (1).jpg",featured:!0},{id:"burger-shop",title:"Burger Shop POS System",description:"Point of sale system using HTML, CSS, and JS. Simple POS system only using JS arrays and storing in local storage customer registration and bill printing system.",technologies:["HTML","CSS","JavaScript","Local Storage"],githubUrl:"https://macamisp.github.io/posesystem/",imageUrl:"/Image_fx (12).jpg",featured:!1},{id:"portfolio-website",title:"Personal Portfolio Website",description:"Modern Angular portfolio website with professional animations, GitHub integration, and responsive design. Features background animations, project filtering, and mobile-optimized layout",technologies:["Angular","TypeScript","SCSS","GitHub API","Responsive Design"],githubUrl:"https://github.com/macamisp/my-portfolio",imageUrl:"/image.png",featured:!1},{id:"weather-app",title:"Weather App",description:"Simple weather app to fetch and get data from a pre-made API and loading real-time weather data with a simple UI/UX design.",technologies:["HTML","CSS","JavaScript","API Integration"],githubUrl:"https://github.com/macamisp/simple_weather",imageUrl:"/Image_fx (11).jpg",featured:!1},{id:"student-management",title:"Student Management System",description:"Comprehensive student management system for educational institutions with student registration, course management, and academic tracking features.",technologies:["Java","Spring Boot","MySQL","HTML","CSS"],githubUrl:"https://github.com/macamisp/student-management-system",imageUrl:"/Image_fx (5).jpg",featured:!1},{id:"e-commerce-platform",title:"E-Commerce Platform",description:"Full-stack e-commerce solution with product catalog, shopping cart, user authentication, and payment integration for online retail businesses.",technologies:["React","Node.js","Express","MongoDB","Stripe API"],githubUrl:"https://github.com/macamisp/ecommerce-platform",imageUrl:"/Image_fx (13).jpg",featured:!1},{id:"countrySearch",title:"Country Search Application",description:"Simple Country search app to fetch and get data from a pre-made API and loading real-time Country data with a simple UI/UX design.",technologies:["HTML5","CSS","JavaScript"],githubUrl:"https://github.com/macamisp/search",imageUrl:"image(14)fx.png",featured:!1},{id:"defenceSystem",title:"Defence System",description:"using the java core consepts Like OOP to create  defence system.",technologies:["JAVA","netbeans"],githubUrl:"https://github.com/macamisp/defenceSystem",imageUrl:"/Image_fx (15).jpg",featured:!1},{id:"walan-kade",title:"Walankade pos system",description:"Walankade pos system using javaFX.",technologies:["JAVA FX","scene builder"],githubUrl:"https://github.com/macamisp/walan_kade",imageUrl:"/How-Much-Does-a-Restaurant-POS-System-Cost.png",featured:!1},{id:"Wallpaper.com",title:"Wallpaper.com",description:"Created a modern and responsive website using HTML, CSS, and JavaScript that allows users to browse and download high-quality wallpapers. The site features a clean layout, smooth interactions, and an easy-to-use interface for exploring beautiful wallpapers across various categories.",technologies:["HTML","JavaScript","CSS"],githubUrl:"https://github.com/macamisp/wallpaper.com",imageUrl:"/123.png",featured:!1},{id:"Weather-Website",title:"weather-website",description:"more funtions to be added.we",technologies:["HTML","JavaScript","CSS"],githubUrl:"https://github.com/macamisp/weather_website/actions/runs/10582278736",imageUrl:"/1234.png",featured:!1},{id:"clothifiy",title:"CLOTHIFIY-POS SYSTEM",description:"using the java core consepts Like OOP to create  clothifiy pos system system.",technologies:["SCENE BUILDER","JAVA-FX"],githubUrl:"https://github.com/macamisp/ClothiFiy",imageUrl:"/12345.jpg",featured:!1},{id:"Mouse_Point  ",title:"Mouse_Point",description:"using the HTML,CSS and javaScript to create a mouse point hover effect.",technologies:["HTML","JavaScript","CSS"],githubUrl:"https://macamisp.github.io/mouse_point/",imageUrl:"/123456.png",featured:!1},{id:"Solarsystem  ",title:"Solarsystem",description:"using the HTML,CSS and javaScript to create a solar system.",technologies:["HTML","JavaScript","CSS"],githubUrl:"https://macamisp.github.io/Solar_system/",imageUrl:"/1234567.png",featured:!1},{id:"MILKYWAY  ",title:"MILKYWAY-SYSTEM",description:"using the HTML,CSS and javaScript to create a milkyway system.",technologies:["HTML","JavaScript","CSS"],githubUrl:"https://macamisp.github.io/milkway/",imageUrl:"/12345678.png",featured:!1},{id:"x-animation-tree  ",title:"X-animation-tree",description:"using the HTML,CSS and javaScript to create a chiristmas tree.",technologies:["HTML","JavaScript","CSS"],githubUrl:"https://macamisp.github.io/x-animation-tree/",imageUrl:"/Image_fx (14).jpg",featured:!1},{id:"hartanimation  ",title:"Hart-animation",description:"using the HTML,CSS and javaScript to create a hart animation.",technologies:["HTML","JavaScript","CSS"],githubUrl:"https://macamisp.github.io/hart--animation/",imageUrl:"/1234567890.png",featured:!1},{id:"ai_gherkin  ",title:"AI-gherkin",description:"using the HTML,CSS and javaScript and Teachable Machine  to create a image detector .",technologies:["HTML","JavaScript","CSS"],githubUrl:"https://macamisp.github.io/ai_gherkin/",imageUrl:"/11.png",featured:!1}];education=[{institution:"University of Moratuwa",degree:"Bachelor of Information Technology (BIT)",period:"2025 - Present",location:"Moratuwa",description:"Currently pursuing undergraduate degree in Information Technology with focus on software engineering, system design, and emerging technologies."},{institution:"Institute of Computer Engineering Technology (ICET)",degree:"ICET Certified Developer (ICD)",period:"Jan 2010 - Feb 2023",location:"Panadura"},{institution:"St. John's College, Panadura",degree:"G.C.E Advanced Level - A/L MALA - B",period:"2021-2023",location:"Panadura"},{institution:"St. John's College, Panadura",degree:"G.C.E Ordinary Level",period:"2010-2022",location:"Panadura"}];workExperience=[{company:"Universal Software Company",position:"Frontend Engineer Intern",period:"January 2025 - Present",responsibilities:["Developing user interfaces using Angular for scalable web applications","Collaborating with backend teams to integrate REST APIs","Participating in code reviews and agile development processes","Enhancing UI/UX design to improve user interaction and performance"]}];fitnessJourney=[{company:"Personal Fitness Development",position:"Dedicated Fitness Training",period:"2021 - Present (3+ Years)",responsibilities:["Maintained consistent strength training routine with progressive overload principles","Achieved significant improvements in compound movements: deadlifts, squats, bench press","Developed exceptional endurance through cardiovascular training and HIIT workouts","Applied systematic approach to goal-setting and performance tracking","Demonstrated mental resilience and discipline through consistent daily routines","Utilized functional fitness methodologies for overall physical development","Translated fitness discipline into professional work ethic and problem-solving mindset"]}];socialLinks=[{name:"GitHub",url:"https://github.com/macamisp",icon:"fab fa-github",color:"#333"},{name:"LinkedIn",url:"https://www.linkedin.com/in/sachin-samarawickrama-*********/",icon:"fab fa-linkedin",color:"#0077b5"},{name:"Facebook",url:"https://www.facebook.com/profile.php?id=100069446774540",icon:"fab fa-facebook",color:"#1877f2"},{name:"Instagram",url:"https://www.instagram.com/macami_impres/",icon:"fab fa-instagram",color:"#e4405f"},{name:"Threads",url:"https://www.threads.com/@macami_impres?xmt=AQGzSQbxGWB_x5fu5c2M7HjJLu0rVs6W7XoMbkST3RdUPDs",icon:"fab fa-threads",color:"#000"},{name:"Telegram",url:"https://t.me/MACAMI_1X1080SP991",icon:"fab fa-telegram",color:"#0088cc"},{name:"WhatsApp",url:"https://wa.me/94788179855",icon:"fab fa-whatsapp",color:"#25d366"}];languages=[{name:"Sinhala",level:"Native"},{name:"English",level:"Fluent"}];hobbies=[{name:"Fitness Activities",icon:"fas fa-dumbbell"},{name:"Traveling",icon:"fas fa-plane"},{name:"Photography",icon:"fas fa-camera"},{name:"Cricket",icon:"fas fa-baseball-ball"},{name:"Badminton",icon:"fas fa-shuttlecock"}];leadership=[{title:"Prefect at St. John's College Panadura",description:"Being a prefect in st john's college panadura"},{title:"School Scout Society Member",description:"Being a member in school scout society"},{title:"Class Monitor",description:"Being a class monitor in school"}];otherQualifications=[{category:"Organizing and Coordinating",items:["Being an active member in school media club","Good skilled in organizing work","Participated in organizing committee of st john's college"]},{category:"Sports & Extracurricular Activities",items:["Participated in cricket","Participated in cricket","Participated in badminton"]}];getPersonalInfo(){return this.personalInfo}getSkills(){return this.skills}getSkillsByCategory(t){return this.skills.filter(n=>n.category===t)}getProjects(){return this.projects}getFeaturedProjects(){return this.projects.filter(t=>t.featured)}getEducation(){return this.education}getWorkExperience(){return this.workExperience}getSocialLinks(){return this.socialLinks}getLanguages(){return this.languages}getHobbies(){return this.hobbies}getLeadership(){return this.leadership}getOtherQualifications(){return this.otherQualifications}getFitnessJourney(){return this.fitnessJourney}getAllProjects(){return on([O(this.projects),this.githubService.getRepositories()]).pipe(A(([t,n])=>{let r=this.convertGitHubToProjects(n);return this.mergeProjects(t,r)}),ke(t=>(console.error("Error fetching projects:",t),O(this.projects))))}getFeaturedProjectsObservable(){return this.getAllProjects().pipe(A(t=>t.filter(n=>n.featured)))}getProjectsByTechnology(t){return this.getAllProjects().pipe(A(n=>n.filter(r=>r.technologies.some(o=>o.toLowerCase().includes(t.toLowerCase()))||r.language?.toLowerCase().includes(t.toLowerCase()))))}convertGitHubToProjects(t){return t.map(n=>({id:n.name,title:n.displayName,description:n.description,technologies:[n.language,...n.topics].filter(Boolean),githubUrl:n.githubUrl,liveUrl:n.liveUrl||void 0,imageUrl:n.imageUrl,featured:n.isFeatured,language:n.language,stars:n.stars,lastUpdated:n.lastUpdated,isFromGitHub:!0}))}mergeProjects(t,n){let r=[...t];return n.forEach(o=>{let i=r.findIndex(s=>s.githubUrl===o.githubUrl||s.id===o.id);i>=0?r[i]=U(M(M({},r[i]),o),{description:r[i].description.length>o.description.length?r[i].description:o.description,technologies:[...new Set([...r[i].technologies,...o.technologies])]}):r.push(o)}),r.sort((o,i)=>o.featured&&!i.featured?-1:!o.featured&&i.featured?1:o.stars&&i.stars&&o.stars!==i.stars?i.stars-o.stars:o.lastUpdated&&i.lastUpdated?i.lastUpdated.getTime()-o.lastUpdated.getTime():0)}static \u0275fac=function(n){return new(n||e)(x(_a))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})};var Ca=class e{constructor(t){this.portfolioService=t;this.personalInfo=this.portfolioService.getPersonalInfo()}personalInfo;isMenuOpen=!1;isScrolled=!1;onWindowScroll(){this.isScrolled=window.scrollY>50}toggleMenu(){this.isMenuOpen=!this.isMenuOpen}closeMenu(){this.isMenuOpen=!1}scrollToSection(t){let n=document.getElementById(t);n&&(n.scrollIntoView({behavior:"smooth"}),this.closeMenu())}static \u0275fac=function(n){return new(n||e)(P(_e))};static \u0275cmp=ne({type:e,selectors:[["app-header"]],hostBindings:function(n,r){n&1&&q("scroll",function(){return r.onWindowScroll()},!1,Eh)},decls:27,vars:7,consts:[[1,"header"],[1,"navbar"],[1,"nav-container"],[1,"nav-brand"],["href","#",1,"brand-link",3,"click"],[1,"brand-text"],[1,"nav-menu"],[1,"nav-item"],["href","#",1,"nav-link",3,"click"],[1,"nav-toggle",3,"click"],[1,"bar"]],template:function(n,r){n&1&&(d(0,"header",0)(1,"nav",1)(2,"div",2)(3,"div",3)(4,"a",4),q("click",function(){return r.scrollToSection("hero")}),d(5,"span",5),m(6),f()()(),d(7,"ul",6)(8,"li",7)(9,"a",8),q("click",function(){return r.scrollToSection("hero")}),m(10,"Home"),f()(),d(11,"li",7)(12,"a",8),q("click",function(){return r.scrollToSection("about")}),m(13,"About"),f()(),d(14,"li",7)(15,"a",8),q("click",function(){return r.scrollToSection("skills")}),m(16,"Skills"),f()(),d(17,"li",7)(18,"a",8),q("click",function(){return r.scrollToSection("projects")}),m(19,"Projects"),f()(),d(20,"li",7)(21,"a",8),q("click",function(){return r.scrollToSection("contact")}),m(22,"Contact"),f()()(),d(23,"div",9),q("click",function(){return r.toggleMenu()}),C(24,"span",10)(25,"span",10)(26,"span",10),f()()()()),n&2&&(fe("scrolled",r.isScrolled),g(6),S(r.personalInfo.name.split(" ")[0]),g(),fe("active",r.isMenuOpen),g(16),fe("active",r.isMenuOpen))},dependencies:[ge,dd],styles:['.header[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:70px;background-color:#fffffff2;box-shadow:0 2px 10px #0000001a;z-index:1000;transition:all .3s ease}.header.scrolled[_ngcontent-%COMP%]{background-color:#fffffffa;box-shadow:0 5px 15px #0000001a}.navbar[_ngcontent-%COMP%]{height:100%}.nav-container[_ngcontent-%COMP%]{max-width:1200px;height:100%;margin:0 auto;padding:0 20px;display:flex;justify-content:space-between;align-items:center}.nav-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]{text-decoration:none;font-size:1.8rem;font-weight:700;color:#4a6cf7;transition:color .3s ease}.nav-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]:hover{color:#3d5af5}.nav-menu[_ngcontent-%COMP%]{display:flex;list-style:none;margin:0;padding:0}.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{margin-left:30px}.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{text-decoration:none;color:#333;font-size:1rem;font-weight:500;padding:8px 0;position:relative;transition:color .3s ease}.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:0;width:0;height:2px;background-color:#4a6cf7;transition:width .3s ease}.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover{color:#4a6cf7}.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover:after{width:100%}.nav-toggle[_ngcontent-%COMP%]{display:none;cursor:pointer;flex-direction:column;justify-content:space-between;width:30px;height:21px}.nav-toggle[_ngcontent-%COMP%]   .bar[_ngcontent-%COMP%]{display:block;width:100%;height:3px;background-color:#333;border-radius:10px;transition:all .3s ease}.nav-toggle.active[_ngcontent-%COMP%]   .bar[_ngcontent-%COMP%]:nth-child(1){transform:translateY(9px) rotate(45deg)}.nav-toggle.active[_ngcontent-%COMP%]   .bar[_ngcontent-%COMP%]:nth-child(2){opacity:0}.nav-toggle.active[_ngcontent-%COMP%]   .bar[_ngcontent-%COMP%]:nth-child(3){transform:translateY(-9px) rotate(-45deg)}@media (max-width: 768px){.nav-menu[_ngcontent-%COMP%]{position:fixed;top:70px;left:-100%;flex-direction:column;background-color:#fff;width:100%;text-align:center;transition:left .3s ease;box-shadow:0 10px 15px #0000001a;padding:20px 0}.nav-menu.active[_ngcontent-%COMP%]{left:0}.nav-menu[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{margin:15px 0}.nav-toggle[_ngcontent-%COMP%]{display:flex}}']})};function bE(e,t){e&1&&(d(0,"div",13),C(1,"div",14)(2,"div",15)(3,"div",16)(4,"div",17)(5,"div",18)(6,"div",19),f())}function ME(e,t){e&1&&C(0,"div",20)}function DE(e,t){e&1&&(d(0,"div",21)(1,"div",22),so(),d(2,"svg",23),C(3,"circle",24),f()(),Ll(),d(4,"div",25),so(),d(5,"svg",26),C(6,"polygon",27),f()(),Ll(),d(7,"div",28),so(),d(8,"svg",29),C(9,"rect",30),f()()())}var Kt=class e{constructor(t){this.elementRef=t}particleCount=50;animationType="particles";intensity="medium";particles=[];animationId=0;canvas=null;ctx=null;prefersReducedMotion=!1;ngOnInit(){this.prefersReducedMotion=window.matchMedia("(prefers-reduced-motion: reduce)").matches,this.prefersReducedMotion||this.initializeParticles()}ngAfterViewInit(){this.prefersReducedMotion||(this.setupCanvas(),this.startAnimation())}ngOnDestroy(){this.animationId&&cancelAnimationFrame(this.animationId)}initializeParticles(){let t=["#4a6cf7","#764ba2","#667eea","#764ba2"],n=["circle","triangle","square","diamond"];for(let r=0;r<this.particleCount;r++)this.particles.push({id:r,x:Math.random()*window.innerWidth,y:Math.random()*window.innerHeight,size:Math.random()*4+2,speedX:(Math.random()-.5)*.5,speedY:(Math.random()-.5)*.5,opacity:Math.random()*.5+.1,shape:n[Math.floor(Math.random()*n.length)],color:t[Math.floor(Math.random()*t.length)]})}setupCanvas(){this.canvas=this.elementRef.nativeElement.querySelector("canvas"),this.canvas&&(this.ctx=this.canvas.getContext("2d"),this.resizeCanvas(),window.addEventListener("resize",()=>this.resizeCanvas()))}resizeCanvas(){this.canvas&&(this.canvas.width=window.innerWidth,this.canvas.height=window.innerHeight)}startAnimation(){if(this.prefersReducedMotion)return;let t=()=>{this.updateParticles(),this.drawParticles(),this.animationId=requestAnimationFrame(t)};t()}updateParticles(){this.particles.forEach(t=>{t.x+=t.speedX,t.y+=t.speedY,t.x>window.innerWidth&&(t.x=0),t.x<0&&(t.x=window.innerWidth),t.y>window.innerHeight&&(t.y=0),t.y<0&&(t.y=window.innerHeight)})}drawParticles(){!this.ctx||!this.canvas||(this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.particles.forEach(t=>{this.ctx.save(),this.ctx.globalAlpha=t.opacity,this.ctx.fillStyle=t.color,this.drawShape(t),this.ctx.restore()}),this.drawConnections())}drawShape(t){if(!this.ctx)return;let{x:n,y:r,size:o,shape:i}=t;switch(i){case"circle":this.ctx.beginPath(),this.ctx.arc(n,r,o,0,Math.PI*2),this.ctx.fill();break;case"square":this.ctx.fillRect(n-o,r-o,o*2,o*2);break;case"triangle":this.ctx.beginPath(),this.ctx.moveTo(n,r-o),this.ctx.lineTo(n-o,r+o),this.ctx.lineTo(n+o,r+o),this.ctx.closePath(),this.ctx.fill();break;case"diamond":this.ctx.beginPath(),this.ctx.moveTo(n,r-o),this.ctx.lineTo(n+o,r),this.ctx.lineTo(n,r+o),this.ctx.lineTo(n-o,r),this.ctx.closePath(),this.ctx.fill();break}}drawConnections(){if(!this.ctx)return;let t=100;for(let n=0;n<this.particles.length;n++)for(let r=n+1;r<this.particles.length;r++){let o=this.particles[n],i=this.particles[r],s=Math.sqrt(Math.pow(o.x-i.x,2)+Math.pow(o.y-i.y,2));if(s<t){let a=(1-s/t)*.1;this.ctx.save(),this.ctx.globalAlpha=a,this.ctx.strokeStyle="#4a6cf7",this.ctx.lineWidth=1,this.ctx.beginPath(),this.ctx.moveTo(o.x,o.y),this.ctx.lineTo(i.x,i.y),this.ctx.stroke(),this.ctx.restore()}}}static \u0275fac=function(n){return new(n||e)(P(Xe))};static \u0275cmp=ne({type:e,selectors:[["app-background-animation"]],inputs:{particleCount:"particleCount",animationType:"animationType",intensity:"intensity"},decls:16,vars:7,consts:[[1,"background-animation"],[1,"particles-canvas"],["class","geometric-shapes",4,"ngIf"],["class","gradient-overlay",4,"ngIf"],["class","floating-elements",4,"ngIf"],["width","0","height","0",2,"position","absolute"],["id","gradient1","x1","0%","y1","0%","x2","100%","y2","100%"],["offset","0%",2,"stop-color","#4a6cf7","stop-opacity","1"],["offset","100%",2,"stop-color","#764ba2","stop-opacity","1"],["id","gradient2","x1","0%","y1","0%","x2","100%","y2","100%"],["offset","0%",2,"stop-color","#667eea","stop-opacity","1"],["id","gradient3","x1","0%","y1","0%","x2","100%","y2","100%"],["offset","100%",2,"stop-color","#667eea","stop-opacity","1"],[1,"geometric-shapes"],[1,"shape","shape-1"],[1,"shape","shape-2"],[1,"shape","shape-3"],[1,"shape","shape-4"],[1,"shape","shape-5"],[1,"shape","shape-6"],[1,"gradient-overlay"],[1,"floating-elements"],[1,"floating-element","floating-element-1"],["width","60","height","60","viewBox","0 0 60 60"],["cx","30","cy","30","r","25","fill","none","stroke","url(#gradient1)","stroke-width","2","opacity","0.3"],[1,"floating-element","floating-element-2"],["width","80","height","80","viewBox","0 0 80 80"],["points","40,10 70,60 10,60","fill","none","stroke","url(#gradient2)","stroke-width","2","opacity","0.2"],[1,"floating-element","floating-element-3"],["width","50","height","50","viewBox","0 0 50 50"],["x","10","y","10","width","30","height","30","fill","none","stroke","url(#gradient3)","stroke-width","2","opacity","0.25"]],template:function(n,r){n&1&&(d(0,"div",0),C(1,"canvas",1),N(2,bE,7,0,"div",2)(3,ME,1,0,"div",3)(4,DE,10,0,"div",4),so(),d(5,"svg",5)(6,"defs")(7,"linearGradient",6),C(8,"stop",7)(9,"stop",8),f(),d(10,"linearGradient",9),C(11,"stop",10)(12,"stop",8),f(),d(13,"linearGradient",11),C(14,"stop",7)(15,"stop",12),f()()()()),n&2&&(Ge("animation-"+r.animationType),g(),Ae("display",r.prefersReducedMotion?"none":"block"),g(),b("ngIf",r.animationType==="geometric"&&!r.prefersReducedMotion),g(),b("ngIf",r.animationType==="gradient"&&!r.prefersReducedMotion),g(),b("ngIf",!r.prefersReducedMotion))},dependencies:[ge,Mn],styles:[".background-animation[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden;pointer-events:none;z-index:-1}.particles-canvas[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;will-change:transform}.geometric-shapes[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%}.geometric-shapes[_ngcontent-%COMP%]   .shape[_ngcontent-%COMP%]{position:absolute;border-radius:50%;background:linear-gradient(135deg,#4a6cf71a,#764ba20d);will-change:transform}.geometric-shapes[_ngcontent-%COMP%]   .shape.shape-1[_ngcontent-%COMP%]{width:100px;height:100px;top:10%;left:10%;animation:_ngcontent-%COMP%_float-1 20s ease-in-out infinite}.geometric-shapes[_ngcontent-%COMP%]   .shape.shape-2[_ngcontent-%COMP%]{width:150px;height:150px;top:60%;right:15%;border-radius:0;transform:rotate(45deg);animation:_ngcontent-%COMP%_float-2 25s ease-in-out infinite}.geometric-shapes[_ngcontent-%COMP%]   .shape.shape-3[_ngcontent-%COMP%]{width:80px;height:80px;top:30%;right:30%;clip-path:polygon(50% 0%,0% 100%,100% 100%);animation:_ngcontent-%COMP%_float-3 18s ease-in-out infinite}.geometric-shapes[_ngcontent-%COMP%]   .shape.shape-4[_ngcontent-%COMP%]{width:120px;height:120px;bottom:20%;left:20%;clip-path:polygon(25% 0%,75% 0%,100% 50%,75% 100%,25% 100%,0% 50%);animation:_ngcontent-%COMP%_float-4 22s ease-in-out infinite}.geometric-shapes[_ngcontent-%COMP%]   .shape.shape-5[_ngcontent-%COMP%]{width:60px;height:60px;top:80%;left:60%;animation:_ngcontent-%COMP%_float-5 16s ease-in-out infinite}.geometric-shapes[_ngcontent-%COMP%]   .shape.shape-6[_ngcontent-%COMP%]{width:200px;height:200px;top:5%;right:5%;border-radius:0;background:linear-gradient(135deg,#667eea14,#764ba208);animation:_ngcontent-%COMP%_float-6 30s ease-in-out infinite}.gradient-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(45deg,#4a6cf70d,#667eea08,#764ba20d,#4a6cf705,#667eea0a);background-size:400% 400%;animation:_ngcontent-%COMP%_gradient-shift 15s ease infinite;will-change:background-position}.floating-elements[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%}.floating-elements[_ngcontent-%COMP%]   .floating-element[_ngcontent-%COMP%]{position:absolute;will-change:transform}.floating-elements[_ngcontent-%COMP%]   .floating-element.floating-element-1[_ngcontent-%COMP%]{top:15%;right:20%;animation:_ngcontent-%COMP%_gentle-float-1 12s ease-in-out infinite}.floating-elements[_ngcontent-%COMP%]   .floating-element.floating-element-2[_ngcontent-%COMP%]{bottom:25%;left:15%;animation:_ngcontent-%COMP%_gentle-float-2 15s ease-in-out infinite}.floating-elements[_ngcontent-%COMP%]   .floating-element.floating-element-3[_ngcontent-%COMP%]{top:50%;right:10%;animation:_ngcontent-%COMP%_gentle-float-3 18s ease-in-out infinite}.floating-elements[_ngcontent-%COMP%]   .floating-element[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{filter:drop-shadow(0 0 10px rgba(74,108,247,.2))}@keyframes _ngcontent-%COMP%_float-1{0%,to{transform:translateY(0) translate(0) rotate(0)}25%{transform:translateY(-20px) translate(10px) rotate(90deg)}50%{transform:translateY(-10px) translate(-15px) rotate(180deg)}75%{transform:translateY(-30px) translate(5px) rotate(270deg)}}@keyframes _ngcontent-%COMP%_float-2{0%,to{transform:translateY(0) translate(0) rotate(45deg)}33%{transform:translateY(-25px) translate(-10px) rotate(135deg)}66%{transform:translateY(-15px) translate(20px) rotate(225deg)}}@keyframes _ngcontent-%COMP%_float-3{0%,to{transform:translateY(0) translate(0) scale(1)}50%{transform:translateY(-35px) translate(-20px) scale(1.1)}}@keyframes _ngcontent-%COMP%_float-4{0%,to{transform:translateY(0) translate(0) rotate(0)}25%{transform:translateY(15px) translate(-25px) rotate(60deg)}50%{transform:translateY(-20px) translate(10px) rotate(120deg)}75%{transform:translateY(10px) translate(15px) rotate(180deg)}}@keyframes _ngcontent-%COMP%_float-5{0%,to{transform:translateY(0) scale(1)}50%{transform:translateY(-40px) scale(1.2)}}@keyframes _ngcontent-%COMP%_float-6{0%,to{transform:translateY(0) translate(0) rotate(0)}50%{transform:translateY(-10px) translate(-10px) rotate(180deg)}}@keyframes _ngcontent-%COMP%_gradient-shift{0%{background-position:0% 50%}50%{background-position:100% 50%}to{background-position:0% 50%}}@keyframes _ngcontent-%COMP%_gentle-float-1{0%,to{transform:translateY(0) translate(0) rotate(0)}33%{transform:translateY(-15px) translate(8px) rotate(120deg)}66%{transform:translateY(-8px) translate(-12px) rotate(240deg)}}@keyframes _ngcontent-%COMP%_gentle-float-2{0%,to{transform:translateY(0) translate(0) scale(1)}50%{transform:translateY(-20px) translate(15px) scale(1.05)}}@keyframes _ngcontent-%COMP%_gentle-float-3{0%,to{transform:translateY(0) translate(0) rotate(0)}25%{transform:translateY(-10px) translate(-8px) rotate(90deg)}50%{transform:translateY(-18px) translate(5px) rotate(180deg)}75%{transform:translateY(-5px) translate(10px) rotate(270deg)}}.animation-particles[_ngcontent-%COMP%]{transform:translateZ(0);backface-visibility:hidden}.background-animation[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{transform-style:preserve-3d;backface-visibility:hidden}@media (prefers-reduced-motion: reduce){.background-animation[_ngcontent-%COMP%]{display:none!important}.geometric-shapes[_ngcontent-%COMP%]   .shape[_ngcontent-%COMP%], .floating-elements[_ngcontent-%COMP%]   .floating-element[_ngcontent-%COMP%], .gradient-overlay[_ngcontent-%COMP%]{animation:none!important;transform:none!important}}@media (max-width: 768px){.geometric-shapes[_ngcontent-%COMP%]   .shape.shape-2[_ngcontent-%COMP%], .geometric-shapes[_ngcontent-%COMP%]   .shape.shape-6[_ngcontent-%COMP%], .floating-elements[_ngcontent-%COMP%]   .floating-element.floating-element-2[_ngcontent-%COMP%]{display:none}}"]})};function wE(e,t){if(e&1&&(d(0,"a",21),C(1,"i"),f()),e&2){let n=t.$implicit;Ae("--social-color",n.color),b("href",n.url,he),yn("aria-label",n.name),g(),Ge(n.icon)}}var ba=class e{constructor(t){this.portfolioService=t;this.personalInfo=this.portfolioService.getPersonalInfo(),this.socialLinks=this.portfolioService.getSocialLinks()}personalInfo;socialLinks;displayText="";fullText="";currentIndex=0;isTyping=!0;ngOnInit(){this.fullText=this.personalInfo.title,this.typeWriter()}typeWriter(){this.currentIndex<this.fullText.length?(this.displayText+=this.fullText.charAt(this.currentIndex),this.currentIndex++,setTimeout(()=>this.typeWriter(),100)):this.isTyping=!1}scrollToSection(t){let n=document.getElementById(t);n&&n.scrollIntoView({behavior:"smooth"})}downloadCV(){console.log("Download CV clicked")}static \u0275fac=function(n){return new(n||e)(P(_e))};static \u0275cmp=ne({type:e,selectors:[["app-hero"]],decls:29,vars:8,consts:[[1,"hero-container"],["animationType","particles","intensity","medium",3,"particleCount"],[1,"hero-content"],[1,"hero-text"],[1,"greeting"],[1,"name"],[1,"title"],[1,"typed-text"],[1,"cursor"],[1,"description"],[1,"hero-buttons"],[1,"btn","btn-primary",3,"click"],[1,"btn","btn-secondary",3,"click"],[1,"social-links"],["target","_blank","class","social-link",3,"href","--social-color",4,"ngFor","ngForOf"],[1,"hero-image"],[1,"image-container"],["src","Polish_20250702_023502090.jpg",1,"profile-image",3,"alt"],[1,"scroll-down",3,"click"],[1,"scroll-text"],[1,"fas","fa-chevron-down"],["target","_blank",1,"social-link",3,"href"]],template:function(n,r){n&1&&(d(0,"div",0),C(1,"app-background-animation",1),d(2,"div",2)(3,"div",3)(4,"h1",4),m(5,"Hello, I'm "),d(6,"span",5),m(7),f()(),d(8,"h2",6)(9,"span",7),m(10),f(),d(11,"span",8),m(12,"|"),f()(),d(13,"p",9),m(14),f(),d(15,"div",10)(16,"button",11),q("click",function(){return r.scrollToSection("contact")}),m(17,"Contact Me"),f(),d(18,"button",12),q("click",function(){return r.downloadCV()}),m(19,"Download CV"),f()(),d(20,"div",13),N(21,wE,2,6,"a",14),f()(),d(22,"div",15)(23,"div",16),C(24,"img",17),f()()(),d(25,"div",18),q("click",function(){return r.scrollToSection("about")}),d(26,"span",19),m(27,"Scroll Down"),f(),C(28,"i",20),f()()),n&2&&(g(),b("particleCount",30),g(6),S(r.personalInfo.name),g(3),S(r.displayText),g(),fe("typing",r.isTyping),g(3),S(r.personalInfo.bio),g(7),b("ngForOf",r.socialLinks.slice(0,4)),g(3),du("alt",r.personalInfo.name))},dependencies:[ge,Ne,Kt],styles:[".hero-container[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);display:flex;flex-direction:column;justify-content:center;align-items:center;position:relative;padding:100px 20px 50px;color:#fff;overflow:hidden;isolation:isolate}.hero-content[_ngcontent-%COMP%]{max-width:1200px;width:100%;display:grid;grid-template-columns:1fr 1fr;gap:60px;align-items:center}.hero-text[_ngcontent-%COMP%]   .greeting[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:300;margin-bottom:20px;opacity:0;animation:_ngcontent-%COMP%_fadeInUp 1s ease forwards}.hero-text[_ngcontent-%COMP%]   .greeting[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:gold;font-weight:700}.hero-text[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:3rem;font-weight:700;margin-bottom:30px;min-height:80px;opacity:0;animation:_ngcontent-%COMP%_fadeInUp 1s ease .3s forwards}.hero-text[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .typed-text[_ngcontent-%COMP%]{color:#fff}.hero-text[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .cursor[_ngcontent-%COMP%]{color:gold;animation:_ngcontent-%COMP%_blink 1s infinite}.hero-text[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .cursor.typing[_ngcontent-%COMP%]{animation:none}.hero-text[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{font-size:1.2rem;line-height:1.6;margin-bottom:40px;opacity:.9;opacity:0;animation:_ngcontent-%COMP%_fadeInUp 1s ease .6s forwards}.hero-buttons[_ngcontent-%COMP%]{display:flex;gap:20px;margin-bottom:40px;opacity:0;animation:_ngcontent-%COMP%_fadeInUp 1s ease .9s forwards}.hero-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:15px 30px;border:none;border-radius:50px;font-size:1rem;font-weight:600;cursor:pointer;transition:all .3s ease;text-decoration:none;display:inline-block}.hero-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background-color:gold;color:#222}.hero-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover{background-color:#e6c200;transform:translateY(-2px);box-shadow:0 10px 20px #ffd7004d}.hero-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]{background-color:transparent;color:#fff;border:2px solid #fff}.hero-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover{background-color:#fff;color:#222;transform:translateY(-2px)}.social-links[_ngcontent-%COMP%]{display:flex;gap:20px;opacity:0;animation:_ngcontent-%COMP%_fadeInUp 1s ease 1.2s forwards}.social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:50px;height:50px;background-color:#ffffff1a;border-radius:50%;color:#fff;font-size:1.5rem;transition:all .3s ease;text-decoration:none}.social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]:hover{background-color:gold;color:#222;transform:translateY(-3px)}.hero-image[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;opacity:0;animation:_ngcontent-%COMP%_fadeInRight 1s ease .6s forwards}.hero-image[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{position:relative;width:400px;height:400px;border-radius:50%;overflow:hidden;border:5px solid rgba(255,255,255,.2);box-shadow:0 20px 40px #0000004d}.hero-image[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .profile-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.hero-image[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]:hover   .profile-image[_ngcontent-%COMP%]{transform:scale(1.1)}.scroll-down[_ngcontent-%COMP%]{position:absolute;bottom:30px;left:50%;transform:translate(-50%);display:flex;flex-direction:column;align-items:center;cursor:pointer;color:#fff;opacity:.8;transition:opacity .3s ease;opacity:0;animation:_ngcontent-%COMP%_fadeIn 1s ease 1.5s forwards}.scroll-down[_ngcontent-%COMP%]:hover{opacity:1}.scroll-down[_ngcontent-%COMP%]   .scroll-text[_ngcontent-%COMP%]{font-size:.9rem;margin-bottom:10px}.scroll-down[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem;animation:_ngcontent-%COMP%_bounce 2s infinite}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_fadeInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_blink{0%,50%{opacity:1}51%,to{opacity:0}}@keyframes _ngcontent-%COMP%_bounce{0%,20%,50%,80%,to{transform:translateY(0)}40%{transform:translateY(-10px)}60%{transform:translateY(-5px)}}@media (max-width: 768px){.hero-container[_ngcontent-%COMP%]{padding:80px 15px 40px;min-height:100vh}.hero-content[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:30px;text-align:center}.hero-text[_ngcontent-%COMP%]   .greeting[_ngcontent-%COMP%]{font-size:1.8rem;line-height:1.3;margin-bottom:15px}.hero-text[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:2rem;line-height:1.2;min-height:60px;margin-bottom:25px}.hero-text[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{font-size:1rem;line-height:1.6;margin-bottom:30px;padding:0 10px}.hero-buttons[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:15px;margin-bottom:30px}.hero-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:200px;padding:12px 25px;font-size:.95rem}.social-links[_ngcontent-%COMP%]{gap:15px;justify-content:center}.social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]{width:45px;height:45px;font-size:1.3rem}.hero-image[_ngcontent-%COMP%]{order:-1}.hero-image[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{width:280px;height:280px;margin:0 auto}.scroll-down[_ngcontent-%COMP%]{bottom:20px}.scroll-down[_ngcontent-%COMP%]   .scroll-text[_ngcontent-%COMP%]{font-size:.8rem}.scroll-down[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 480px){.hero-container[_ngcontent-%COMP%]{padding:70px 10px 30px}.hero-text[_ngcontent-%COMP%]   .greeting[_ngcontent-%COMP%]{font-size:1.6rem}.hero-text[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:1.8rem;min-height:50px}.hero-text[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{font-size:.95rem;padding:0 5px}.hero-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:180px;padding:10px 20px;font-size:.9rem}.social-links[_ngcontent-%COMP%]{gap:12px}.social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]{width:40px;height:40px;font-size:1.2rem}.hero-image[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{width:240px;height:240px}}"]})};function EE(e,t){if(e&1&&(d(0,"div",28)(1,"span",29),m(2),f(),d(3,"span",30),m(4),f()()),e&2){let n=t.$implicit;g(2),S(n.name),g(2),S(n.level)}}function IE(e,t){if(e&1&&(d(0,"div",31),C(1,"i"),d(2,"span"),m(3),f()()),e&2){let n=t.$implicit;g(),Ge(n.icon),g(2),S(n.name)}}function xE(e,t){if(e&1&&(d(0,"div",32),C(1,"div",33),d(2,"div",34),m(3),f(),d(4,"div",35)(5,"h4"),m(6),f(),d(7,"p"),m(8),f()()()),e&2){let n=t.$implicit;g(3),S(n.period),g(3),S(n.degree),g(2),_n("",n.institution,", ",n.location,"")}}function SE(e,t){if(e&1&&(d(0,"li"),m(1),f()),e&2){let n=t.$implicit;g(),S(n)}}function OE(e,t){if(e&1&&(d(0,"div",32),C(1,"div",33),d(2,"div",34),m(3),f(),d(4,"div",35)(5,"h4"),m(6),f(),d(7,"p"),m(8),f(),d(9,"ul",36),N(10,SE,2,1,"li",37),f()()()),e&2){let n=t.$implicit;g(3),S(n.period),g(3),S(n.position),g(2),S(n.company),g(2),b("ngForOf",n.responsibilities)}}function PE(e,t){if(e&1&&(d(0,"li"),m(1),f()),e&2){let n=t.$implicit;g(),S(n)}}function TE(e,t){if(e&1&&(d(0,"div",38),C(1,"div",39),d(2,"div",34),m(3),f(),d(4,"div",35)(5,"h4"),m(6),f(),d(7,"p",40),m(8),f(),d(9,"div",41)(10,"div",42),C(11,"i",43),d(12,"span"),m(13,"Strength Training Excellence"),f()(),d(14,"div",42),C(15,"i",44),d(16,"span"),m(17,"Endurance Development"),f()(),d(18,"div",42),C(19,"i",45),d(20,"span"),m(21,"Goal-Oriented Mindset"),f()(),d(22,"div",42),C(23,"i",46),d(24,"span"),m(25,"Mental Resilience"),f()()(),d(26,"ul",47),N(27,PE,2,1,"li",37),f(),d(28,"div",48)(29,"h5"),C(30,"i",49),m(31," Connection to Development Career"),f(),d(32,"p"),m(33,"The discipline, consistency, and problem-solving mindset developed through fitness training directly translates to my approach in software development - maintaining focus on long-term goals, systematic improvement, and resilience when facing challenges."),f()()()()),e&2){let n=t.$implicit;g(3),S(n.period),g(3),S(n.position),g(2),S(n.company),g(19),b("ngForOf",n.responsibilities)}}function AE(e,t){if(e&1&&(d(0,"div",32),C(1,"div",33),d(2,"div",35)(3,"h4"),m(4),f(),d(5,"p"),m(6),f()()()),e&2){let n=t.$implicit;g(4),S(n.title),g(2),S(n.description)}}function NE(e,t){if(e&1&&(d(0,"li"),m(1),f()),e&2){let n=t.$implicit;g(),S(n)}}function RE(e,t){if(e&1&&(d(0,"div",50)(1,"h4",51),m(2),f(),d(3,"ul",52),N(4,NE,2,1,"li",37),f()()),e&2){let n=t.$implicit;g(2),S(n.category),g(2),b("ngForOf",n.items)}}var Ma=class e{constructor(t){this.portfolioService=t;this.personalInfo=this.portfolioService.getPersonalInfo(),this.education=this.portfolioService.getEducation(),this.workExperience=this.portfolioService.getWorkExperience(),this.fitnessJourney=this.portfolioService.getFitnessJourney(),this.languages=this.portfolioService.getLanguages(),this.hobbies=this.portfolioService.getHobbies(),this.leadership=this.portfolioService.getLeadership(),this.otherQualifications=this.portfolioService.getOtherQualifications()}personalInfo;education;workExperience;fitnessJourney;languages;hobbies;leadership;otherQualifications;static \u0275fac=function(n){return new(n||e)(P(_e))};static \u0275cmp=ne({type:e,selectors:[["app-about"]],decls:76,vars:14,consts:[[1,"about-container"],["animationType","geometric","intensity","low",3,"particleCount"],[1,"section-header"],[1,"section-title"],[1,"section-divider"],[1,"about-content"],[1,"about-text"],[1,"personal-info"],[1,"info-item"],[1,"info-label"],[1,"info-value"],[1,"additional-info"],[1,"info-section"],[1,"info-title"],[1,"languages-grid"],["class","language-item",4,"ngFor","ngForOf"],[1,"hobbies-grid"],["class","hobby-item",4,"ngFor","ngForOf"],[1,"timeline-container"],[1,"timeline-section"],[1,"timeline-title"],[1,"timeline"],["class","timeline-item",4,"ngFor","ngForOf"],[1,"timeline-section","fitness-section"],[1,"fas","fa-dumbbell","fitness-icon"],["class","timeline-item fitness-item",4,"ngFor","ngForOf"],[1,"qualifications-grid"],["class","qualification-category",4,"ngFor","ngForOf"],[1,"language-item"],[1,"language-name"],[1,"language-level"],[1,"hobby-item"],[1,"timeline-item"],[1,"timeline-dot"],[1,"timeline-date"],[1,"timeline-content"],[1,"responsibility-list"],[4,"ngFor","ngForOf"],[1,"timeline-item","fitness-item"],[1,"timeline-dot","fitness-dot"],[1,"fitness-subtitle"],[1,"fitness-highlights"],[1,"highlight-card"],[1,"fas","fa-trophy"],[1,"fas","fa-heartbeat"],[1,"fas","fa-target"],[1,"fas","fa-brain"],[1,"responsibility-list","fitness-achievements"],[1,"fitness-connection"],[1,"fas","fa-link"],[1,"qualification-category"],[1,"category-name"],[1,"qualification-list"]],template:function(n,r){n&1&&(d(0,"div",0),C(1,"app-background-animation",1),d(2,"div",2)(3,"h2",3),m(4,"About Me"),f(),C(5,"div",4),f(),d(6,"div",5)(7,"div",6)(8,"h3"),m(9,"Who I Am"),f(),d(10,"p"),m(11),f(),d(12,"div",7)(13,"div",8)(14,"span",9),m(15,"Name:"),f(),d(16,"span",10),m(17),f()(),d(18,"div",8)(19,"span",9),m(20,"Phone:"),f(),d(21,"span",10),m(22),f()(),d(23,"div",8)(24,"span",9),m(25,"Location:"),f(),d(26,"span",10),m(27),f()(),d(28,"div",8)(29,"span",9),m(30,"WhatsApp:"),f(),d(31,"span",10),m(32),f()(),d(33,"div",8)(34,"span",9),m(35,"Email:"),f(),d(36,"span",10),m(37),f()()()()(),d(38,"div",11)(39,"div",12)(40,"h3",13),m(41,"Languages"),f(),d(42,"div",14),N(43,EE,5,2,"div",15),f()(),d(44,"div",12)(45,"h3",13),m(46,"Hobbies & Interests"),f(),d(47,"div",16),N(48,IE,4,3,"div",17),f()()(),d(49,"div",18)(50,"div",19)(51,"h3",20),m(52,"Education"),f(),d(53,"div",21),N(54,xE,9,4,"div",22),f()(),d(55,"div",19)(56,"h3",20),m(57,"Work Experience"),f(),d(58,"div",21),N(59,OE,11,4,"div",22),f()(),d(60,"div",23)(61,"h3",20),C(62,"i",24),m(63," Fitness Journey "),f(),d(64,"div",21),N(65,TE,34,4,"div",25),f()(),d(66,"div",19)(67,"h3",20),m(68,"Leadership Experience"),f(),d(69,"div",21),N(70,AE,7,2,"div",22),f()(),d(71,"div",19)(72,"h3",20),m(73,"Other Qualifications"),f(),d(74,"div",26),N(75,RE,5,2,"div",27),f()()()()),n&2&&(g(),b("particleCount",20),g(10),S(r.personalInfo.bio),g(6),S(r.personalInfo.name),g(5),S(r.personalInfo.phone),g(5),S(r.personalInfo.location),g(5),S(r.personalInfo.whatsapp),g(5),S(r.personalInfo.email),g(6),b("ngForOf",r.languages),g(5),b("ngForOf",r.hobbies),g(6),b("ngForOf",r.education),g(5),b("ngForOf",r.workExperience),g(6),b("ngForOf",r.fitnessJourney),g(5),b("ngForOf",r.leadership),g(5),b("ngForOf",r.otherQualifications))},dependencies:[ge,Ne,Kt],styles:['@charset "UTF-8";.about-container[_ngcontent-%COMP%]{padding:100px 20px;background-color:#f8f9fa;min-height:100vh}.section-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:80px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:3rem;font-weight:700;color:#2c3e50;margin-bottom:20px;position:relative}.section-header[_ngcontent-%COMP%]   .section-divider[_ngcontent-%COMP%]{width:80px;height:4px;background:linear-gradient(90deg,#4a6cf7,#764ba2);margin:0 auto;border-radius:2px}.about-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto 80px}.about-content[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%]{background:#fff;padding:40px;border-radius:15px;box-shadow:0 10px 30px #0000001a}.about-content[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:2rem;color:#2c3e50;margin-bottom:20px;font-weight:600}.about-content[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;line-height:1.8;color:#666;margin-bottom:30px}.personal-info[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px}.personal-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:15px;background:#f8f9fa;border-radius:10px;transition:transform .3s ease}.personal-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.personal-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50;margin-right:10px;min-width:80px}.personal-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{color:#666;font-weight:500}.timeline-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;display:grid;grid-template-columns:repeat(auto-fit,minmax(500px,1fr));gap:60px}.timeline-section[_ngcontent-%COMP%]   .timeline-title[_ngcontent-%COMP%]{font-size:2rem;color:#2c3e50;margin-bottom:40px;font-weight:600;text-align:center;display:flex;align-items:center;justify-content:center;gap:15px}.timeline-section[_ngcontent-%COMP%]   .timeline-title[_ngcontent-%COMP%]   .fitness-icon[_ngcontent-%COMP%]{color:#4a6cf7;font-size:1.8rem}.timeline-section.fitness-section[_ngcontent-%COMP%]   .timeline-title[_ngcontent-%COMP%]{color:#4a6cf7;background:linear-gradient(135deg,#4a6cf71a,#764ba21a);padding:20px;border-radius:15px;margin-bottom:50px}.timeline[_ngcontent-%COMP%]{position:relative;padding-left:30px}.timeline[_ngcontent-%COMP%]:before{content:"";position:absolute;left:15px;top:0;bottom:0;width:2px;background:linear-gradient(180deg,#4a6cf7,#764ba2)}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]{position:relative;margin-bottom:40px;background:#fff;padding:25px;border-radius:15px;box-shadow:0 5px 15px #0000001a;transition:all .3s ease}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]:hover{transform:translate(10px);box-shadow:0 10px 25px #00000026}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-dot[_ngcontent-%COMP%]{position:absolute;left:-37px;top:30px;width:12px;height:12px;background:#4a6cf7;border-radius:50%;border:3px solid #fff;box-shadow:0 0 0 3px #4a6cf7}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-dot.fitness-dot[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4a6cf7,#764ba2);box-shadow:0 0 0 3px #4a6cf7,0 0 20px #4a6cf74d;animation:_ngcontent-%COMP%_pulse-fitness 2s infinite}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-date[_ngcontent-%COMP%]{font-size:.9rem;color:#4a6cf7;font-weight:600;margin-bottom:10px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.3rem;color:#2c3e50;margin-bottom:8px;font-weight:600}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:15px;font-weight:500}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .responsibility-list[_ngcontent-%COMP%]{list-style:none;padding:0}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .responsibility-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{position:relative;padding-left:20px;margin-bottom:8px;color:#666;line-height:1.6}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .responsibility-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:"\\25b8";position:absolute;left:0;color:#4a6cf7;font-weight:700}.additional-info[_ngcontent-%COMP%]{max-width:1200px;margin:60px auto;display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:40px}.info-section[_ngcontent-%COMP%]{background:#fff;padding:30px;border-radius:15px;box-shadow:0 10px 30px #0000001a}.info-section[_ngcontent-%COMP%]   .info-title[_ngcontent-%COMP%]{font-size:1.8rem;color:#2c3e50;margin-bottom:25px;font-weight:600;position:relative;padding-bottom:15px}.info-section[_ngcontent-%COMP%]   .info-title[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:0;width:50px;height:3px;background:linear-gradient(90deg,#4a6cf7,#764ba2);border-radius:2px}.languages-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr;gap:15px}.languages-grid[_ngcontent-%COMP%]   .language-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:15px;background:#f8f9fa;border-radius:10px;transition:transform .3s ease}.languages-grid[_ngcontent-%COMP%]   .language-item[_ngcontent-%COMP%]:hover{transform:translateY(-3px);box-shadow:0 5px 15px #0000000d}.languages-grid[_ngcontent-%COMP%]   .language-item[_ngcontent-%COMP%]   .language-name[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50}.languages-grid[_ngcontent-%COMP%]   .language-item[_ngcontent-%COMP%]   .language-level[_ngcontent-%COMP%]{color:#4a6cf7;font-weight:500;padding:5px 10px;background:#4a6cf71a;border-radius:20px;font-size:.9rem}.hobbies-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(120px,1fr));gap:15px}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;padding:20px 10px;background:#f8f9fa;border-radius:10px;transition:all .3s ease}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 20px #0000001a;background:linear-gradient(135deg,#4a6cf7,#764ba2);color:#fff}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:10px;color:#4a6cf7}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{color:#fff}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:.9rem}.qualifications-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr;gap:30px}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category[_ngcontent-%COMP%]{background:#fff;padding:25px;border-radius:15px;box-shadow:0 5px 15px #0000000d;transition:all .3s ease}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 25px #0000001a}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{font-size:1.3rem;color:#4a6cf7;margin-bottom:15px;font-weight:600}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category[_ngcontent-%COMP%]   .qualification-list[_ngcontent-%COMP%]{list-style:none;padding:0}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category[_ngcontent-%COMP%]   .qualification-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{position:relative;padding-left:25px;margin-bottom:10px;color:#666;line-height:1.6}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category[_ngcontent-%COMP%]   .qualification-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:"\\2713";position:absolute;left:0;color:#4a6cf7;font-weight:700}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4a6cf70d,#764ba20d);border-left:4px solid #4a6cf7}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-subtitle[_ngcontent-%COMP%]{color:#4a6cf7;font-weight:600;font-style:italic}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-highlights[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin:20px 0}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-highlights[_ngcontent-%COMP%]   .highlight-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4a6cf7,#764ba2);color:#fff;padding:15px;border-radius:10px;display:flex;align-items:center;gap:10px;font-weight:500;transition:transform .3s ease}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-highlights[_ngcontent-%COMP%]   .highlight-card[_ngcontent-%COMP%]:hover{transform:translateY(-3px);box-shadow:0 10px 20px #4a6cf74d}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-highlights[_ngcontent-%COMP%]   .highlight-card[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-achievements[_ngcontent-%COMP%]{background:#fffc;padding:20px;border-radius:10px;margin:20px 0}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-achievements[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:"\\1f4aa";color:#4a6cf7}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-connection[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4a6cf71a,#764ba21a);padding:20px;border-radius:10px;margin-top:20px;border-left:4px solid #4a6cf7}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-connection[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:#4a6cf7;font-size:1.1rem;margin-bottom:10px;display:flex;align-items:center;gap:8px}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-connection[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category.fitness-item[_ngcontent-%COMP%]   .fitness-connection[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.7;font-style:italic}@keyframes _ngcontent-%COMP%_pulse-fitness{0%{box-shadow:0 0 0 3px #4a6cf7,0 0 20px #4a6cf74d}50%{box-shadow:0 0 0 3px #4a6cf7,0 0 30px #4a6cf799}to{box-shadow:0 0 0 3px #4a6cf7,0 0 20px #4a6cf74d}}@media (max-width: 768px){.about-container[_ngcontent-%COMP%]{padding:80px 15px;min-height:auto}.section-header[_ngcontent-%COMP%]{margin-bottom:60px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2.5rem;line-height:1.2}.about-content[_ngcontent-%COMP%]{margin-bottom:60px}.about-content[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%]{padding:25px 20px}.about-content[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.8rem;line-height:1.3}.about-content[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem;line-height:1.7}.personal-info[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:15px}.personal-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{padding:12px;font-size:.95rem}.personal-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]{min-width:70px;font-size:.9rem}.personal-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{font-size:.9rem;word-break:break-word}.additional-info[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:25px;margin:40px auto}.info-section[_ngcontent-%COMP%]{padding:20px 15px}.info-section[_ngcontent-%COMP%]   .info-title[_ngcontent-%COMP%]{font-size:1.6rem;margin-bottom:20px}.languages-grid[_ngcontent-%COMP%]   .language-item[_ngcontent-%COMP%]{padding:12px;font-size:.9rem}.languages-grid[_ngcontent-%COMP%]   .language-item[_ngcontent-%COMP%]   .language-level[_ngcontent-%COMP%]{font-size:.8rem;padding:4px 8px}.hobbies-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(90px,1fr));gap:12px}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]{padding:15px 8px}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:8px}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.8rem;line-height:1.2}.timeline-container[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:35px}.timeline-section[_ngcontent-%COMP%]   .timeline-title[_ngcontent-%COMP%]{font-size:1.8rem;margin-bottom:30px}.timeline-section.fitness-section[_ngcontent-%COMP%]   .timeline-title[_ngcontent-%COMP%]{flex-direction:column;gap:10px;padding:15px;font-size:1.7rem;margin-bottom:35px}.timeline-section.fitness-section[_ngcontent-%COMP%]   .timeline-title[_ngcontent-%COMP%]   .fitness-icon[_ngcontent-%COMP%]{font-size:1.5rem}.timeline[_ngcontent-%COMP%]{padding-left:20px}.timeline[_ngcontent-%COMP%]:before{left:10px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]{padding:18px 15px;margin-bottom:25px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-dot[_ngcontent-%COMP%]{left:-27px;top:25px;width:10px;height:10px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-dot.fitness-dot[_ngcontent-%COMP%]{width:12px;height:12px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-date[_ngcontent-%COMP%]{font-size:.85rem;margin-bottom:8px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.2rem;line-height:1.3;margin-bottom:6px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.95rem;margin-bottom:12px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .responsibility-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:.9rem;line-height:1.5;margin-bottom:6px;padding-left:18px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]:hover{transform:translate(3px)}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-subtitle[_ngcontent-%COMP%]{font-size:.95rem}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-highlights[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:12px;margin:15px 0}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-highlights[_ngcontent-%COMP%]   .highlight-card[_ngcontent-%COMP%]{padding:12px;font-size:.9rem}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-highlights[_ngcontent-%COMP%]   .highlight-card[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-achievements[_ngcontent-%COMP%]{padding:15px;margin:15px 0}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-achievements[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:.85rem;line-height:1.4}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-connection[_ngcontent-%COMP%]{padding:15px;margin-top:15px}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-connection[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:8px}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-connection[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}.timeline[_ngcontent-%COMP%]   .timeline-item.fitness-item[_ngcontent-%COMP%]   .fitness-connection[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;line-height:1.6}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category[_ngcontent-%COMP%]{padding:20px}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{font-size:1.2rem;margin-bottom:12px}.qualifications-grid[_ngcontent-%COMP%]   .qualification-category[_ngcontent-%COMP%]   .qualification-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:.9rem;line-height:1.5;margin-bottom:8px;padding-left:20px}}@media (max-width: 480px){.about-container[_ngcontent-%COMP%]{padding:60px 10px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2rem}.about-content[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%]{padding:20px 15px}.about-content[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.6rem}.about-content[_ngcontent-%COMP%]   .about-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.95rem}.personal-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{padding:10px;flex-direction:column;align-items:flex-start;gap:5px}.personal-info[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]{min-width:auto;font-weight:700}.hobbies-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(80px,1fr))}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]{padding:12px 6px}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.3rem}.hobbies-grid[_ngcontent-%COMP%]   .hobby-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.75rem}.timeline[_ngcontent-%COMP%]{padding-left:15px}.timeline[_ngcontent-%COMP%]:before{left:7px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]{padding:15px 12px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-dot[_ngcontent-%COMP%]{left:-22px;width:8px;height:8px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-dot.fitness-dot[_ngcontent-%COMP%]{width:10px;height:10px}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.1rem}.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .responsibility-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:.85rem;padding-left:15px}}']})};function kE(e,t){if(e&1&&(d(0,"div",11)(1,"div",12)(2,"span",13),m(3),f(),d(4,"span",14),m(5),f()(),d(6,"div",15),C(7,"div",16),f()()),e&2){let n=t.$implicit;g(3),S(n.name),g(2),Pt("",n.level,"%"),g(2),Ae("width",n.level,"%")}}function FE(e,t){if(e&1&&(d(0,"div",7)(1,"h3",8),m(2),f(),d(3,"div",9),N(4,kE,8,4,"div",10),f()()),e&2){let n=t.$implicit,r=Q();g(2),S(n),g(2),b("ngForOf",r.getSkillsByCategory(n))}}var Da=class e{constructor(t){this.portfolioService=t;this.skills=this.portfolioService.getSkills(),this.skillCategories=[...new Set(this.skills.map(n=>n.category))]}skills;skillCategories;getSkillsByCategory(t){return this.portfolioService.getSkillsByCategory(t)}static \u0275fac=function(n){return new(n||e)(P(_e))};static \u0275cmp=ne({type:e,selectors:[["app-skills"]],decls:8,vars:2,consts:[[1,"skills-container"],["animationType","gradient","intensity","low",3,"particleCount"],[1,"section-header"],[1,"section-title"],[1,"section-divider"],[1,"skills-content"],["class","skills-categories",4,"ngFor","ngForOf"],[1,"skills-categories"],[1,"category-title"],[1,"skills-grid"],["class","skill-item",4,"ngFor","ngForOf"],[1,"skill-item"],[1,"skill-info"],[1,"skill-name"],[1,"skill-percentage"],[1,"skill-bar"],[1,"skill-progress"]],template:function(n,r){n&1&&(d(0,"div",0),C(1,"app-background-animation",1),d(2,"div",2)(3,"h2",3),m(4,"Skills & Technologies"),f(),C(5,"div",4),f(),d(6,"div",5),N(7,FE,5,2,"div",6),f()()),n&2&&(g(),b("particleCount",15),g(6),b("ngForOf",r.skillCategories))},dependencies:[ge,Ne,Kt],styles:['.skills-container[_ngcontent-%COMP%]{padding:100px 20px;background-color:#fff;min-height:100vh}.section-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:80px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:3rem;font-weight:700;color:#2c3e50;margin-bottom:20px;position:relative}.section-header[_ngcontent-%COMP%]   .section-divider[_ngcontent-%COMP%]{width:80px;height:4px;background:linear-gradient(90deg,#4a6cf7,#764ba2);margin:0 auto;border-radius:2px}.skills-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.skills-categories[_ngcontent-%COMP%]{margin-bottom:60px}.skills-categories[_ngcontent-%COMP%]   .category-title[_ngcontent-%COMP%]{font-size:2rem;color:#2c3e50;margin-bottom:30px;font-weight:600;position:relative;padding-left:20px}.skills-categories[_ngcontent-%COMP%]   .category-title[_ngcontent-%COMP%]:before{content:"";position:absolute;left:0;top:50%;transform:translateY(-50%);width:8px;height:30px;background:#4a6cf7;border-radius:4px}.skills-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(400px,1fr));gap:30px}.skill-item[_ngcontent-%COMP%]{background:#f8f9fa;padding:20px;border-radius:10px;box-shadow:0 5px 15px #0000000d;transition:all .3s ease}.skill-item[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 25px #0000001a}.skill-item[_ngcontent-%COMP%]   .skill-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:10px}.skill-item[_ngcontent-%COMP%]   .skill-info[_ngcontent-%COMP%]   .skill-name[_ngcontent-%COMP%]{font-weight:600;color:#2c3e50}.skill-item[_ngcontent-%COMP%]   .skill-info[_ngcontent-%COMP%]   .skill-percentage[_ngcontent-%COMP%]{color:#4a6cf7;font-weight:600}.skill-item[_ngcontent-%COMP%]   .skill-bar[_ngcontent-%COMP%]{height:10px;background:#6663;border-radius:5px;overflow:hidden}.skill-item[_ngcontent-%COMP%]   .skill-bar[_ngcontent-%COMP%]   .skill-progress[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,#4a6cf7,#764ba2);border-radius:5px;transition:width 1s ease-in-out}@media (max-width: 768px){.skills-container[_ngcontent-%COMP%]{padding:80px 15px}.section-header[_ngcontent-%COMP%]{margin-bottom:60px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2.5rem;line-height:1.2}.skills-categories[_ngcontent-%COMP%]{margin-bottom:40px}.skills-categories[_ngcontent-%COMP%]   .category-title[_ngcontent-%COMP%]{font-size:1.8rem;margin-bottom:25px;padding-left:15px}.skills-categories[_ngcontent-%COMP%]   .category-title[_ngcontent-%COMP%]:before{width:6px;height:25px}.skills-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:20px}.skill-item[_ngcontent-%COMP%]{padding:18px}.skill-item[_ngcontent-%COMP%]   .skill-info[_ngcontent-%COMP%]{margin-bottom:8px}.skill-item[_ngcontent-%COMP%]   .skill-info[_ngcontent-%COMP%]   .skill-name[_ngcontent-%COMP%]{font-size:.95rem}.skill-item[_ngcontent-%COMP%]   .skill-info[_ngcontent-%COMP%]   .skill-percentage[_ngcontent-%COMP%]{font-size:.9rem}.skill-item[_ngcontent-%COMP%]   .skill-bar[_ngcontent-%COMP%]{height:8px}}@media (max-width: 480px){.skills-container[_ngcontent-%COMP%]{padding:60px 10px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2rem}.skills-categories[_ngcontent-%COMP%]   .category-title[_ngcontent-%COMP%]{font-size:1.6rem;padding-left:12px}.skill-item[_ngcontent-%COMP%]{padding:15px}.skill-item[_ngcontent-%COMP%]   .skill-info[_ngcontent-%COMP%]   .skill-name[_ngcontent-%COMP%]{font-size:.9rem}.skill-item[_ngcontent-%COMP%]   .skill-info[_ngcontent-%COMP%]   .skill-percentage[_ngcontent-%COMP%]{font-size:.85rem}.skill-item[_ngcontent-%COMP%]   .skill-bar[_ngcontent-%COMP%]{height:6px}}']})};var gv=(()=>{class e{_renderer;_elementRef;onChange=n=>{};onTouched=()=>{};constructor(n,r){this._renderer=n,this._elementRef=r}setProperty(n,r){this._renderer.setProperty(this._elementRef.nativeElement,n,r)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static \u0275fac=function(r){return new(r||e)(P(Gt),P(Xe))};static \u0275dir=ce({type:e})}return e})(),yd=(()=>{class e extends gv{static \u0275fac=(()=>{let n;return function(o){return(n||(n=mn(e)))(o||e)}})();static \u0275dir=ce({type:e,features:[tt]})}return e})(),Wo=new D("");var LE={provide:Wo,useExisting:It(()=>Aa),multi:!0};function jE(){let e=qe()?qe().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var VE=new D(""),Aa=(()=>{class e extends gv{_compositionMode;_composing=!1;constructor(n,r,o){super(n,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!jE())}writeValue(n){let r=n??"";this.setProperty("value",r)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static \u0275fac=function(r){return new(r||e)(P(Gt),P(Xe),P(VE,8))};static \u0275dir=ce({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&q("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[Cn([LE]),tt]})}return e})();function _d(e){return e==null||Cd(e)===0}function Cd(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var bd=new D(""),Md=new D(""),UE=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,yt=class{static min(t){return BE(t)}static max(t){return HE(t)}static required(t){return $E(t)}static requiredTrue(t){return zE(t)}static email(t){return GE(t)}static minLength(t){return qE(t)}static maxLength(t){return WE(t)}static pattern(t){return YE(t)}static nullValidator(t){return mv()}static compose(t){return Mv(t)}static composeAsync(t){return wv(t)}};function BE(e){return t=>{if(t.value==null||e==null)return null;let n=parseFloat(t.value);return!isNaN(n)&&n<e?{min:{min:e,actual:t.value}}:null}}function HE(e){return t=>{if(t.value==null||e==null)return null;let n=parseFloat(t.value);return!isNaN(n)&&n>e?{max:{max:e,actual:t.value}}:null}}function $E(e){return _d(e.value)?{required:!0}:null}function zE(e){return e.value===!0?null:{required:!0}}function GE(e){return _d(e.value)||UE.test(e.value)?null:{email:!0}}function qE(e){return t=>{let n=t.value?.length??Cd(t.value);return n===null||n===0?null:n<e?{minlength:{requiredLength:e,actualLength:n}}:null}}function WE(e){return t=>{let n=t.value?.length??Cd(t.value);return n!==null&&n>e?{maxlength:{requiredLength:e,actualLength:n}}:null}}function YE(e){if(!e)return mv;let t,n;return typeof e=="string"?(n="",e.charAt(0)!=="^"&&(n+="^"),n+=e,e.charAt(e.length-1)!=="$"&&(n+="$"),t=new RegExp(n)):(n=e.toString(),t=e),r=>{if(_d(r.value))return null;let o=r.value;return t.test(o)?null:{pattern:{requiredPattern:n,actualValue:o}}}}function mv(e){return null}function vv(e){return e!=null}function yv(e){return vn(e)?K(e):e}function _v(e){let t={};return e.forEach(n=>{t=n!=null?M(M({},t),n):t}),Object.keys(t).length===0?null:t}function Cv(e,t){return t.map(n=>n(e))}function ZE(e){return!e.validate}function bv(e){return e.map(t=>ZE(t)?t:n=>t.validate(n))}function Mv(e){if(!e)return null;let t=e.filter(vv);return t.length==0?null:function(n){return _v(Cv(n,t))}}function Dv(e){return e!=null?Mv(bv(e)):null}function wv(e){if(!e)return null;let t=e.filter(vv);return t.length==0?null:function(n){let r=Cv(n,t).map(yv);return Cc(r).pipe(A(_v))}}function Ev(e){return e!=null?wv(bv(e)):null}function sv(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function Iv(e){return e._rawValidators}function xv(e){return e._rawAsyncValidators}function fd(e){return e?Array.isArray(e)?e:[e]:[]}function Ea(e,t){return Array.isArray(e)?e.includes(t):e===t}function av(e,t){let n=fd(t);return fd(e).forEach(o=>{Ea(n,o)||n.push(o)}),n}function cv(e,t){return fd(t).filter(n=>!Ea(e,n))}var Ia=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=Dv(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=Ev(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},Rn=class extends Ia{name;get formDirective(){return null}get path(){return null}},kn=class extends Ia{_parent=null;name=null;valueAccessor=null},xa=class{_cd;constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},QE={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},C2=U(M({},QE),{"[class.ng-submitted]":"isSubmitted"}),Na=(()=>{class e extends xa{constructor(n){super(n)}static \u0275fac=function(r){return new(r||e)(P(kn,2))};static \u0275dir=ce({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&fe("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[tt]})}return e})(),Sv=(()=>{class e extends xa{constructor(n){super(n)}static \u0275fac=function(r){return new(r||e)(P(Rn,10))};static \u0275dir=ce({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&fe("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[tt]})}return e})();var Ho="VALID",wa="INVALID",kr="PENDING",$o="DISABLED",Xt=class{},Sa=class extends Xt{value;source;constructor(t,n){super(),this.value=t,this.source=n}},zo=class extends Xt{pristine;source;constructor(t,n){super(),this.pristine=t,this.source=n}},Go=class extends Xt{touched;source;constructor(t,n){super(),this.touched=t,this.source=n}},Fr=class extends Xt{status;source;constructor(t,n){super(),this.status=t,this.source=n}},pd=class extends Xt{source;constructor(t){super(),this.source=t}},hd=class extends Xt{source;constructor(t){super(),this.source=t}};function Dd(e){return(Ra(e)?e.validators:e)||null}function JE(e){return Array.isArray(e)?Dv(e):e||null}function wd(e,t){return(Ra(t)?t.asyncValidators:e)||null}function KE(e){return Array.isArray(e)?Ev(e):e||null}function Ra(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function Ov(e,t,n){let r=e.controls;if(!(t?Object.keys(r):r).length)throw new E(1e3,"");if(!r[n])throw new E(1001,"")}function Pv(e,t,n){e._forEachChild((r,o)=>{if(n[o]===void 0)throw new E(1002,"")})}var Lr=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(t,n){this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return Tt(this.statusReactive)}set status(t){Tt(()=>this.statusReactive.set(t))}_status=uo(()=>this.statusReactive());statusReactive=ao(void 0);get valid(){return this.status===Ho}get invalid(){return this.status===wa}get pending(){return this.status==kr}get disabled(){return this.status===$o}get enabled(){return this.status!==$o}errors;get pristine(){return Tt(this.pristineReactive)}set pristine(t){Tt(()=>this.pristineReactive.set(t))}_pristine=uo(()=>this.pristineReactive());pristineReactive=ao(!0);get dirty(){return!this.pristine}get touched(){return Tt(this.touchedReactive)}set touched(t){Tt(()=>this.touchedReactive.set(t))}_touched=uo(()=>this.touchedReactive());touchedReactive=ao(!1);get untouched(){return!this.touched}_events=new re;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(av(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(av(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(cv(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(cv(t,this._rawAsyncValidators))}hasValidator(t){return Ea(this._rawValidators,t)}hasAsyncValidator(t){return Ea(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(U(M({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new Go(!0,r))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new Go(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(U(M({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new zo(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new zo(!0,r))}markAsPending(t={}){this.status=kr;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Fr(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(U(M({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=$o,this.errors=null,this._forEachChild(o=>{o.disable(U(M({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Sa(this.value,r)),this._events.next(new Fr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(U(M({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=Ho,this._forEachChild(r=>{r.enable(U(M({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(U(M({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Ho||this.status===kr)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Sa(this.value,n)),this._events.next(new Fr(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(U(M({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?$o:Ho}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=kr,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let r=yv(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,o)=>r&&r._find(o),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new Fr(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new ae,this.statusChanges=new ae}_calculateStatus(){return this._allControlsDisabled()?$o:this.errors?wa:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(kr)?kr:this._anyControlsHaveStatus(wa)?wa:Ho}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),o&&this._events.next(new zo(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new Go(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_onDisabledChange=[];_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){Ra(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=JE(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=KE(this._rawAsyncValidators)}},Oa=class extends Lr{constructor(t,n,r){super(Dd(n),wd(r,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(t,n){return this.controls[t]?this.controls[t]:(this.controls[t]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(t,n,r={}){this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(t,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(t,n,r={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],n&&this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,n={}){Pv(this,!0,t),Object.keys(t).forEach(r=>{Ov(this,!0,r),this.controls[r].setValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){t!=null&&(Object.keys(t).forEach(r=>{let o=this.controls[r];o&&o.patchValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t={},n={}){this._forEachChild((r,o)=>{r.reset(t?t[o]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n,this),this._updateTouched(n,this),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(t,n,r)=>(t[r]=n.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(n,r)=>r._syncPendingControls()?!0:n);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(n=>{let r=this.controls[n];r&&t(r,n)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[n,r]of Object.entries(this.controls))if(this.contains(n)&&t(r))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(n,r,o)=>((r.enabled||this.disabled)&&(n[o]=r.value),n))}_reduceChildren(t,n){let r=t;return this._forEachChild((o,i)=>{r=n(r,o,i)}),r}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var gd=class extends Oa{};var ka=new D("",{providedIn:"root",factory:()=>Fa}),Fa="always";function Tv(e,t){return[...t.path,e]}function md(e,t,n=Fa){Ed(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),eI(e,t),nI(e,t),tI(e,t),XE(e,t)}function lv(e,t,n=!0){let r=()=>{};t.valueAccessor&&(t.valueAccessor.registerOnChange(r),t.valueAccessor.registerOnTouched(r)),Ta(e,t),e&&(t._invokeOnDestroyCallbacks(),e._registerOnCollectionChange(()=>{}))}function Pa(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function XE(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function Ed(e,t){let n=Iv(e);t.validator!==null?e.setValidators(sv(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=xv(e);t.asyncValidator!==null?e.setAsyncValidators(sv(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();Pa(t._rawValidators,o),Pa(t._rawAsyncValidators,o)}function Ta(e,t){let n=!1;if(e!==null){if(t.validator!==null){let o=Iv(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==t.validator);i.length!==o.length&&(n=!0,e.setValidators(i))}}if(t.asyncValidator!==null){let o=xv(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==t.asyncValidator);i.length!==o.length&&(n=!0,e.setAsyncValidators(i))}}}let r=()=>{};return Pa(t._rawValidators,r),Pa(t._rawAsyncValidators,r),n}function eI(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&Av(e,t)})}function tI(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&Av(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function Av(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function nI(e,t){let n=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function rI(e,t){e==null,Ed(e,t)}function oI(e,t){return Ta(e,t)}function Nv(e,t){if(!e.hasOwnProperty("model"))return!1;let n=e.model;return n.isFirstChange()?!0:!Object.is(t,n.currentValue)}function iI(e){return Object.getPrototypeOf(e.constructor)===yd}function sI(e,t){e._syncPendingControls(),t.forEach(n=>{let r=n.control;r.updateOn==="submit"&&r._pendingChange&&(n.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function Rv(e,t){if(!t)return null;Array.isArray(t);let n,r,o;return t.forEach(i=>{i.constructor===Aa?n=i:iI(i)?r=i:o=i}),o||r||n||null}function aI(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function uv(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function dv(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var qo=class extends Lr{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(t=null,n,r){super(Dd(n),wd(r,n)),this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Ra(n)&&(n.nonNullable||n.initialValueIsDefault)&&(dv(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&n.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,n.emitViewToModelChange!==!1)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){uv(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){uv(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){dv(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var cI=e=>e instanceof qo;var lI={provide:kn,useExisting:It(()=>Id)},fv=Promise.resolve(),Id=(()=>{class e extends kn{_changeDetectorRef;callSetDisabledState;control=new qo;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new ae;constructor(n,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=n,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=Rv(this,i)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let r=n.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),Nv(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){md(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){fv.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){let r=n.isDisabled.currentValue,o=r!==0&&xs(r);fv.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?Tv(n,this._parent):[n]}static \u0275fac=function(r){return new(r||e)(P(Rn,9),P(bd,10),P(Md,10),P(Wo,10),P(bn,8),P(ka,8))};static \u0275dir=ce({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[Cn([lI]),tt,$t]})}return e})();var kv=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=ce({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})();var Fv=new D("");var uI={provide:Rn,useExisting:It(()=>xd)},xd=(()=>{class e extends Rn{callSetDisabledState;get submitted(){return Tt(this._submittedReactive)}set submitted(n){this._submittedReactive.set(n)}_submitted=uo(()=>this._submittedReactive());_submittedReactive=ao(!1);_oldForm;_onCollectionChange=()=>this._updateDomValue();directives=[];form=null;ngSubmit=new ae;constructor(n,r,o){super(),this.callSetDisabledState=o,this._setValidators(n),this._setAsyncValidators(r)}ngOnChanges(n){n.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(Ta(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(n){let r=this.form.get(n.path);return md(r,n,this.callSetDisabledState),r.updateValueAndValidity({emitEvent:!1}),this.directives.push(n),r}getControl(n){return this.form.get(n.path)}removeControl(n){lv(n.control||null,n,!1),aI(this.directives,n)}addFormGroup(n){this._setUpFormContainer(n)}removeFormGroup(n){this._cleanUpFormContainer(n)}getFormGroup(n){return this.form.get(n.path)}addFormArray(n){this._setUpFormContainer(n)}removeFormArray(n){this._cleanUpFormContainer(n)}getFormArray(n){return this.form.get(n.path)}updateModel(n,r){this.form.get(n.path).setValue(r)}onSubmit(n){return this._submittedReactive.set(!0),sI(this.form,this.directives),this.ngSubmit.emit(n),this.form._events.next(new pd(this.control)),n?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this._submittedReactive.set(!1),this.form._events.next(new hd(this.form))}_updateDomValue(){this.directives.forEach(n=>{let r=n.control,o=this.form.get(n.path);r!==o&&(lv(r||null,n),cI(o)&&(md(o,n,this.callSetDisabledState),n.control=o))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(n){let r=this.form.get(n.path);rI(r,n),r.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(n){if(this.form){let r=this.form.get(n.path);r&&oI(r,n)&&r.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){Ed(this.form,this),this._oldForm&&Ta(this._oldForm,this)}static \u0275fac=function(r){return new(r||e)(P(bd,10),P(Md,10),P(ka,8))};static \u0275dir=ce({type:e,selectors:[["","formGroup",""]],hostBindings:function(r,o){r&1&&q("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{form:[0,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[Cn([uI]),tt,$t]})}return e})();var dI={provide:kn,useExisting:It(()=>Sd)},Sd=(()=>{class e extends kn{_ngModelWarningConfig;_added=!1;viewModel;control;name=null;set isDisabled(n){}model;update=new ae;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(n,r,o,i,s){super(),this._ngModelWarningConfig=s,this._parent=n,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=Rv(this,i)}ngOnChanges(n){this._added||this._setUpControl(),Nv(n,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}get path(){return Tv(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_setUpControl(){this.control=this.formDirective.addControl(this),this._added=!0}static \u0275fac=function(r){return new(r||e)(P(Rn,13),P(bd,10),P(Md,10),P(Wo,10),P(Fv,8))};static \u0275dir=ce({type:e,selectors:[["","formControlName",""]],inputs:{name:[0,"formControlName","name"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},standalone:!1,features:[Cn([dI]),tt,$t]})}return e})();var fI={provide:Wo,useExisting:It(()=>La),multi:!0};function Lv(e,t){return e==null?`${t}`:(t&&typeof t=="object"&&(t="Object"),`${e}: ${t}`.slice(0,50))}function pI(e){return e.split(":")[0]}var La=(()=>{class e extends yd{value;_optionMap=new Map;_idCounter=0;set compareWith(n){this._compareWith=n}_compareWith=Object.is;writeValue(n){this.value=n;let r=this._getOptionId(n),o=Lv(r,n);this.setProperty("value",o)}registerOnChange(n){this.onChange=r=>{this.value=this._getOptionValue(r),n(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(n){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r),n))return r;return null}_getOptionValue(n){let r=pI(n);return this._optionMap.has(r)?this._optionMap.get(r):n}static \u0275fac=(()=>{let n;return function(o){return(n||(n=mn(e)))(o||e)}})();static \u0275dir=ce({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(r,o){r&1&&q("change",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[Cn([fI]),tt]})}return e})(),jv=(()=>{class e{_element;_renderer;_select;id;constructor(n,r,o){this._element=n,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption())}set ngValue(n){this._select!=null&&(this._select._optionMap.set(this.id,n),this._setElementValue(Lv(this.id,n)),this._select.writeValue(this._select.value))}set value(n){this._setElementValue(n),this._select&&this._select.writeValue(this._select.value)}_setElementValue(n){this._renderer.setProperty(this._element.nativeElement,"value",n)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(P(Xe),P(Gt),P(La,9))};static \u0275dir=ce({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})(),hI={provide:Wo,useExisting:It(()=>Vv),multi:!0};function pv(e,t){return e==null?`${t}`:(typeof t=="string"&&(t=`'${t}'`),t&&typeof t=="object"&&(t="Object"),`${e}: ${t}`.slice(0,50))}function gI(e){return e.split(":")[0]}var Vv=(()=>{class e extends yd{value;_optionMap=new Map;_idCounter=0;set compareWith(n){this._compareWith=n}_compareWith=Object.is;writeValue(n){this.value=n;let r;if(Array.isArray(n)){let o=n.map(i=>this._getOptionId(i));r=(i,s)=>{i._setSelected(o.indexOf(s.toString())>-1)}}else r=(o,i)=>{o._setSelected(!1)};this._optionMap.forEach(r)}registerOnChange(n){this.onChange=r=>{let o=[],i=r.selectedOptions;if(i!==void 0){let s=i;for(let a=0;a<s.length;a++){let c=s[a],l=this._getOptionValue(c.value);o.push(l)}}else{let s=r.options;for(let a=0;a<s.length;a++){let c=s[a];if(c.selected){let l=this._getOptionValue(c.value);o.push(l)}}}this.value=o,n(o)}}_registerOption(n){let r=(this._idCounter++).toString();return this._optionMap.set(r,n),r}_getOptionId(n){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r)._value,n))return r;return null}_getOptionValue(n){let r=gI(n);return this._optionMap.has(r)?this._optionMap.get(r)._value:n}static \u0275fac=(()=>{let n;return function(o){return(n||(n=mn(e)))(o||e)}})();static \u0275dir=ce({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(r,o){r&1&&q("change",function(s){return o.onChange(s.target)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[Cn([hI]),tt]})}return e})(),Uv=(()=>{class e{_element;_renderer;_select;id;_value;constructor(n,r,o){this._element=n,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption(this))}set ngValue(n){this._select!=null&&(this._value=n,this._setElementValue(pv(this.id,n)),this._select.writeValue(this._select.value))}set value(n){this._select?(this._value=n,this._setElementValue(pv(this.id,n)),this._select.writeValue(this._select.value)):this._setElementValue(n)}_setElementValue(n){this._renderer.setProperty(this._element.nativeElement,"value",n)}_setSelected(n){this._renderer.setProperty(this._element.nativeElement,"selected",n)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(P(Xe),P(Gt),P(Vv,9))};static \u0275dir=ce({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})();var Bv=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=et({type:e});static \u0275inj=Je({})}return e})(),vd=class extends Lr{constructor(t,n,r){super(Dd(n),wd(r,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;at(t){return this.controls[this._adjustIndex(t)]}push(t,n={}){this.controls.push(t),this._registerControl(t),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}insert(t,n,r={}){this.controls.splice(t,0,n),this._registerControl(n),this.updateValueAndValidity({emitEvent:r.emitEvent})}removeAt(t,n={}){let r=this._adjustIndex(t);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),this.updateValueAndValidity({emitEvent:n.emitEvent})}setControl(t,n,r={}){let o=this._adjustIndex(t);o<0&&(o=0),this.controls[o]&&this.controls[o]._registerOnCollectionChange(()=>{}),this.controls.splice(o,1),n&&(this.controls.splice(o,0,n),this._registerControl(n)),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(t,n={}){Pv(this,!1,t),t.forEach((r,o)=>{Ov(this,!1,o),this.at(o).setValue(r,{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){t!=null&&(t.forEach((r,o)=>{this.at(o)&&this.at(o).patchValue(r,{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t=[],n={}){this._forEachChild((r,o)=>{r.reset(t[o],{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n,this),this._updateTouched(n,this),this.updateValueAndValidity(n)}getRawValue(){return this.controls.map(t=>t.getRawValue())}clear(t={}){this.controls.length<1||(this._forEachChild(n=>n._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:t.emitEvent}))}_adjustIndex(t){return t<0?t+this.length:t}_syncPendingControls(){let t=this.controls.reduce((n,r)=>r._syncPendingControls()?!0:n,!1);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){this.controls.forEach((n,r)=>{t(n,r)})}_updateValue(){this.value=this.controls.filter(t=>t.enabled||this.disabled).map(t=>t.value)}_anyControls(t){return this.controls.some(n=>n.enabled&&t(n))}_setUpControls(){this._forEachChild(t=>this._registerControl(t))}_allControlsDisabled(){for(let t of this.controls)if(t.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(t){t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)}_find(t){return this.at(t)??null}};function hv(e){return!!e&&(e.asyncValidators!==void 0||e.validators!==void 0||e.updateOn!==void 0)}var Hv=(()=>{class e{useNonNullable=!1;get nonNullable(){let n=new e;return n.useNonNullable=!0,n}group(n,r=null){let o=this._reduceControls(n),i={};return hv(r)?i=r:r!==null&&(i.validators=r.validator,i.asyncValidators=r.asyncValidator),new Oa(o,i)}record(n,r=null){let o=this._reduceControls(n);return new gd(o,r)}control(n,r,o){let i={};return this.useNonNullable?(hv(r)?i=r:(i.validators=r,i.asyncValidators=o),new qo(n,U(M({},i),{nonNullable:!0}))):new qo(n,r,o)}array(n,r,o){let i=n.map(s=>this._createControl(s));return new vd(i,r,o)}_reduceControls(n){let r={};return Object.keys(n).forEach(o=>{r[o]=this._createControl(n[o])}),r}_createControl(n){if(n instanceof qo)return n;if(n instanceof Lr)return n;if(Array.isArray(n)){let r=n[0],o=n.length>1?n[1]:null,i=n.length>2?n[2]:null;return this.control(r,o,i)}else return this.control(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var ja=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:ka,useValue:n.callSetDisabledState??Fa}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=et({type:e});static \u0275inj=Je({imports:[Bv]})}return e})(),$v=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Fv,useValue:n.warnOnNgModelWithFormControl??"always"},{provide:ka,useValue:n.callSetDisabledState??Fa}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=et({type:e});static \u0275inj=Je({imports:[Bv]})}return e})();var mI=()=>[1,2,3,4,5,6],vI=()=>[1,2,3];function yI(e,t){if(e&1&&(d(0,"option",20),m(1),f()),e&2){let n=t.$implicit;b("value",n.value),g(),_n(" ",n.label," (",n.count,") ")}}function _I(e,t){if(e&1&&(d(0,"option",20),m(1),f()),e&2){let n=t.$implicit;b("value",n.value),g(),Pt(" ",n.label," ")}}function CI(e,t){if(e&1&&(d(0,"div",22)(1,"span",23),m(2),f(),d(3,"span",24),m(4,"Featured"),f()()),e&2){let n=Q(2);g(2),S(n.featuredProjects.length)}}function bI(e,t){if(e&1&&(d(0,"div",22)(1,"span",23),m(2),f(),d(3,"span",24),m(4,"From GitHub"),f()()),e&2){let n=Q(2);g(2),S(n.getGitHubProjectsCount())}}function MI(e,t){if(e&1&&(d(0,"div",21)(1,"div",22)(2,"span",23),m(3),f(),d(4,"span",24),m(5),f()(),N(6,CI,5,1,"div",25)(7,bI,5,1,"div",25),f()),e&2){let n=Q();g(3),S(n.projects.length),g(2),S(n.projects.length===1?"Project":"Projects"),g(),b("ngIf",n.featuredProjects.length>0),g(),b("ngIf",n.hasGitHubProjects())}}function DI(e,t){e&1&&C(0,"div",36)}function wI(e,t){e&1&&(d(0,"div",29),C(1,"div",30),d(2,"div",31),C(3,"div",32)(4,"div",33),d(5,"div",34),N(6,DI,1,0,"div",35),f()()()),e&2&&(g(6),b("ngForOf",pu(1,vI)))}function EI(e,t){e&1&&(d(0,"div",26)(1,"div",27),N(2,wI,7,2,"div",28),f()()),e&2&&(g(2),b("ngForOf",pu(1,mI)))}function II(e,t){if(e&1){let n=ws();d(0,"div",37)(1,"div",38),C(2,"i",39),d(3,"h3"),m(4,"Oops! Something went wrong"),f(),d(5,"p"),m(6),f(),d(7,"button",40),q("click",function(){ps(n);let o=Q();return hs(o.retryLoading())}),C(8,"i",41),m(9," Try Again "),f()()()}if(e&2){let n=Q();g(6),S(n.errorMessage)}}function xI(e,t){if(e&1&&(d(0,"a",58),C(1,"i",59),f()),e&2){let n=Q().$implicit;b("href",n.githubUrl,he)}}function SI(e,t){if(e&1&&(d(0,"a",58),C(1,"i",60),f()),e&2){let n=Q().$implicit;b("href",n.liveUrl,he)}}function OI(e,t){if(e&1&&(d(0,"div",51)(1,"div",52),C(2,"img",53),d(3,"div",54)(4,"div",55)(5,"h4"),m(6),f(),d(7,"p"),m(8),f(),d(9,"div",56),N(10,xI,2,1,"a",57)(11,SI,2,1,"a",57),f()()()()()),e&2){let n=t.$implicit,r=t.index,o=Q(3);Ae("animation-delay",r*.2+"s"),g(2),b("src",n.imageUrl||"/Image_fx.jpg",he)("alt",o.getProjectImageAlt(n)),g(4),S(n.title),g(2),S(n.description),g(2),b("ngIf",n.githubUrl),g(),b("ngIf",n.liveUrl)}}function PI(e,t){if(e&1&&(d(0,"div",47)(1,"h3",48),C(2,"i",15),m(3," Featured Projects "),f(),d(4,"div",49),N(5,OI,12,8,"div",50),f()()),e&2){let n=Q(2);g(5),b("ngForOf",n.featuredProjects.slice(0,2))}}function TI(e,t){if(e&1&&(d(0,"a",79),C(1,"i",59),f()),e&2){let n=Q().$implicit;b("href",n.githubUrl,he)}}function AI(e,t){if(e&1&&(d(0,"a",79),C(1,"i",60),f()),e&2){let n=Q().$implicit;b("href",n.liveUrl,he)}}function NI(e,t){e&1&&(d(0,"span",80),C(1,"i",15),m(2," Featured "),f())}function RI(e,t){e&1&&(d(0,"span",81),C(1,"i",59),m(2," GitHub "),f())}function kI(e,t){if(e&1&&(d(0,"div",22),C(1,"i",15),d(2,"span"),m(3),f(),d(4,"small"),m(5,"stars"),f()()),e&2){let n=Q(2).$implicit;g(3),S(n.stars)}}function FI(e,t){if(e&1&&(d(0,"div",22),C(1,"i",82),d(2,"span"),m(3),f()()),e&2){let n=Q(2).$implicit;g(3),S(n.language)}}function LI(e,t){if(e&1&&(d(0,"div",22),C(1,"i",83),d(2,"span"),m(3),f()()),e&2){let n=Q(2).$implicit,r=Q(2);g(3),S(r.formatLastUpdated(n.lastUpdated))}}function jI(e,t){if(e&1&&(d(0,"div",21),N(1,kI,6,1,"div",25)(2,FI,4,1,"div",25)(3,LI,4,1,"div",25),f()),e&2){let n=Q().$implicit;g(),b("ngIf",n.stars!==void 0),g(),b("ngIf",n.language),g(),b("ngIf",n.lastUpdated)}}function VI(e,t){if(e&1&&(d(0,"span",84),m(1),f()),e&2){let n=t.$implicit;g(),S(n)}}function UI(e,t){if(e&1&&(d(0,"span",85),m(1),f()),e&2){let n=Q().$implicit;g(),Pt(" +",n.technologies.length-4," ")}}function BI(e,t){if(e&1&&(d(0,"a",86),C(1,"i",59),d(2,"span"),m(3,"Code"),f()()),e&2){let n=Q().$implicit;b("href",n.githubUrl,he)}}function HI(e,t){if(e&1&&(d(0,"a",87),C(1,"i",60),d(2,"span"),m(3,"Demo"),f()()),e&2){let n=Q().$implicit;b("href",n.liveUrl,he)}}function $I(e,t){if(e&1&&(d(0,"div",61)(1,"div",62),C(2,"img",53),d(3,"div",63)(4,"div",64),N(5,TI,2,1,"a",65)(6,AI,2,1,"a",65),f()()(),d(7,"div",66)(8,"div",67)(9,"h3",68),m(10),f(),d(11,"div",69),N(12,NI,3,0,"span",70)(13,RI,3,0,"span",71),f()(),d(14,"p",72),m(15),f(),N(16,jI,4,3,"div",16),d(17,"div",73),N(18,VI,2,1,"span",74)(19,UI,2,1,"span",75),f(),d(20,"div",76),N(21,BI,4,1,"a",77)(22,HI,4,1,"a",78),f()()()),e&2){let n=t.$implicit,r=t.index,o=Q(2);Ae("animation-delay",r*.1+"s"),fe("featured",n.featured)("github-project",n.isFromGitHub),g(2),b("src",n.imageUrl||"/Image_fx.jpg",he)("alt",o.getProjectImageAlt(n)),g(3),b("ngIf",n.githubUrl),g(),b("ngIf",n.liveUrl),g(4),S(n.title),g(2),b("ngIf",n.featured),g(),b("ngIf",n.isFromGitHub),g(2),S(n.description),g(),b("ngIf",n.stars!==void 0||n.lastUpdated||n.language),g(2),b("ngForOf",n.technologies.slice(0,4)),g(),b("ngIf",n.technologies.length>4),g(2),b("ngIf",n.githubUrl),g(),b("ngIf",n.liveUrl)}}function zI(e,t){if(e&1){let n=ws();d(0,"p"),m(1),d(2,"button",91),q("click",function(){ps(n);let o=Q(3);return hs(o.onFilterChange("all"))}),m(3,"Show all projects"),f()()}if(e&2){let n=Q(3);g(),Pt(' No projects found for "',n.getSelectedFilterLabel(),'". ')}}function GI(e,t){if(e&1){let n=ws();d(0,"p"),m(1," No featured projects available. "),d(2,"button",91),q("click",function(){ps(n);let o=Q(3);return hs(o.toggleFeaturedProjects())}),m(3,"Show all projects"),f()()}}function qI(e,t){e&1&&(d(0,"p"),m(1," No projects available at the moment. "),f())}function WI(e,t){if(e&1&&(d(0,"div",88),C(1,"i",89),d(2,"h3"),m(3,"No projects found"),f(),N(4,zI,4,1,"p",90)(5,GI,4,0,"p",90)(6,qI,2,0,"p",90),f()),e&2){let n=Q(2);g(4),b("ngIf",n.selectedFilter!=="all"),g(),b("ngIf",n.selectedFilter==="all"&&n.showFeaturedOnly),g(),b("ngIf",n.selectedFilter==="all"&&!n.showFeaturedOnly)}}function YI(e,t){if(e&1&&(d(0,"div",42),N(1,PI,6,1,"div",43),d(2,"div",44),N(3,$I,23,19,"div",45),f(),N(4,WI,7,3,"div",46),f()),e&2){let n=Q();g(),b("ngIf",n.featuredProjects.length>0&&!n.showFeaturedOnly&&n.selectedFilter==="all"),g(),Ge("view-"+n.viewMode),g(),b("ngForOf",n.projects),g(),b("ngIf",n.projects.length===0)}}var Va=class e{constructor(t){this.portfolioService=t}projects=[];featuredProjects=[];allProjects=[];filteredProjects=[];isLoading=!0;hasError=!1;errorMessage="";showFeaturedOnly=!1;selectedFilter="all";selectedSort="featured";availableFilters=[];availableSorts=[{value:"featured",label:"Featured First"},{value:"stars",label:"Most Stars"},{value:"updated",label:"Recently Updated"},{value:"name",label:"Name A-Z"}];viewMode="masonry";destroy$=new re;ngOnInit(){this.loadProjects()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}loadProjects(){this.isLoading=!0,this.hasError=!1,this.portfolioService.getAllProjects().pipe(Gr(this.destroy$)).subscribe({next:t=>{this.allProjects=t,this.featuredProjects=t.filter(n=>n.featured),this.setupFilters(),this.applyFiltersAndSort(),this.isLoading=!1},error:t=>{console.error("Error loading projects:",t),this.hasError=!0,this.errorMessage="Failed to load projects. Please try again later.",this.isLoading=!1;let n=this.portfolioService.getProjects();this.allProjects=n,this.featuredProjects=this.portfolioService.getFeaturedProjects(),this.setupFilters(),this.applyFiltersAndSort()}})}setupFilters(){let t=new Set;this.allProjects.forEach(n=>{n.technologies.forEach(r=>t.add(r)),n.language&&t.add(n.language)}),this.availableFilters=[{value:"all",label:"All Projects",count:this.allProjects.length},{value:"featured",label:"Featured",count:this.featuredProjects.length},...Array.from(t).map(n=>({value:n.toLowerCase(),label:n,count:this.allProjects.filter(r=>r.technologies.some(o=>o.toLowerCase()===n.toLowerCase())||r.language?.toLowerCase()===n.toLowerCase()).length}))].sort((n,r)=>n.value==="all"?-1:r.value==="all"?1:n.value==="featured"?-1:r.value==="featured"?1:r.count-n.count)}applyFiltersAndSort(){let t=[...this.allProjects];switch(this.selectedFilter==="featured"?t=t.filter(n=>n.featured):this.selectedFilter!=="all"&&(t=t.filter(n=>n.technologies.some(r=>r.toLowerCase()===this.selectedFilter)||n.language?.toLowerCase()===this.selectedFilter)),this.selectedSort){case"featured":t.sort((n,r)=>n.featured&&!r.featured?-1:!n.featured&&r.featured?1:n.stars&&r.stars?r.stars-n.stars:0);break;case"stars":t.sort((n,r)=>(r.stars||0)-(n.stars||0));break;case"updated":t.sort((n,r)=>!n.lastUpdated&&!r.lastUpdated?0:n.lastUpdated?r.lastUpdated?r.lastUpdated.getTime()-n.lastUpdated.getTime():-1:1);break;case"name":t.sort((n,r)=>n.title.localeCompare(r.title));break}this.filteredProjects=t,this.projects=this.showFeaturedOnly?this.featuredProjects:t}toggleFeaturedProjects(){this.showFeaturedOnly=!this.showFeaturedOnly,this.projects=this.showFeaturedOnly?this.featuredProjects:this.filteredProjects}onFilterChange(t){this.selectedFilter=t,this.applyFiltersAndSort()}onSortChange(t){this.selectedSort=t,this.applyFiltersAndSort()}toggleViewMode(){this.viewMode=this.viewMode==="grid"?"masonry":"grid"}retryLoading(){this.loadProjects()}getProjectImageAlt(t){return`${t.title} - ${t.language||"Project"} screenshot`}formatLastUpdated(t){if(!t)return"";let r=Math.abs(new Date().getTime()-t.getTime()),o=Math.ceil(r/(1e3*60*60*24));return o===1?"1 day ago":o<30?`${o} days ago`:o<365?`${Math.floor(o/30)} months ago`:`${Math.floor(o/365)} years ago`}hasGitHubProjects(){return this.allProjects.some(t=>t.isFromGitHub===!0)}getGitHubProjectsCount(){return this.allProjects.filter(t=>t.isFromGitHub===!0).length}getSelectedFilterLabel(){return this.availableFilters.find(n=>n.value===this.selectedFilter)?.label||this.selectedFilter}static \u0275fac=function(n){return new(n||e)(P(_e))};static \u0275cmp=ne({type:e,selectors:[["app-projects"]],decls:29,vars:20,consts:[[1,"projects-container"],[1,"section-header"],[1,"section-title"],[1,"section-divider"],[1,"section-description"],[1,"projects-controls"],[1,"filter-controls"],[1,"filter-group"],[1,"filter-label"],[1,"filter-select",3,"ngModelChange","change","ngModel","disabled"],[3,"value",4,"ngFor","ngForOf"],[1,"view-controls"],[1,"view-toggle-btn",3,"click","disabled","title"],[1,"fas"],[1,"featured-toggle-btn",3,"click","disabled"],[1,"fas","fa-star"],["class","project-stats",4,"ngIf"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["class","projects-content",4,"ngIf"],[3,"value"],[1,"project-stats"],[1,"stat-item"],[1,"stat-number"],[1,"stat-label"],["class","stat-item",4,"ngIf"],[1,"loading-container"],[1,"loading-skeleton"],["class","skeleton-card",4,"ngFor","ngForOf"],[1,"skeleton-card"],[1,"skeleton-image"],[1,"skeleton-content"],[1,"skeleton-title"],[1,"skeleton-description"],[1,"skeleton-tags"],["class","skeleton-tag",4,"ngFor","ngForOf"],[1,"skeleton-tag"],[1,"error-container"],[1,"error-content"],[1,"fas","fa-exclamation-triangle"],[1,"btn","btn-primary",3,"click"],[1,"fas","fa-redo"],[1,"projects-content"],["class","featured-showcase",4,"ngIf"],[1,"projects-grid"],["class","project-card",3,"featured","github-project","animation-delay",4,"ngFor","ngForOf"],["class","empty-state",4,"ngIf"],[1,"featured-showcase"],[1,"showcase-title"],[1,"featured-grid"],["class","featured-project-card",3,"animation-delay",4,"ngFor","ngForOf"],[1,"featured-project-card"],[1,"featured-project-image"],["loading","lazy",1,"project-img",3,"src","alt"],[1,"featured-overlay"],[1,"featured-info"],[1,"featured-actions"],["target","_blank","class","featured-link",3,"href",4,"ngIf"],["target","_blank",1,"featured-link",3,"href"],[1,"fab","fa-github"],[1,"fas","fa-external-link-alt"],[1,"project-card"],[1,"project-image"],[1,"project-overlay"],[1,"project-links"],["target","_blank","class","project-link",3,"href",4,"ngIf"],[1,"project-content"],[1,"project-header"],[1,"project-title"],[1,"project-badges"],["class","featured-badge",4,"ngIf"],["class","github-badge",4,"ngIf"],[1,"project-description"],[1,"project-technologies"],["class","tech-tag",4,"ngFor","ngForOf"],["class","tech-tag more",4,"ngIf"],[1,"project-actions"],["target","_blank","class","btn btn-primary",3,"href",4,"ngIf"],["target","_blank","class","btn btn-secondary",3,"href",4,"ngIf"],["target","_blank",1,"project-link",3,"href"],[1,"featured-badge"],[1,"github-badge"],[1,"fas","fa-code"],[1,"fas","fa-clock"],[1,"tech-tag"],[1,"tech-tag","more"],["target","_blank",1,"btn","btn-primary",3,"href"],["target","_blank",1,"btn","btn-secondary",3,"href"],[1,"empty-state"],[1,"fas","fa-search"],[4,"ngIf"],[1,"link-btn",3,"click"]],template:function(n,r){n&1&&(d(0,"div",0)(1,"div",1)(2,"h2",2),m(3,"My Projects"),f(),C(4,"div",3),d(5,"p",4),m(6,"Here are some of my recent projects that showcase my skills and experience"),f(),d(7,"div",5)(8,"div",6)(9,"div",7)(10,"label",8),m(11,"Filter by:"),f(),d(12,"select",9),Is("ngModelChange",function(i){return fu(r.selectedFilter,i)||(r.selectedFilter=i),i}),q("change",function(){return r.onFilterChange(r.selectedFilter)}),N(13,yI,2,3,"option",10),f()(),d(14,"div",7)(15,"label",8),m(16,"Sort by:"),f(),d(17,"select",9),Is("ngModelChange",function(i){return fu(r.selectedSort,i)||(r.selectedSort=i),i}),q("change",function(){return r.onSortChange(r.selectedSort)}),N(18,_I,2,2,"option",10),f()()(),d(19,"div",11)(20,"button",12),q("click",function(){return r.toggleViewMode()}),C(21,"i",13),f(),d(22,"button",14),q("click",function(){return r.toggleFeaturedProjects()}),C(23,"i",15),m(24),f()()(),N(25,MI,8,4,"div",16),f(),N(26,EI,3,2,"div",17)(27,II,10,1,"div",18)(28,YI,5,5,"div",19),f()),n&2&&(g(12),Es("ngModel",r.selectedFilter),b("disabled",r.isLoading),g(),b("ngForOf",r.availableFilters),g(4),Es("ngModel",r.selectedSort),b("disabled",r.isLoading),g(),b("ngForOf",r.availableSorts),g(2),b("disabled",r.isLoading)("title",r.viewMode==="grid"?"Switch to Masonry View":"Switch to Grid View"),g(),fe("fa-th",r.viewMode==="masonry")("fa-th-large",r.viewMode==="grid"),g(),fe("active",r.showFeaturedOnly),b("disabled",r.isLoading),g(2),Pt(" ",r.showFeaturedOnly?"Show All":"Featured Only"," "),g(),b("ngIf",!r.isLoading&&!r.hasError),g(),b("ngIf",r.isLoading),g(),b("ngIf",r.hasError&&!r.isLoading),g(),b("ngIf",!r.isLoading&&!r.hasError))},dependencies:[ge,Ne,Mn,ja,jv,Uv,La,Na,Id],styles:['.projects-container[_ngcontent-%COMP%]{padding:100px 20px;background-color:#f8f9fa;min-height:100vh}.section-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:60px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:3rem;font-weight:700;color:#2c3e50;margin-bottom:20px;position:relative}.section-header[_ngcontent-%COMP%]   .section-divider[_ngcontent-%COMP%]{width:80px;height:4px;background:linear-gradient(90deg,#4a6cf7,#764ba2);margin:0 auto 20px;border-radius:2px}.section-header[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%]{max-width:700px;margin:0 auto 30px;color:#666;font-size:1.1rem;line-height:1.6}.projects-controls[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:30px;padding:20px;background:#fffc;border-radius:15px;box-shadow:0 5px 15px #0000001a;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.projects-controls[_ngcontent-%COMP%]   .filter-controls[_ngcontent-%COMP%]{display:flex;gap:20px;align-items:center}.projects-controls[_ngcontent-%COMP%]   .filter-controls[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:5px}.projects-controls[_ngcontent-%COMP%]   .filter-controls[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;color:#2c3e50}.projects-controls[_ngcontent-%COMP%]   .filter-controls[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]{padding:8px 12px;border:2px solid rgba(74,108,247,.2);border-radius:8px;background:#fff;color:#2c3e50;font-size:.9rem;cursor:pointer;transition:all .3s ease;min-width:150px}.projects-controls[_ngcontent-%COMP%]   .filter-controls[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus{outline:none;border-color:#4a6cf7;box-shadow:0 0 0 3px #4a6cf71a}.projects-controls[_ngcontent-%COMP%]   .filter-controls[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]{display:flex;gap:10px;align-items:center}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]   .view-toggle-btn[_ngcontent-%COMP%]{background:#4a6cf71a;color:#4a6cf7;border:2px solid rgba(74,108,247,.2);padding:10px;border-radius:8px;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;width:40px;height:40px}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]   .view-toggle-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#4a6cf7;color:#fff;transform:scale(1.05)}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]   .view-toggle-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]   .view-toggle-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]   .featured-toggle-btn[_ngcontent-%COMP%]{background:#4a6cf71a;color:#4a6cf7;border:2px solid rgba(74,108,247,.2);padding:8px 16px;border-radius:20px;font-size:.9rem;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:6px}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]   .featured-toggle-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#4a6cf7;color:#fff;transform:translateY(-2px);box-shadow:0 5px 15px #4a6cf74d}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]   .featured-toggle-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]   .featured-toggle-btn.active[_ngcontent-%COMP%]{background:linear-gradient(90deg,#4a6cf7,#764ba2);color:#fff;border-color:transparent}.projects-controls[_ngcontent-%COMP%]   .view-controls[_ngcontent-%COMP%]   .featured-toggle-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}.project-stats[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:30px;margin-bottom:40px;padding:20px;background:linear-gradient(135deg,#4a6cf71a,#764ba20d);border-radius:15px}.project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{text-align:center}.project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{display:block;font-size:2rem;font-weight:700;color:#4a6cf7;line-height:1}.project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.9rem;color:#666;font-weight:500;text-transform:uppercase;letter-spacing:.5px}.loading-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.loading-skeleton[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(350px,1fr));gap:30px}.skeleton-card[_ngcontent-%COMP%]{background:#fff;border-radius:15px;overflow:hidden;box-shadow:0 5px 15px #0000001a;animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite alternate}.skeleton-card[_ngcontent-%COMP%]   .skeleton-image[_ngcontent-%COMP%]{height:200px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 2s infinite}.skeleton-card[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]{padding:25px}.skeleton-card[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-title[_ngcontent-%COMP%]{height:24px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 2s infinite;border-radius:4px;margin-bottom:15px}.skeleton-card[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-description[_ngcontent-%COMP%]{height:60px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 2s infinite;border-radius:4px;margin-bottom:20px}.skeleton-card[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-tags[_ngcontent-%COMP%]{display:flex;gap:10px}.skeleton-card[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-tags[_ngcontent-%COMP%]   .skeleton-tag[_ngcontent-%COMP%]{height:24px;width:60px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 2s infinite;border-radius:12px}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}to{opacity:.8}}.error-container[_ngcontent-%COMP%]{max-width:600px;margin:60px auto;text-align:center}.error-content[_ngcontent-%COMP%]{background:#fff;padding:60px 40px;border-radius:15px;box-shadow:0 10px 30px #0000001a}.error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#ff6b6b;margin-bottom:20px}.error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.8rem;color:#2c3e50;margin-bottom:15px;font-weight:600}.error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:1.1rem;margin-bottom:30px;line-height:1.6}.error-content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:12px 24px;font-size:1rem}.featured-showcase[_ngcontent-%COMP%]{margin-bottom:60px}.featured-showcase[_ngcontent-%COMP%]   .showcase-title[_ngcontent-%COMP%]{font-size:1.8rem;color:#2c3e50;margin-bottom:30px;text-align:center;display:flex;align-items:center;justify-content:center;gap:10px}.featured-showcase[_ngcontent-%COMP%]   .showcase-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#4a6cf7;font-size:1.6rem}.featured-showcase[_ngcontent-%COMP%]   .featured-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(500px,1fr));gap:30px}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]{position:relative;border-radius:20px;overflow:hidden;box-shadow:0 10px 30px #00000026;transition:all .3s ease;animation:_ngcontent-%COMP%_fadeInUp .8s ease forwards;opacity:0}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]:hover{transform:translateY(-10px) scale(1.02);box-shadow:0 20px 50px #00000040}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]:hover   .featured-overlay[_ngcontent-%COMP%]{opacity:1}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]:hover   .project-img[_ngcontent-%COMP%]{transform:scale(1.1)}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]   .featured-project-image[_ngcontent-%COMP%]{position:relative;height:300px;overflow:hidden}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]   .featured-project-image[_ngcontent-%COMP%]   .project-img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]   .featured-project-image[_ngcontent-%COMP%]   .featured-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,#4a6cf7e6,#764ba2e6);display:flex;align-items:center;justify-content:center;opacity:0;transition:opacity .3s ease}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]   .featured-project-image[_ngcontent-%COMP%]   .featured-overlay[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]{text-align:center;color:#fff;padding:20px}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]   .featured-project-image[_ngcontent-%COMP%]   .featured-overlay[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:700;margin-bottom:15px}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]   .featured-project-image[_ngcontent-%COMP%]   .featured-overlay[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;line-height:1.6;margin-bottom:25px;opacity:.9}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]   .featured-project-image[_ngcontent-%COMP%]   .featured-overlay[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:15px}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]   .featured-project-image[_ngcontent-%COMP%]   .featured-overlay[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-actions[_ngcontent-%COMP%]   .featured-link[_ngcontent-%COMP%]{width:50px;height:50px;background:#fff3;border:2px solid rgba(255,255,255,.3);border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.3rem;transition:all .3s ease;text-decoration:none}.featured-showcase[_ngcontent-%COMP%]   .featured-project-card[_ngcontent-%COMP%]   .featured-project-image[_ngcontent-%COMP%]   .featured-overlay[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-actions[_ngcontent-%COMP%]   .featured-link[_ngcontent-%COMP%]:hover{background:#fff;color:#4a6cf7;transform:scale(1.1) rotate(5deg)}.projects-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.projects-grid[_ngcontent-%COMP%]{display:grid;gap:30px}.projects-grid.view-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(350px,1fr))}.projects-grid.view-masonry[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(320px,1fr));grid-auto-rows:masonry}@supports not (grid-auto-rows: masonry){.projects-grid.view-masonry[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;align-items:flex-start}.projects-grid.view-masonry[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]{width:calc(33.333% - 20px);margin:10px}@media (max-width: 1024px){.projects-grid.view-masonry[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]{width:calc(50% - 20px)}}@media (max-width: 768px){.projects-grid.view-masonry[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]{width:calc(100% - 20px)}}}.project-card[_ngcontent-%COMP%]{background:#fff;border-radius:20px;overflow:hidden;box-shadow:0 8px 25px #0000001f;transition:all .3s ease;height:100%;display:flex;flex-direction:column;animation:_ngcontent-%COMP%_fadeInUp .6s ease forwards;opacity:0;position:relative;will-change:transform}.project-card[_ngcontent-%COMP%]:hover{transform:translateY(-15px) scale(1.03);box-shadow:0 25px 50px #00000040}.project-card[_ngcontent-%COMP%]:hover   .project-image[_ngcontent-%COMP%]   .project-overlay[_ngcontent-%COMP%]{opacity:1}.project-card[_ngcontent-%COMP%]:hover   .project-img[_ngcontent-%COMP%]{transform:scale(1.15)}.project-card[_ngcontent-%COMP%]:hover   .project-content[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4a6cf705,#764ba203)}.project-card.featured[_ngcontent-%COMP%]{position:relative}.project-card.featured[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:-3px;border-radius:23px;z-index:-1;opacity:.1}.project-card.featured[_ngcontent-%COMP%]:after{content:"";position:absolute;top:0;left:0;right:0;height:6px;border-radius:20px 20px 0 0}.project-card.featured[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]{padding:30px 25px}.project-card.github-project[_ngcontent-%COMP%]   .project-image[_ngcontent-%COMP%]:after{content:"";position:absolute;top:15px;right:15px;width:30px;height:30px;background:#333c;border-radius:50%;display:flex;align-items:center;justify-content:center}.project-card.github-project[_ngcontent-%COMP%]   .project-image[_ngcontent-%COMP%]:before{content:"\\f09b";font-family:"Font Awesome 6 Brands";position:absolute;top:15px;right:15px;width:30px;height:30px;display:flex;align-items:center;justify-content:center;color:#fff;font-size:14px;z-index:1}.project-image[_ngcontent-%COMP%]{height:200px;position:relative;overflow:hidden;background:linear-gradient(135deg,#4a6cf7,#764ba2)}.project-image[_ngcontent-%COMP%]   .project-img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.project-image[_ngcontent-%COMP%]   .project-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:#222c;display:flex;justify-content:center;align-items:center;opacity:0;transition:opacity .3s ease}.project-image[_ngcontent-%COMP%]   .project-links[_ngcontent-%COMP%]{display:flex;gap:20px}.project-image[_ngcontent-%COMP%]   .project-links[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%]{width:50px;height:50px;background:#fff;border-radius:50%;display:flex;justify-content:center;align-items:center;color:#222;font-size:1.5rem;transition:all .3s ease;text-decoration:none}.project-image[_ngcontent-%COMP%]   .project-links[_ngcontent-%COMP%]   .project-link[_ngcontent-%COMP%]:hover{background:#4a6cf7;color:#fff;transform:scale(1.1) rotate(5deg)}.project-content[_ngcontent-%COMP%]{padding:25px;flex-grow:1;display:flex;flex-direction:column}.project-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:15px}.project-header[_ngcontent-%COMP%]   .project-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#2c3e50;flex:1;margin-right:10px}.project-header[_ngcontent-%COMP%]   .project-badges[_ngcontent-%COMP%]{display:flex;gap:8px;flex-shrink:0;flex-wrap:wrap}.project-header[_ngcontent-%COMP%]   .project-badges[_ngcontent-%COMP%]   .featured-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4a6cf7,#764ba2);color:#fff;padding:6px 12px;border-radius:20px;font-size:.75rem;font-weight:700;display:flex;align-items:center;gap:4px;box-shadow:0 2px 8px #4a6cf74d;animation:_ngcontent-%COMP%_pulse-badge 2s infinite}.project-header[_ngcontent-%COMP%]   .project-badges[_ngcontent-%COMP%]   .featured-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.7rem}.project-header[_ngcontent-%COMP%]   .project-badges[_ngcontent-%COMP%]   .github-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#333,#555);color:#fff;padding:6px 10px;border-radius:15px;font-size:.75rem;font-weight:600;display:flex;align-items:center;gap:4px;box-shadow:0 2px 8px #3333}.project-header[_ngcontent-%COMP%]   .project-badges[_ngcontent-%COMP%]   .github-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem}.project-description[_ngcontent-%COMP%]{color:#666;line-height:1.6;margin-bottom:20px;flex-grow:1}.project-stats[_ngcontent-%COMP%]{display:flex;gap:12px;margin-bottom:18px;flex-wrap:wrap}.project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;background:#4a6cf714;padding:6px 10px;border-radius:12px;color:#2c3e50;font-size:.85rem;font-weight:500;transition:all .3s ease}.project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]:hover{background:#4a6cf726;transform:translateY(-1px)}.project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#4a6cf7;font-size:.8rem}.project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:600}.project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.75rem;opacity:.8;margin-left:2px}.project-technologies[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:6px;margin-bottom:20px}.project-technologies[_ngcontent-%COMP%]   .tech-tag[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4a6cf71a,#764ba20d);color:#4a6cf7;padding:6px 12px;border-radius:18px;font-size:.75rem;font-weight:600;border:1px solid rgba(74,108,247,.2);transition:all .3s ease;position:relative;overflow:hidden}.project-technologies[_ngcontent-%COMP%]   .tech-tag[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(74,108,247,.2),transparent);transition:left .5s ease}.project-technologies[_ngcontent-%COMP%]   .tech-tag[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#4a6cf7,#764ba2);color:#fff;transform:translateY(-2px) scale(1.05);box-shadow:0 5px 15px #4a6cf74d}.project-technologies[_ngcontent-%COMP%]   .tech-tag[_ngcontent-%COMP%]:hover:before{left:100%}.project-technologies[_ngcontent-%COMP%]   .tech-tag.more[_ngcontent-%COMP%]{background:#6666661a;color:#666;border-color:#6663;font-weight:500}.project-technologies[_ngcontent-%COMP%]   .tech-tag.more[_ngcontent-%COMP%]:hover{background:#666;color:#fff}.project-actions[_ngcontent-%COMP%]{display:flex;gap:15px}.project-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:8px 15px;font-size:.9rem;display:flex;align-items:center;gap:8px}.project-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:100px 20px;color:#666;background:linear-gradient(135deg,#4a6cf705,#764ba203);border-radius:20px;margin:40px 0}.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:5rem;color:#4a6cf7;margin-bottom:30px;opacity:.6;animation:_ngcontent-%COMP%_gentle-bounce 2s ease-in-out infinite}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:2rem;color:#2c3e50;margin-bottom:20px;font-weight:700}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;line-height:1.7;max-width:500px;margin:0 auto 20px}.empty-state[_ngcontent-%COMP%]   .link-btn[_ngcontent-%COMP%]{background:none;border:none;color:#4a6cf7;font-weight:600;cursor:pointer;text-decoration:underline;transition:all .3s ease}.empty-state[_ngcontent-%COMP%]   .link-btn[_ngcontent-%COMP%]:hover{color:#764ba2;transform:translateY(-1px)}@keyframes _ngcontent-%COMP%_pulse-badge{0%,to{box-shadow:0 2px 8px #4a6cf74d}50%{box-shadow:0 4px 16px #4a6cf780}}@keyframes _ngcontent-%COMP%_gentle-bounce{0%,to{transform:translateY(0)}50%{transform:translateY(-10px)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}@media (prefers-reduced-motion: reduce){.project-card[_ngcontent-%COMP%]{animation:none}.project-card[_ngcontent-%COMP%]:hover{transform:none}.skeleton-card[_ngcontent-%COMP%], .skeleton-image[_ngcontent-%COMP%], .skeleton-title[_ngcontent-%COMP%], .skeleton-description[_ngcontent-%COMP%], .skeleton-tag[_ngcontent-%COMP%]{animation:none}}@media (max-width: 768px){.projects-container[_ngcontent-%COMP%]{padding:80px 15px}.section-header[_ngcontent-%COMP%]{margin-bottom:50px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2.5rem;line-height:1.2}.section-header[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%]{font-size:1rem;line-height:1.6;margin-bottom:25px}.projects-controls[_ngcontent-%COMP%]{margin-bottom:30px}.projects-controls[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{padding:10px 20px;font-size:.95rem;gap:6px}.loading-skeleton[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:20px}.skeleton-card[_ngcontent-%COMP%]   .skeleton-image[_ngcontent-%COMP%]{height:180px}.skeleton-card[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]{padding:20px}.error-container[_ngcontent-%COMP%]{margin:40px auto}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]{padding:40px 25px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.6rem}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem}.projects-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:25px}.projects-grid[_ngcontent-%COMP%]   .project-card.featured[_ngcontent-%COMP%]{grid-column:span 1;border-width:3px}.project-card[_ngcontent-%COMP%]   .project-image[_ngcontent-%COMP%]{height:180px}.project-card[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]{padding:20px}.project-card[_ngcontent-%COMP%]   .project-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:10px}.project-card[_ngcontent-%COMP%]   .project-header[_ngcontent-%COMP%]   .project-title[_ngcontent-%COMP%]{font-size:1.3rem;margin-right:0}.project-card[_ngcontent-%COMP%]   .project-header[_ngcontent-%COMP%]   .project-badges[_ngcontent-%COMP%]{align-self:flex-end}.project-card[_ngcontent-%COMP%]   .project-description[_ngcontent-%COMP%]{font-size:.95rem;line-height:1.6;margin-bottom:15px}.project-card[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]{flex-wrap:wrap;gap:12px;margin-bottom:12px}.project-card[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{font-size:.85rem}.project-card[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.project-card[_ngcontent-%COMP%]   .project-technologies[_ngcontent-%COMP%]{margin-bottom:15px}.project-card[_ngcontent-%COMP%]   .project-technologies[_ngcontent-%COMP%]   .tech-tag[_ngcontent-%COMP%]{font-size:.8rem;padding:4px 8px;margin:2px 4px 2px 0}.project-card[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]{flex-direction:column;gap:10px}.project-card[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%;justify-content:center;padding:10px 15px;font-size:.9rem}.project-card.featured[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]{padding:25px 20px}.empty-state[_ngcontent-%COMP%]{padding:60px 20px}.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.6rem}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 480px){.projects-container[_ngcontent-%COMP%]{padding:60px 10px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2rem}.section-header[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%]{font-size:.95rem}.projects-controls[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{padding:9px 18px;font-size:.9rem}.projects-grid[_ngcontent-%COMP%]{gap:20px}.project-card[_ngcontent-%COMP%]   .project-image[_ngcontent-%COMP%]{height:160px}.project-card[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]{padding:18px 15px}.project-card[_ngcontent-%COMP%]   .project-header[_ngcontent-%COMP%]   .project-title[_ngcontent-%COMP%]{font-size:1.2rem}.project-card[_ngcontent-%COMP%]   .project-header[_ngcontent-%COMP%]   .project-badges[_ngcontent-%COMP%]   .featured-badge[_ngcontent-%COMP%], .project-card[_ngcontent-%COMP%]   .project-header[_ngcontent-%COMP%]   .project-badges[_ngcontent-%COMP%]   .github-badge[_ngcontent-%COMP%]{font-size:.75rem;padding:4px 8px}.project-card[_ngcontent-%COMP%]   .project-description[_ngcontent-%COMP%]{font-size:.9rem}.project-card[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{font-size:.8rem}.project-card[_ngcontent-%COMP%]   .project-technologies[_ngcontent-%COMP%]   .tech-tag[_ngcontent-%COMP%]{font-size:.75rem;padding:3px 6px}.project-card[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:9px 12px;font-size:.85rem}.project-card.featured[_ngcontent-%COMP%]   .project-content[_ngcontent-%COMP%]{padding:20px 15px}}']})};function ZI(e,t){if(e&1&&(d(0,"a",34),C(1,"i"),f()),e&2){let n=t.$implicit;Ae("--social-color",n.color),b("href",n.url,he),g(),Ge(n.icon)}}function QI(e,t){e&1&&(d(0,"div",35),C(1,"i",36),d(2,"span"),m(3,"Thank you! Your message has been sent successfully."),f()())}function JI(e,t){e&1&&(d(0,"div",37),m(1," Name is required (minimum 2 characters) "),f())}function KI(e,t){e&1&&(d(0,"div",37),m(1," Please enter a valid email address "),f())}function XI(e,t){e&1&&(d(0,"div",37),m(1," Subject is required "),f())}function ex(e,t){e&1&&(d(0,"div",37),m(1," Message is required (minimum 10 characters) "),f())}var Ua=class e{constructor(t,n){this.portfolioService=t;this.fb=n;this.personalInfo=this.portfolioService.getPersonalInfo(),this.socialLinks=this.portfolioService.getSocialLinks(),this.contactForm=this.fb.group({name:["",[yt.required,yt.minLength(2)]],email:["",[yt.required,yt.email]],subject:["",yt.required],message:["",[yt.required,yt.minLength(10)]]})}personalInfo;socialLinks;contactForm;isSubmitted=!1;isSuccess=!1;errorMessage="";onSubmit(){this.isSubmitted=!0,this.contactForm.valid?(console.log("Form submitted:",this.contactForm.value),this.isSuccess=!0,this.contactForm.reset(),this.isSubmitted=!1,setTimeout(()=>{this.isSuccess=!1},5e3)):this.errorMessage="Please fill all required fields correctly."}static \u0275fac=function(n){return new(n||e)(P(_e),P(Hv))};static \u0275cmp=ne({type:e,selectors:[["app-contact"]],decls:69,vars:22,consts:[[1,"contact-container"],[1,"section-header"],[1,"section-title"],[1,"section-divider"],[1,"section-description"],[1,"contact-content"],[1,"contact-info"],[1,"contact-item"],[1,"fas","fa-envelope"],[1,"contact-details"],[1,"contact-label"],[1,"contact-value",3,"href"],[1,"fas","fa-phone"],[1,"fas","fa-map-marker-alt"],[1,"contact-value"],[1,"fab","fa-whatsapp"],["target","_blank",1,"contact-value",3,"href"],[1,"social-links"],["target","_blank","class","social-link",3,"href","--social-color",4,"ngFor","ngForOf"],[1,"contact-form-container"],["class","success-message",4,"ngIf"],[1,"contact-form",3,"ngSubmit","formGroup"],[1,"form-group"],["for","name"],["type","text","id","name","formControlName","name",1,"form-control"],["class","error-message",4,"ngIf"],["for","email"],["type","email","id","email","formControlName","email",1,"form-control"],["for","subject"],["type","text","id","subject","formControlName","subject",1,"form-control"],["for","message"],["id","message","formControlName","message","rows","5",1,"form-control"],["type","submit",1,"btn","btn-primary"],[1,"fas","fa-paper-plane"],["target","_blank",1,"social-link",3,"href"],[1,"success-message"],[1,"fas","fa-check-circle"],[1,"error-message"]],template:function(n,r){if(n&1&&(d(0,"div",0)(1,"div",1)(2,"h2",2),m(3,"Get In Touch"),f(),C(4,"div",3),d(5,"p",4),m(6,"Let's discuss your next project or opportunity"),f()(),d(7,"div",5)(8,"div",6)(9,"h3"),m(10,"Contact Information"),f(),d(11,"div",7),C(12,"i",8),d(13,"div",9)(14,"span",10),m(15,"Email"),f(),d(16,"a",11),m(17),f()()(),d(18,"div",7),C(19,"i",12),d(20,"div",9)(21,"span",10),m(22,"Phone"),f(),d(23,"a",11),m(24),f()()(),d(25,"div",7),C(26,"i",13),d(27,"div",9)(28,"span",10),m(29,"Location"),f(),d(30,"span",14),m(31),f()()(),d(32,"div",7),C(33,"i",15),d(34,"div",9)(35,"span",10),m(36,"WhatsApp"),f(),d(37,"a",16),m(38),f()()(),d(39,"div",17),N(40,ZI,2,5,"a",18),f()(),d(41,"div",19)(42,"h3"),m(43,"Send Message"),f(),N(44,QI,4,0,"div",20),d(45,"form",21),q("ngSubmit",function(){return r.onSubmit()}),d(46,"div",22)(47,"label",23),m(48,"Name *"),f(),C(49,"input",24),N(50,JI,2,0,"div",25),f(),d(51,"div",22)(52,"label",26),m(53,"Email *"),f(),C(54,"input",27),N(55,KI,2,0,"div",25),f(),d(56,"div",22)(57,"label",28),m(58,"Subject *"),f(),C(59,"input",29),N(60,XI,2,0,"div",25),f(),d(61,"div",22)(62,"label",30),m(63,"Message *"),f(),C(64,"textarea",31),N(65,ex,2,0,"div",25),f(),d(66,"button",32),C(67,"i",33),m(68," Send Message "),f()()()()()),n&2){let o,i,s,a,c,l,u,h;g(16),b("href","mailto:"+r.personalInfo.email,he),g(),S(r.personalInfo.email),g(6),b("href","tel:"+r.personalInfo.phone,he),g(),S(r.personalInfo.phone),g(7),S(r.personalInfo.location),g(6),b("href","https://wa.me/94788179855",he),g(),S(r.personalInfo.whatsapp),g(2),b("ngForOf",r.socialLinks),g(4),b("ngIf",r.isSuccess),g(),b("formGroup",r.contactForm),g(4),fe("error",r.isSubmitted&&((o=r.contactForm.get("name"))==null?null:o.invalid)),g(),b("ngIf",r.isSubmitted&&((i=r.contactForm.get("name"))==null?null:i.invalid)),g(4),fe("error",r.isSubmitted&&((s=r.contactForm.get("email"))==null?null:s.invalid)),g(),b("ngIf",r.isSubmitted&&((a=r.contactForm.get("email"))==null?null:a.invalid)),g(4),fe("error",r.isSubmitted&&((c=r.contactForm.get("subject"))==null?null:c.invalid)),g(),b("ngIf",r.isSubmitted&&((l=r.contactForm.get("subject"))==null?null:l.invalid)),g(4),fe("error",r.isSubmitted&&((u=r.contactForm.get("message"))==null?null:u.invalid)),g(),b("ngIf",r.isSubmitted&&((h=r.contactForm.get("message"))==null?null:h.invalid))}},dependencies:[ge,Ne,Mn,ja,kv,Aa,Na,Sv,$v,xd,Sd],styles:['.contact-container[_ngcontent-%COMP%]{padding:100px 20px;background-color:#fff;min-height:100vh}.section-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:60px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:3rem;font-weight:700;color:#2c3e50;margin-bottom:20px;position:relative}.section-header[_ngcontent-%COMP%]   .section-divider[_ngcontent-%COMP%]{width:80px;height:4px;background:linear-gradient(90deg,#4a6cf7,#764ba2);margin:0 auto 20px;border-radius:2px}.section-header[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%]{max-width:700px;margin:0 auto;color:#666;font-size:1.1rem;line-height:1.6}.contact-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;display:grid;grid-template-columns:1fr 2fr;gap:50px}.contact-info[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4a6cf7,#764ba2);padding:40px;border-radius:15px;color:#fff;height:fit-content}.contact-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:600;margin-bottom:30px;position:relative;padding-bottom:15px}.contact-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:0;width:50px;height:3px;background:#fff;border-radius:2px}.contact-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;margin-bottom:25px}.contact-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;margin-right:15px;margin-top:5px}.contact-item[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.contact-item[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%]{font-size:.9rem;opacity:.8;margin-bottom:5px}.contact-item[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-value[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:500;color:#fff;text-decoration:none;transition:opacity .3s ease}.contact-item[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-value[_ngcontent-%COMP%]:hover{opacity:.8}.social-links[_ngcontent-%COMP%]{display:flex;gap:15px;margin-top:40px}.social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:45px;height:45px;background:#fff3;border-radius:50%;color:#fff;font-size:1.3rem;transition:all .3s ease;text-decoration:none}.social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]:hover{background:#fff;color:#4a6cf7;transform:translateY(-3px)}.contact-form-container[_ngcontent-%COMP%]{background:#fff;padding:40px;border-radius:15px;box-shadow:0 5px 20px #0000001a}.contact-form-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:600;color:#2c3e50;margin-bottom:30px;position:relative;padding-bottom:15px}.contact-form-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:0;width:50px;height:3px;background:#4a6cf7;border-radius:2px}.success-message[_ngcontent-%COMP%]{display:flex;align-items:center;background:#2ecc711a;color:#2ecc71;padding:15px;border-radius:10px;margin-bottom:20px}.success-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;margin-right:10px}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{margin-bottom:20px}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;font-weight:500;margin-bottom:8px;color:#2c3e50}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{width:100%;padding:12px 15px;border:1px solid #ddd;border-radius:8px;font-size:1rem;transition:all .3s ease}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{outline:none;border-color:#4a6cf7;box-shadow:0 0 0 3px #4a6cf733}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control.error[_ngcontent-%COMP%]{border-color:#e74c3c}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea.form-control[_ngcontent-%COMP%]{resize:vertical}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#e74c3c;font-size:.9rem;margin-top:5px}.contact-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;padding:12px 25px;font-size:1rem}.contact-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}@media (max-width: 768px){.contact-container[_ngcontent-%COMP%]{padding:80px 15px}.section-header[_ngcontent-%COMP%]{margin-bottom:40px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2.5rem;line-height:1.2}.section-header[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%]{font-size:1rem;line-height:1.6}.contact-content[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:30px}.contact-info[_ngcontent-%COMP%]{padding:25px 20px}.contact-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.6rem;margin-bottom:25px}.contact-info[_ngcontent-%COMP%]   .contact-item[_ngcontent-%COMP%]{margin-bottom:20px}.contact-info[_ngcontent-%COMP%]   .contact-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.3rem}.contact-info[_ngcontent-%COMP%]   .contact-item[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%]{font-size:.85rem}.contact-info[_ngcontent-%COMP%]   .contact-item[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-value[_ngcontent-%COMP%]{font-size:1rem;word-break:break-word}.contact-info[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%]{margin-top:30px;justify-content:center;flex-wrap:wrap}.contact-info[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]{width:40px;height:40px;font-size:1.2rem}.contact-form-container[_ngcontent-%COMP%]{padding:25px 20px}.contact-form-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.6rem;margin-bottom:25px}.contact-form-container[_ngcontent-%COMP%]   .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{margin-bottom:18px}.contact-form-container[_ngcontent-%COMP%]   .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:.95rem;margin-bottom:6px}.contact-form-container[_ngcontent-%COMP%]   .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{padding:10px 12px;font-size:.95rem}.contact-form-container[_ngcontent-%COMP%]   .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-size:.85rem}.contact-form-container[_ngcontent-%COMP%]   .contact-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:10px 20px;font-size:.95rem;width:100%;justify-content:center}.success-message[_ngcontent-%COMP%]{padding:12px;margin-bottom:15px}.success-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.3rem}.success-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.95rem}}@media (max-width: 480px){.contact-container[_ngcontent-%COMP%]{padding:60px 10px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2rem}.section-header[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%]{font-size:.95rem}.contact-info[_ngcontent-%COMP%]{padding:20px 15px}.contact-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem}.contact-info[_ngcontent-%COMP%]   .contact-item[_ngcontent-%COMP%]{margin-bottom:18px}.contact-info[_ngcontent-%COMP%]   .contact-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem;margin-right:12px}.contact-info[_ngcontent-%COMP%]   .contact-item[_ngcontent-%COMP%]   .contact-details[_ngcontent-%COMP%]   .contact-value[_ngcontent-%COMP%]{font-size:.95rem}.contact-info[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%]{gap:12px}.contact-info[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]{width:38px;height:38px;font-size:1.1rem}.contact-form-container[_ngcontent-%COMP%]{padding:20px 15px}.contact-form-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem}.contact-form-container[_ngcontent-%COMP%]   .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{padding:9px 10px;font-size:.9rem}.contact-form-container[_ngcontent-%COMP%]   .contact-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:9px 18px;font-size:.9rem}}']})};function tx(e,t){if(e&1&&(d(0,"a",15),C(1,"i"),f()),e&2){let n=t.$implicit;Ae("--social-color",n.color),b("href",n.url,he),yn("aria-label",n.name),g(),Ge(n.icon)}}var Ba=class e{constructor(t){this.portfolioService=t;this.personalInfo=this.portfolioService.getPersonalInfo(),this.socialLinks=this.portfolioService.getSocialLinks()}personalInfo;socialLinks;currentYear=new Date().getFullYear();scrollToTop(){window.scrollTo({top:0,behavior:"smooth"})}static \u0275fac=function(n){return new(n||e)(P(_e))};static \u0275cmp=ne({type:e,selectors:[["app-footer"]],decls:36,vars:5,consts:[[1,"footer"],[1,"footer-content"],[1,"footer-logo"],[1,"footer-links"],["href","#",3,"click"],["href","#about"],["href","#skills"],["href","#projects"],["href","#contact"],[1,"footer-social"],[1,"social-icons"],["target","_blank",3,"href","--social-color",4,"ngFor","ngForOf"],[1,"footer-bottom"],[1,"scroll-top",3,"click"],[1,"fas","fa-arrow-up"],["target","_blank",3,"href"]],template:function(n,r){n&1&&(d(0,"footer",0)(1,"div",1)(2,"div",2)(3,"h3"),m(4),f(),d(5,"p"),m(6),f()(),d(7,"div",3)(8,"h4"),m(9,"Quick Links"),f(),d(10,"ul")(11,"li")(12,"a",4),q("click",function(){return r.scrollToTop()}),m(13,"Home"),f()(),d(14,"li")(15,"a",5),m(16,"About"),f()(),d(17,"li")(18,"a",6),m(19,"Skills"),f()(),d(20,"li")(21,"a",7),m(22,"Projects"),f()(),d(23,"li")(24,"a",8),m(25,"Contact"),f()()()(),d(26,"div",9)(27,"h4"),m(28,"Connect"),f(),d(29,"div",10),N(30,tx,2,6,"a",11),f()()(),d(31,"div",12)(32,"p"),m(33),f(),d(34,"button",13),q("click",function(){return r.scrollToTop()}),C(35,"i",14),f()()()),n&2&&(g(4),S(r.personalInfo.name.split(" ")[0]),g(2),S(r.personalInfo.title),g(24),b("ngForOf",r.socialLinks),g(3),_n("\xA9 ",r.currentYear," ",r.personalInfo.name,". All Rights Reserved."))},dependencies:[ge,Ne],styles:[".footer[_ngcontent-%COMP%]{background:linear-gradient(135deg,#2c3e50,#222);color:#fff;padding:60px 20px 20px}.footer-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto 40px;display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:40px}.footer-logo[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#4a6cf7;margin-bottom:10px}.footer-logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#fffc;font-size:1.1rem}.footer-links[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;margin-bottom:20px;color:#fff}.footer-links[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0}.footer-links[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:10px}.footer-links[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fffc;text-decoration:none;transition:color .3s ease}.footer-links[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#4a6cf7}.footer-social[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:600;margin-bottom:20px;color:#fff}.footer-social[_ngcontent-%COMP%]   .social-icons[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:15px}.footer-social[_ngcontent-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:45px;height:45px;background:#ffffff1a;border-radius:50%;color:#fff;font-size:1.3rem;transition:all .3s ease;text-decoration:none}.footer-social[_ngcontent-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{background:var(--social-color, #4a6cf7);color:#fff;transform:translateY(-3px);box-shadow:0 10px 20px #0000004d}.footer-bottom[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding-top:30px;border-top:1px solid rgba(255,255,255,.2);display:flex;justify-content:space-between;align-items:center}.footer-bottom[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#fffc;margin:0}.footer-bottom[_ngcontent-%COMP%]   .scroll-top[_ngcontent-%COMP%]{background:#4a6cf7;color:#fff;border:none;width:45px;height:45px;border-radius:50%;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center}.footer-bottom[_ngcontent-%COMP%]   .scroll-top[_ngcontent-%COMP%]:hover{background:#3d5af5;transform:translateY(-3px);box-shadow:0 10px 20px #4a6cf74d}.footer-bottom[_ngcontent-%COMP%]   .scroll-top[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}@media (max-width: 768px){.footer[_ngcontent-%COMP%]{padding:40px 15px 15px}.footer-content[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:30px;text-align:center}.footer-bottom[_ngcontent-%COMP%]{flex-direction:column;gap:20px;text-align:center}.footer-social[_ngcontent-%COMP%]   .social-icons[_ngcontent-%COMP%]{justify-content:center}}"]})};var Ha=class e{static \u0275fac=function(n){return new(n||e)};static \u0275cmp=ne({type:e,selectors:[["app-home"]],decls:13,vars:0,consts:[["id","hero"],["id","about"],["id","skills"],["id","projects"],["id","contact"]],template:function(n,r){n&1&&(C(0,"app-header"),d(1,"main")(2,"section",0),C(3,"app-hero"),f(),d(4,"section",1),C(5,"app-about"),f(),d(6,"section",2),C(7,"app-skills"),f(),d(8,"section",3),C(9,"app-projects"),f(),d(10,"section",4),C(11,"app-contact"),f()(),C(12,"app-footer"))},dependencies:[Ca,ba,Ma,Da,Va,Ua,Ba],encapsulation:2})};var Gv=[{path:"",component:Ha},{path:"home",redirectTo:"",pathMatch:"full"},{path:"**",redirectTo:""}];var qv={providers:[Ig({eventCoalescing:!0}),ld(Gv),Ru(ku())]};var $a=class e{title="my-angilar-app";static \u0275fac=function(n){return new(n||e)};static \u0275cmp=ne({type:e,selectors:[["app-root"]],decls:1,vars:0,template:function(n,r){n&1&&C(0,"router-outlet")},dependencies:[Lo],encapsulation:2})};Su($a,qv).catch(e=>console.error(e));
