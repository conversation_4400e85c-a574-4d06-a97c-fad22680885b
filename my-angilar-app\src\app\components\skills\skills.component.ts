import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PortfolioService, Skill } from '../../services/portfolio.service';
import { ScrollAnimationService } from '../../services/scroll-animation.service';
import { BackgroundAnimationComponent } from '../background-animation/background-animation.component';

@Component({
  selector: 'app-skills',
  standalone: true,
  imports: [CommonModule, BackgroundAnimationComponent],
  templateUrl: './skills.component.html',
  styleUrl: './skills.component.scss'
})
export class SkillsComponent {
  skills: Skill[];
  skillCategories: string[];

  constructor(private portfolioService: PortfolioService) {
    this.skills = this.portfolioService.getSkills();
    this.skillCategories = [...new Set(this.skills.map(skill => skill.category))];
  }

  getSkillsByCategory(category: string): Skill[] {
    return this.portfolioService.getSkillsByCategory(category);
  }
}
