import { Injectable } from '@angular/core';

export interface PersonalInfo {
  name: string;
  title: string;
  email: string;
  phone: string;
  whatsapp: string;
  location: string;
  dateOfBirth: string;
  linkedin: string;
  github: string;
  facebook: string;
  instagram: string;
  threads: string;
  telegram: string;
  bio: string;
}

export interface SocialLink {
  name: string;
  url: string;
  icon: string;
  color: string;
}

export interface Language {
  name: string;
  level: string;
}

export interface Hobby {
  name: string;
  icon: string;
}

export interface Leadership {
  title: string;
  description: string;
}

export interface OtherQualification {
  category: string;
  items: string[];
}

export interface Skill {
  name: string;
  level: number;
  category: string;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  githubUrl?: string;
  liveUrl?: string;
  imageUrl?: string;
  featured: boolean;
}

export interface Education {
  institution: string;
  degree: string;
  period: string;
  location: string;
}

export interface WorkExperience {
  company: string;
  position: string;
  period: string;
  responsibilities: string[];
}

@Injectable({
  providedIn: 'root'
})
export class PortfolioService {

  private personalInfo: PersonalInfo = {
    name: 'Sachin Prabashwara Samarawickrama',
    title: 'Trainee Full Stack Developer',
    email: '<EMAIL>',
    phone: '+94788179855',
    whatsapp: '+94788179855',
    location: 'Panadura, Sri Lanka',
    dateOfBirth: 'February 8, 2004',
    linkedin: 'https://www.linkedin.com/in/sachin-samarawickrama-*********/',
    github: 'https://github.com/macamisp',
    facebook: 'https://www.facebook.com/profile.php?id=100069446774540',
    instagram: 'https://www.instagram.com/macami_impres/',
    threads: 'https://www.threads.com/@macami_impres?xmt=AQGzSQbxGWB_x5fu5c2M7HjJLu0rVs6W7XoMbkST3RdUPDs',
    telegram: 'https://t.me/MACAMI_1X1080SP991',
    bio: 'Software Engineer Trainee with expertise in full-stack development and website design. Passionate about emerging technologies, specializing in object-oriented programming and modern development frameworks. Combines technical skills with business acumen through successful projects. Strong leadership qualities with experience in organizing events and coordinating teams.'
  };

  private skills: Skill[] = [
    // Technical Skills
    { name: 'Java', level: 85, category: 'Backend' },
    { name: 'JavaScript', level: 80, category: 'Frontend' },
    { name: 'TypeScript', level: 75, category: 'Frontend' },
    { name: 'HTML5', level: 90, category: 'Frontend' },
    { name: 'CSS3', level: 85, category: 'Frontend' },
    { name: 'SCSS', level: 80, category: 'Frontend' },
    { name: 'Angular', level: 80, category: 'Frontend' },
    { name: 'Spring Boot', level: 75, category: 'Backend' },
    { name: 'Git', level: 85, category: 'Tools' },
    { name: 'GitHub', level: 85, category: 'Tools' },

    // Adobe Creative Suite
    { name: 'Adobe Photoshop', level: 80, category: 'Design' },
    { name: 'Adobe Premiere Pro', level: 75, category: 'Design' },
    { name: 'Adobe After Effects', level: 70, category: 'Design' },

    // Microsoft Office
    { name: 'MS Office', level: 85, category: 'Productivity' },
    { name: 'MS Excel', level: 80, category: 'Productivity' },
    { name: 'MS Word', level: 90, category: 'Productivity' },
    { name: 'MS PowerPoint', level: 85, category: 'Productivity' }
  ];

  private projects: Project[] = [
    {
      id: 'we-care',
      title: 'WE CARE - Hospital Management System',
      description: 'Frontend development for hospital management system ensuring high-quality and user-friendly interfaces. Built with modern web technologies for optimal performance.',
      technologies: ['HTML', 'CSS', 'JavaScript', 'Bootstrap'],
      githubUrl: 'https://github.com/macamisp/ICET-110Hospital-Management-Frontend',
      featured: true
    },
    {
      id: 'nitro-gass',
      title: 'NITRO GASS',
      description: 'Full-stack web application using simple HTML, CSS, and JS to fetch data from a pre-made API and loading data using Spring Boot backend and Angular frontend.',
      technologies: ['HTML', 'CSS', 'JavaScript', 'Spring Boot', 'Angular'],
      githubUrl: 'https://github.com/macamisp/Nitro-gass-FRONTEND',
      featured: true
    },
    {
      id: 'weather-app',
      title: 'Weather App',
      description: 'Simple weather app to fetch and get data from a pre-made API and loading real-time weather data with a simple UI/UX design.',
      technologies: ['HTML', 'CSS', 'JavaScript', 'API Integration'],
      githubUrl: 'https://github.com/macamisp/simple_weather',
      featured: false
    },
    {
      id: 'burger-shop',
      title: 'Burger Shop POS System',
      description: 'Point of sale system using HTML, CSS, and JS. Simple POS system only using JS arrays and storing in local storage customer registration and bill printing system.',
      technologies: ['HTML', 'CSS', 'JavaScript', 'Local Storage'],
      githubUrl: 'https://github.com/macamisp/possystem',
      featured: false
    }
  ];

  private education: Education[] = [
    {
      institution: 'Institute of Computer Engineering Technology (ICET)',
      degree: 'ICET Certified Developer (ICD)',
      period: 'Jan 2010 - Feb 2023',
      location: 'Panadura'
    },
    {
      institution: 'St. John\'s College, Panadura',
      degree: 'G.C.E Advanced Level - A/L MALA - B',
      period: '2021-2023',
      location: 'Panadura'
    },
    {
      institution: 'St. John\'s College, Panadura',
      degree: 'G.C.E Ordinary Level',
      period: '2010-2022',
      location: 'Panadura'
    }
  ];

  private workExperience: WorkExperience[] = [
    {
      company: 'Universal Software Company',
      position: 'Frontend Engineer Intern',
      period: 'January 2025 - Present',
      responsibilities: [
        'Developing user interfaces using Angular for scalable web applications',
        'Collaborating with backend teams to integrate REST APIs',
        'Participating in code reviews and agile development processes',
        'Enhancing UI/UX design to improve user interaction and performance'
      ]
    }
  ];

  private socialLinks: SocialLink[] = [
    { name: 'GitHub', url: 'https://github.com/macamisp', icon: 'fab fa-github', color: '#333' },
    { name: 'LinkedIn', url: 'https://www.linkedin.com/in/sachin-samarawickrama-*********/', icon: 'fab fa-linkedin', color: '#0077b5' },
    { name: 'Facebook', url: 'https://www.facebook.com/profile.php?id=100069446774540', icon: 'fab fa-facebook', color: '#1877f2' },
    { name: 'Instagram', url: 'https://www.instagram.com/macami_impres/', icon: 'fab fa-instagram', color: '#e4405f' },
    { name: 'Threads', url: 'https://www.threads.com/@macami_impres?xmt=AQGzSQbxGWB_x5fu5c2M7HjJLu0rVs6W7XoMbkST3RdUPDs', icon: 'fab fa-threads', color: '#000' },
    { name: 'Telegram', url: 'https://t.me/MACAMI_1X1080SP991', icon: 'fab fa-telegram', color: '#0088cc' },
    { name: 'WhatsApp', url: 'https://wa.me/94788179855', icon: 'fab fa-whatsapp', color: '#25d366' }
  ];

  private languages: Language[] = [
    { name: 'Sinhala', level: 'Native' },
    { name: 'English', level: 'Fluent' }
  ];

  private hobbies: Hobby[] = [
    { name: 'Fitness Activities', icon: 'fas fa-dumbbell' },
    { name: 'Traveling', icon: 'fas fa-plane' },
    { name: 'Photography', icon: 'fas fa-camera' },
    { name: 'Cricket', icon: 'fas fa-baseball-ball' },
    { name: 'Badminton', icon: 'fas fa-shuttlecock' }
  ];

  private leadership: Leadership[] = [
    {
      title: 'Prefect at St. John\'s College Panadura',
      description: 'Being a prefect in st john\'s college panadura'
    },
    {
      title: 'School Scout Society Member',
      description: 'Being a member in school scout society'
    },
    {
      title: 'Class Monitor',
      description: 'Being a class monitor in school'
    }
  ];

  private otherQualifications: OtherQualification[] = [
    {
      category: 'Organizing and Coordinating',
      items: [
        'Being an active member in school media club',
        'Good skilled in organizing work',
        'Participated in organizing committee of st john\'s college'
      ]
    },
    {
      category: 'Sports & Extracurricular Activities',
      items: [
        'Participated in cricket',
        'Participated in cricket',
        'Participated in badminton'
      ]
    }
  ];

  constructor() { }

  getPersonalInfo(): PersonalInfo {
    return this.personalInfo;
  }

  getSkills(): Skill[] {
    return this.skills;
  }

  getSkillsByCategory(category: string): Skill[] {
    return this.skills.filter(skill => skill.category === category);
  }

  getProjects(): Project[] {
    return this.projects;
  }

  getFeaturedProjects(): Project[] {
    return this.projects.filter(project => project.featured);
  }

  getEducation(): Education[] {
    return this.education;
  }

  getWorkExperience(): WorkExperience[] {
    return this.workExperience;
  }

  getSocialLinks(): SocialLink[] {
    return this.socialLinks;
  }

  getLanguages(): Language[] {
    return this.languages;
  }

  getHobbies(): Hobby[] {
    return this.hobbies;
  }

  getLeadership(): Leadership[] {
    return this.leadership;
  }

  getOtherQualifications(): OtherQualification[] {
    return this.otherQualifications;
  }
}
