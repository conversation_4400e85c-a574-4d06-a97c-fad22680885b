import { Injectable } from '@angular/core';
import { Observable, combineLatest, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { GithubService, ProcessedRepository } from './github.service';

export interface PersonalInfo {
  name: string;
  title: string;
  email: string;
  phone: string;
  whatsapp: string;
  location: string;
  dateOfBirth: string;
  linkedin: string;
  github: string;
  facebook: string;
  instagram: string;
  threads: string;
  telegram: string;
  bio: string;
}

export interface SocialLink {
  name: string;
  url: string;
  icon: string;
  color: string;
}

export interface Language {
  name: string;
  level: string;
}

export interface Hobby {
  name: string;
  icon: string;
}

export interface Leadership {
  title: string;
  description: string;
}

export interface OtherQualification {
  category: string;
  items: string[];
}

export interface Skill {
  name: string;
  level: number;
  category: string;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  githubUrl?: string;
  liveUrl?: string;
  imageUrl?: string;
  featured: boolean;
  language?: string;
  stars?: number;
  lastUpdated?: Date;
  isFromGitHub?: boolean;
}

export interface Education {
  institution: string;
  degree: string;
  period: string;
  location: string;
  description?: string;
}

export interface WorkExperience {
  company: string;
  position: string;
  period: string;
  responsibilities: string[];
}

@Injectable({
  providedIn: 'root'
})
export class PortfolioService {

  private personalInfo: PersonalInfo = {
    name: 'Sachin Prabashwara Samarawickrama',
    title: 'Trainee Full Stack Developer',
    email: '<EMAIL>',
    phone: '+94788179855',
    whatsapp: '+94788179855',
    location: 'Panadura, Sri Lanka',
    dateOfBirth: 'February 8, 2004',
    linkedin: 'https://www.linkedin.com/in/sachin-samarawickrama-*********/',
    github: 'https://github.com/macamisp',
    facebook: 'https://www.facebook.com/profile.php?id=100069446774540',
    instagram: 'https://www.instagram.com/macami_impres/',
    threads: 'https://www.threads.com/@macami_impres?xmt=AQGzSQbxGWB_x5fu5c2M7HjJLu0rVs6W7XoMbkST3RdUPDs',
    telegram: 'https://t.me/MACAMI_1X1080SP991',
    bio: 'Software Engineer Trainee with expertise in full-stack development and website design. Passionate about emerging technologies, specializing in object-oriented programming and modern development frameworks. Combines technical skills with business acumen through successful projects. Strong leadership qualities with experience in organizing events and coordinating teams.'
  };

  private skills: Skill[] = [
    // Technical Skills
    { name: 'Java', level: 85, category: 'Backend' },
    { name: 'JavaScript', level: 80, category: 'Frontend' },
    { name: 'TypeScript', level: 75, category: 'Frontend' },
    { name: 'HTML5', level: 90, category: 'Frontend' },
    { name: 'CSS3', level: 85, category: 'Frontend' },
    { name: 'SCSS', level: 80, category: 'Frontend' },
    { name: 'Angular', level: 80, category: 'Frontend' },
    { name: 'Spring Boot', level: 75, category: 'Backend' },
    { name: 'Git', level: 85, category: 'Tools' },
    { name: 'GitHub', level: 85, category: 'Tools' },

    // Adobe Creative Suite
    { name: 'Adobe Photoshop', level: 80, category: 'Design' },
    { name: 'Adobe Premiere Pro', level: 75, category: 'Design' },
    { name: 'Adobe After Effects', level: 70, category: 'Design' },

    // Microsoft Office
  
// set ther my outher projects 
      { name: 'MS Office', level: 85, category: 'Productivity' },
    { name: 'MS Excel', level: 80, category: 'Productivity' },
    { name: 'MS Word', level: 90, category: 'Productivity' },
    { name: 'MS PowerPoint', level: 85, category: 'Productivity' }
  ];

  private projects: Project[] = [
    {
      id: 'we-care',
      title: 'WE CARE - Hospital Management System',
      description: 'Frontend development for hospital management system ensuring high-quality and user-friendly interfaces. Built with modern web technologies for optimal performance.',
      technologies: ['HTML', 'CSS', 'TypeScript', 'Bootstrap','Angular'],
      githubUrl: 'https://github.com/macamisp/ICET-110Hospital-Management-Frontend',
      imageUrl: '/Image_fx.jpg',
      featured: true
    },
    {
      id: 'nitro-gass',
      title: 'NITRO GASS',
      description: 'Full-stack web application using simple HTML, CSS, and JS to fetch data from a pre-made API and loading data using Spring Boot backend and Angular frontend.',
      technologies: ['HTML', 'CSS', 'TypeScript', 'Boostrap', 'Spring Boot', 'Angular'],
      githubUrl: 'https://github.com/macamisp/Nitro-gas-FRONTEND',
      imageUrl: '/Image_fx (1).jpg',
      featured: true
    },
   
    {
      id: 'burger-shop',
      title: 'Burger Shop POS System',
      description: 'Point of sale system using HTML, CSS, and JS. Simple POS system only using JS arrays and storing in local storage customer registration and bill printing system.',
      technologies: ['HTML', 'CSS', 'JavaScript', 'Local Storage'],
      githubUrl: 'https://macamisp.github.io/posesystem/',
      imageUrl: '/Image_fx (12).jpg',
      featured: false
    },
    {
      id: 'portfolio-website',
      title: 'Personal Portfolio Website',
      description: 'Modern Angular portfolio website with professional animations, GitHub integration, and responsive design. Features background animations, project filtering, and mobile-optimized layout',
      technologies: ['Angular', 'TypeScript', 'SCSS', 'GitHub API', 'Responsive Design'],
      githubUrl: 'https://github.com/macamisp/my-portfolio',
      imageUrl: '/image.png',
      featured: false
    },
    
     {
      id: 'weather-app',
      title: 'Weather App',
      description: 'Simple weather app to fetch and get data from a pre-made API and loading real-time weather data with a simple UI/UX design.',
      technologies: ['HTML', 'CSS', 'JavaScript', 'API Integration'],
      githubUrl: 'https://github.com/macamisp/simple_weather',
      imageUrl: '/Image_fx (11).jpg',
      featured: false
    },
    {
      id: 'student-management',
      title: 'Student Management System',
      description: 'Comprehensive student management system for educational institutions with student registration, course management, and academic tracking features.',
      technologies: ['Java', 'Spring Boot', 'MySQL', 'HTML', 'CSS'],
      githubUrl: 'https://github.com/macamisp/student-management-system',
      imageUrl: '/Image_fx (5).jpg',
      featured: false
    },
    {
      id: 'e-commerce-platform',
      title: 'E-Commerce Platform',
      description: 'Full-stack e-commerce solution with product catalog, shopping cart, user authentication, and payment integration for online retail businesses.',
      technologies: ['React', 'Node.js', 'Express', 'MongoDB', 'Stripe API'],
      githubUrl: 'https://github.com/macamisp/ecommerce-platform',
      imageUrl: '/Image_fx (13).jpg',
      featured: false
    },
    {
      id: 'countrySearch',
      title: 'Country Search Application',
      description: 'Simple Country search app to fetch and get data from a pre-made API and loading real-time Country data with a simple UI/UX design.',
      technologies: [ 'HTML5', 'CSS', 'JavaScript'],
      githubUrl: 'https://github.com/macamisp/search',
      imageUrl: 'image(14)fx.png',
      featured: false
    },
    {
      id: 'defenceSystem',
      title: 'Defence System',
      description: 'using the java core consepts Like OOP to create  defence system.',
      technologies: ['JAVA','netbeans'],
      githubUrl: 'https://github.com/macamisp/defenceSystem',
      imageUrl: '/Image_fx (15).jpg',
      featured: false
    },
    {
      id: 'walan-kade',
      title: 'Walankade pos system',
      description: 'Walankade pos system using javaFX.',
      technologies: ['JAVA FX','scene builder'],
      githubUrl: 'https://github.com/macamisp/walan_kade',
      imageUrl: '/How-Much-Does-a-Restaurant-POS-System-Cost.png',
      featured: false
    },
    {
      id: 'Wallpaper.com',
      title: 'Wallpaper.com',
      description: 'Created a modern and responsive website using HTML, CSS, and JavaScript that allows users to browse and download high-quality wallpapers. The site features a clean layout, smooth interactions, and an easy-to-use interface for exploring beautiful wallpapers across various categories.',
      technologies: [ 'HTML', 'JavaScript', 'CSS'],
      githubUrl: 'https://github.com/macamisp/wallpaper.com',
      imageUrl: '/123.png',
      featured: false
    },
    {
      id: 'Weather-Website',
      title: 'weather-website',
      description: 'more funtions to be added.we',
      technologies:[ 'HTML', 'JavaScript', 'CSS'],
      githubUrl: 'https://github.com/macamisp/weather_website/actions/runs/10582278736',
      imageUrl: '/1234.png',
      featured: false
    },
    {
      id: 'clothifiy',
      title: 'CLOTHIFIY-POS SYSTEM',
      description: 'using the java core consepts Like OOP to create  clothifiy pos system system.',
      technologies: [ 'SCENE BUILDER', 'JAVA-FX'],
      githubUrl: 'https://github.com/macamisp/ClothiFiy',
      imageUrl: '/12345.jpg',
      featured: false
    },
{
      id: 'Mouse_Point  ',
      title: 'Mouse_Point',
      description: 'using the HTML,CSS and javaScript to create a mouse point hover effect.',
      technologies: [ 'HTML', 'JavaScript', 'CSS'],
      githubUrl: 'https://macamisp.github.io/mouse_point/',
      imageUrl: '/123456.png',
      featured: false
    },


  {
      id: 'Solarsystem  ',
      title: 'Solarsystem',
      description: 'using the HTML,CSS and javaScript to create a solar system.',
      technologies: [ 'HTML', 'JavaScript', 'CSS'],
      githubUrl: 'https://macamisp.github.io/Solar_system/',
      imageUrl: '/1234567.png',
      featured: false
    },
    
    {
      id: 'MILKYWAY  ',
      title: 'MILKYWAY-SYSTEM',
      description: 'using the HTML,CSS and javaScript to create a milkyway system.',
      technologies: [ 'HTML', 'JavaScript', 'CSS'],
      githubUrl: 'https://macamisp.github.io/milkway/',
      imageUrl: '/12345678.png',
      featured: false
    },

    {
      id: 'x-animation-tree  ',
      title: 'X-animation-tree',
      description: 'using the HTML,CSS and javaScript to create a chiristmas tree.',
      technologies: [ 'HTML', 'JavaScript', 'CSS'],
      githubUrl: 'https://macamisp.github.io/x-animation-tree/',
      imageUrl: '/Image_fx (14).jpg',
      featured: false
    },

    {
      id: 'hartanimation  ',
      title: 'Hart-animation',
      description: 'using the HTML,CSS and javaScript to create a hart animation.',
      technologies: [ 'HTML', 'JavaScript', 'CSS'],
      githubUrl: 'https://macamisp.github.io/hart--animation/',
      imageUrl: '/1234567890.png',
      featured: false
    },

    {
      id: 'ai_gherkin  ',
      title: 'AI-gherkin',
      description: 'using the HTML,CSS and javaScript and Teachable Machine  to create a image detector .',
      technologies: [ 'HTML', 'JavaScript', 'CSS'],
      githubUrl: 'https://macamisp.github.io/ai_gherkin/',
      imageUrl: '/11.png',
      featured: false
    },















  ];

  private education: Education[] = [
    {
      institution: 'University of Moratuwa',
      degree: 'Bachelor of Information Technology (BIT)',
      period: '2025 - Present',
      location: 'Moratuwa',
      description: 'Currently pursuing undergraduate degree in Information Technology with focus on software engineering, system design, and emerging technologies.'
    },
    {
      institution: 'Institute of Computer Engineering Technology (ICET)',
      degree: 'ICET Certified Developer (ICD)',
      period: 'Jan 2010 - Feb 2023',
      location: 'Panadura'
    },
    {
      institution: 'St. John\'s College, Panadura',
      degree: 'G.C.E Advanced Level - A/L MALA - B',
      period: '2021-2023',
      location: 'Panadura'
    },
    {
      institution: 'St. John\'s College, Panadura',
      degree: 'G.C.E Ordinary Level',
      period: '2010-2022',
      location: 'Panadura'
    }
  ];

  private workExperience: WorkExperience[] = [
    {
      company: 'Universal Software Company',
      position: 'Frontend Engineer Intern',
      period: 'January 2025 - Present',
      responsibilities: [
        'Developing user interfaces using Angular for scalable web applications',
        'Collaborating with backend teams to integrate REST APIs',
        'Participating in code reviews and agile development processes',
        'Enhancing UI/UX design to improve user interaction and performance'
      ]
    }
  ];

  private fitnessJourney: WorkExperience[] = [
    {
      company: 'Personal Fitness Development',
      position: 'Dedicated Fitness Training',
      period: '2021 - Present (3+ Years)',
      responsibilities: [
        'Maintained consistent strength training routine with progressive overload principles',
        'Achieved significant improvements in compound movements: deadlifts, squats, bench press',
        'Developed exceptional endurance through cardiovascular training and HIIT workouts',
        'Applied systematic approach to goal-setting and performance tracking',
        'Demonstrated mental resilience and discipline through consistent daily routines',
        'Utilized functional fitness methodologies for overall physical development',
        'Translated fitness discipline into professional work ethic and problem-solving mindset'
      ]
    }
  ];

  private socialLinks: SocialLink[] = [
    { name: 'GitHub', url: 'https://github.com/macamisp', icon: 'fab fa-github', color: '#333' },
    { name: 'LinkedIn', url: 'https://www.linkedin.com/in/sachin-samarawickrama-*********/', icon: 'fab fa-linkedin', color: '#0077b5' },
    { name: 'Facebook', url: 'https://www.facebook.com/profile.php?id=100069446774540', icon: 'fab fa-facebook', color: '#1877f2' },
    { name: 'Instagram', url: 'https://www.instagram.com/macami_impres/', icon: 'fab fa-instagram', color: '#e4405f' },
    { name: 'Threads', url: 'https://www.threads.com/@macami_impres?xmt=AQGzSQbxGWB_x5fu5c2M7HjJLu0rVs6W7XoMbkST3RdUPDs', icon: 'fab fa-threads', color: '#000' },
    { name: 'Telegram', url: 'https://t.me/MACAMI_1X1080SP991', icon: 'fab fa-telegram', color: '#0088cc' },
    { name: 'WhatsApp', url: 'https://wa.me/94788179855', icon: 'fab fa-whatsapp', color: '#25d366' }
  ];

  private languages: Language[] = [
    { name: 'Sinhala', level: 'Native' },
    { name: 'English', level: 'Fluent' }
  ];

  private hobbies: Hobby[] = [
    { name: 'Fitness Activities', icon: 'fas fa-dumbbell' },
    { name: 'Traveling', icon: 'fas fa-plane' },
    { name: 'Photography', icon: 'fas fa-camera' },
    { name: 'Cricket', icon: 'fas fa-baseball-ball' },
    { name: 'Badminton', icon: 'fas fa-shuttlecock' }
  ];

  private leadership: Leadership[] = [
    {
      title: 'Prefect at St. John\'s College Panadura',
      description: 'Being a prefect in st john\'s college panadura'
    },
    {
      title: 'School Scout Society Member',
      description: 'Being a member in school scout society'
    },
    {
      title: 'Class Monitor',
      description: 'Being a class monitor in school'
    }
  ];

  private otherQualifications: OtherQualification[] = [
    {
      category: 'Organizing and Coordinating',
      items: [
        'Being an active member in school media club',
        'Good skilled in organizing work',
        'Participated in organizing committee of st john\'s college'
      ]
    },
    {
      category: 'Sports & Extracurricular Activities',
      items: [
        'Participated in cricket',
        'Participated in cricket',
        'Participated in badminton'
      ]
    }
  ];

  constructor(private githubService: GithubService) { }

  getPersonalInfo(): PersonalInfo {
    return this.personalInfo;
  }

  getSkills(): Skill[] {
    return this.skills;
  }

  getSkillsByCategory(category: string): Skill[] {
    return this.skills.filter(skill => skill.category === category);
  }

  getProjects(): Project[] {
    return this.projects;
  }

  getFeaturedProjects(): Project[] {
    return this.projects.filter(project => project.featured);
  }

  getEducation(): Education[] {
    return this.education;
  }

  getWorkExperience(): WorkExperience[] {
    return this.workExperience;
  }

  getSocialLinks(): SocialLink[] {
    return this.socialLinks;
  }

  getLanguages(): Language[] {
    return this.languages;
  }

  getHobbies(): Hobby[] {
    return this.hobbies;
  }

  getLeadership(): Leadership[] {
    return this.leadership;
  }

  getOtherQualifications(): OtherQualification[] {
    return this.otherQualifications;
  }

  getFitnessJourney(): WorkExperience[] {
    return this.fitnessJourney;
  }

  /**
   * Get all projects (static + GitHub repositories)
   */
  getAllProjects(): Observable<Project[]> {
    return combineLatest([
      of(this.projects),
      this.githubService.getRepositories()
    ]).pipe(
      map(([staticProjects, githubRepos]) => {
        const githubProjects = this.convertGitHubToProjects(githubRepos);
        return this.mergeProjects(staticProjects, githubProjects);
      }),
      catchError(error => {
        console.error('Error fetching projects:', error);
        return of(this.projects); // Fallback to static projects
      })
    );
  }

  /**
   * Get only featured projects
   */
  getFeaturedProjectsObservable(): Observable<Project[]> {
    return this.getAllProjects().pipe(
      map(projects => projects.filter(project => project.featured))
    );
  }

  /**
   * Get projects by technology/language
   */
  getProjectsByTechnology(tech: string): Observable<Project[]> {
    return this.getAllProjects().pipe(
      map(projects => projects.filter(project =>
        project.technologies.some(t => t.toLowerCase().includes(tech.toLowerCase())) ||
        project.language?.toLowerCase().includes(tech.toLowerCase())
      ))
    );
  }

  /**
   * Convert GitHub repositories to Project format
   */
  private convertGitHubToProjects(repos: ProcessedRepository[]): Project[] {
    return repos.map(repo => ({
      id: repo.name,
      title: repo.displayName,
      description: repo.description,
      technologies: [repo.language, ...repo.topics].filter(Boolean),
      githubUrl: repo.githubUrl,
      liveUrl: repo.liveUrl || undefined,
      imageUrl: repo.imageUrl,
      featured: repo.isFeatured,
      language: repo.language,
      stars: repo.stars,
      lastUpdated: repo.lastUpdated,
      isFromGitHub: true
    }));
  }

  /**
   * Merge static projects with GitHub projects, avoiding duplicates
   */
  private mergeProjects(staticProjects: Project[], githubProjects: Project[]): Project[] {
    const merged = [...staticProjects];

    githubProjects.forEach(githubProject => {
      const existingIndex = merged.findIndex(p =>
        p.githubUrl === githubProject.githubUrl ||
        p.id === githubProject.id
      );

      if (existingIndex >= 0) {
        // Update existing project with GitHub data
        merged[existingIndex] = {
          ...merged[existingIndex],
          ...githubProject,
          // Keep original description if it's more detailed
          description: merged[existingIndex].description.length > githubProject.description.length
            ? merged[existingIndex].description
            : githubProject.description,
          technologies: [...new Set([...merged[existingIndex].technologies, ...githubProject.technologies])]
        };
      } else {
        // Add new GitHub project
        merged.push(githubProject);
      }
    });

    return merged.sort((a, b) => {
      // Featured first, then by stars (if available), then by last updated
      if (a.featured && !b.featured) return -1;
      if (!a.featured && b.featured) return 1;
      if (a.stars && b.stars && a.stars !== b.stars) return b.stars - a.stars;
      if (a.lastUpdated && b.lastUpdated) return b.lastUpdated.getTime() - a.lastUpdated.getTime();
      return 0;
    });
  }
}
