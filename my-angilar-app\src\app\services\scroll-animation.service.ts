import { Injectable } from '@angular/core';
import { fromEvent, Observable } from 'rxjs';
import { throttleTime, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ScrollAnimationService {
  private observer: IntersectionObserver | null = null;

  constructor() { }

  /**
   * Create an intersection observer for scroll-triggered animations
   */
  createIntersectionObserver(
    callback: (entries: IntersectionObserverEntry[]) => void,
    options: IntersectionObserverInit = {}
  ): IntersectionObserver {
    const defaultOptions: IntersectionObserverInit = {
      root: null,
      rootMargin: '0px 0px -100px 0px',
      threshold: 0.1,
      ...options
    };

    return new IntersectionObserver(callback, defaultOptions);
  }

  /**
   * Observe element for scroll animations
   */
  observeElement(
    element: Element,
    callback: (entry: IntersectionObserverEntry) => void,
    options?: IntersectionObserverInit
  ): void {
    const observer = this.createIntersectionObserver(
      (entries) => {
        entries.forEach(callback);
      },
      options
    );

    observer.observe(element);
  }

  /**
   * Animate skill progress bars when they come into view
   */
  animateSkillBars(skillBars: NodeListOf<Element>): void {
    const observer = this.createIntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const progressBar = entry.target as HTMLElement;
            const percentage = progressBar.getAttribute('data-percentage') || '0';

            // Add animation class
            progressBar.classList.add('animate');

            // Set the width with animation
            setTimeout(() => {
              progressBar.style.width = percentage + '%';
            }, 100);

            // Unobserve after animation
            observer.unobserve(progressBar);
          }
        });
      },
      { threshold: 0.5 }
    );

    skillBars.forEach(bar => observer.observe(bar));
  }

  /**
   * Staggered animation for project cards
   */
  animateProjectCards(cards: NodeListOf<Element>): void {
    const observer = this.createIntersectionObserver(
      (entries) => {
        entries.forEach((entry, index) => {
          if (entry.isIntersecting) {
            const card = entry.target as HTMLElement;

            // Add staggered delay
            setTimeout(() => {
              card.classList.add('animate-in');
            }, index * 100);

            observer.unobserve(card);
          }
        });
      },
      { threshold: 0.2 }
    );

    cards.forEach(card => observer.observe(card));
  }

  /**
   * Get scroll position observable
   */
  getScrollPosition(): Observable<number> {
    return fromEvent(window, 'scroll').pipe(
      throttleTime(16), // ~60fps
      map(() => window.pageYOffset)
    );
  }

  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  /**
   * Cleanup observers
   */
  cleanup(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
}
