import { Component, OnInit, On<PERSON><PERSON>roy, Input, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  opacity: number;
  shape: 'circle' | 'triangle' | 'square' | 'diamond';
  color: string;
}

@Component({
  selector: 'app-background-animation',
  imports: [CommonModule],
  templateUrl: './background-animation.component.html',
  styleUrl: './background-animation.component.scss'
})
export class BackgroundAnimationComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() particleCount: number = 50;
  @Input() animationType: 'particles' | 'geometric' | 'gradient' = 'particles';
  @Input() intensity: 'low' | 'medium' | 'high' = 'medium';

  particles: Particle[] = [];
  private animationId: number = 0;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  prefersReducedMotion: boolean = false;

  constructor(private elementRef: ElementRef) {}

  ngOnInit(): void {
    // Check for reduced motion preference
    this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (!this.prefersReducedMotion) {
      this.initializeParticles();
    }
  }

  ngAfterViewInit(): void {
    if (!this.prefersReducedMotion) {
      this.setupCanvas();
      this.startAnimation();
    }
  }

  ngOnDestroy(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  }

  private initializeParticles(): void {
    const colors = ['#4a6cf7', '#764ba2', '#667eea', '#764ba2'];
    const shapes: Particle['shape'][] = ['circle', 'triangle', 'square', 'diamond'];

    for (let i = 0; i < this.particleCount; i++) {
      this.particles.push({
        id: i,
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        size: Math.random() * 4 + 2,
        speedX: (Math.random() - 0.5) * 0.5,
        speedY: (Math.random() - 0.5) * 0.5,
        opacity: Math.random() * 0.5 + 0.1,
        shape: shapes[Math.floor(Math.random() * shapes.length)],
        color: colors[Math.floor(Math.random() * colors.length)]
      });
    }
  }

  private setupCanvas(): void {
    this.canvas = this.elementRef.nativeElement.querySelector('canvas');
    if (this.canvas) {
      this.ctx = this.canvas.getContext('2d');
      this.resizeCanvas();

      // Handle window resize
      window.addEventListener('resize', () => this.resizeCanvas());
    }
  }

  private resizeCanvas(): void {
    if (this.canvas) {
      this.canvas.width = window.innerWidth;
      this.canvas.height = window.innerHeight;
    }
  }

  private startAnimation(): void {
    if (this.prefersReducedMotion) return;

    const animate = () => {
      this.updateParticles();
      this.drawParticles();
      this.animationId = requestAnimationFrame(animate);
    };

    animate();
  }

  private updateParticles(): void {
    this.particles.forEach(particle => {
      particle.x += particle.speedX;
      particle.y += particle.speedY;

      // Wrap around screen edges
      if (particle.x > window.innerWidth) particle.x = 0;
      if (particle.x < 0) particle.x = window.innerWidth;
      if (particle.y > window.innerHeight) particle.y = 0;
      if (particle.y < 0) particle.y = window.innerHeight;
    });
  }

  private drawParticles(): void {
    if (!this.ctx || !this.canvas) return;

    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    this.particles.forEach(particle => {
      this.ctx!.save();
      this.ctx!.globalAlpha = particle.opacity;
      this.ctx!.fillStyle = particle.color;

      this.drawShape(particle);
      this.ctx!.restore();
    });

    // Draw connections between nearby particles
    this.drawConnections();
  }

  private drawShape(particle: Particle): void {
    if (!this.ctx) return;

    const { x, y, size, shape } = particle;

    switch (shape) {
      case 'circle':
        this.ctx.beginPath();
        this.ctx.arc(x, y, size, 0, Math.PI * 2);
        this.ctx.fill();
        break;

      case 'square':
        this.ctx.fillRect(x - size, y - size, size * 2, size * 2);
        break;

      case 'triangle':
        this.ctx.beginPath();
        this.ctx.moveTo(x, y - size);
        this.ctx.lineTo(x - size, y + size);
        this.ctx.lineTo(x + size, y + size);
        this.ctx.closePath();
        this.ctx.fill();
        break;

      case 'diamond':
        this.ctx.beginPath();
        this.ctx.moveTo(x, y - size);
        this.ctx.lineTo(x + size, y);
        this.ctx.lineTo(x, y + size);
        this.ctx.lineTo(x - size, y);
        this.ctx.closePath();
        this.ctx.fill();
        break;
    }
  }

  private drawConnections(): void {
    if (!this.ctx) return;

    const maxDistance = 100;

    for (let i = 0; i < this.particles.length; i++) {
      for (let j = i + 1; j < this.particles.length; j++) {
        const particle1 = this.particles[i];
        const particle2 = this.particles[j];

        const distance = Math.sqrt(
          Math.pow(particle1.x - particle2.x, 2) +
          Math.pow(particle1.y - particle2.y, 2)
        );

        if (distance < maxDistance) {
          const opacity = (1 - distance / maxDistance) * 0.1;

          this.ctx.save();
          this.ctx.globalAlpha = opacity;
          this.ctx.strokeStyle = '#4a6cf7';
          this.ctx.lineWidth = 1;
          this.ctx.beginPath();
          this.ctx.moveTo(particle1.x, particle1.y);
          this.ctx.lineTo(particle2.x, particle2.y);
          this.ctx.stroke();
          this.ctx.restore();
        }
      }
    }
  }
}
