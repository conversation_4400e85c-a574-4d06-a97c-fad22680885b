/* Import Font Awesome */
@import '@fortawesome/fontawesome-free/css/all.min.css';

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Variables */
:root {
  --primary-color: #4a6cf7;
  --secondary-color: #2c3e50;
  --text-color: #333;
  --light-color: #fff;
  --dark-color: #222;
  --gray-color: #666;
  --light-gray: #f8f9fa;
  --transition-speed: 0.3s;
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

img {
  max-width: 100%;
  height: auto;
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section {
  padding: 100px 0;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 50px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: var(--primary-color);
  border-radius: 2px;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--light-color);
}

.btn-primary:hover {
  background-color: darken(#4a6cf7, 10%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(74, 108, 247, 0.3);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: var(--light-color);
  transform: translateY(-2px);
}

/* Social Media Colors */
.social-link {
  transition: all var(--transition-speed) ease;

  &:hover {
    background-color: var(--social-color) !important;
    color: var(--light-color) !important;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 1s ease-in-out;
}

.fade-in-up {
  animation: fadeInUp 1s ease-in-out;
}

.fade-in-right {
  animation: fadeInRight 1s ease-in-out;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Media Queries */
@media (max-width: 768px) {
  .section {
    padding: 80px 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .btn {
    padding: 10px 25px;
  }
}
