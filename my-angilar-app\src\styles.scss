/* Import Font Awesome */
@import '@fortawesome/fontawesome-free/css/all.min.css';

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Variables */
:root {
  --primary-color: #4a6cf7;
  --secondary-color: #2c3e50;
  --text-color: #333;
  --light-color: #fff;
  --dark-color: #222;
  --gray-color: #666;
  --light-gray: #f8f9fa;
  --transition-speed: 0.3s;
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px; // Account for fixed header
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

img {
  max-width: 100%;
  height: auto;
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section {
  padding: 100px 0;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 50px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: var(--primary-color);
  border-radius: 2px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 30px;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  text-decoration: none;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;

    &::before {
      display: none;
    }
  }
}

.btn-primary {
  background: linear-gradient(90deg, var(--primary-color), #764ba2);
  color: var(--light-color);

  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 15px 30px rgba(74, 108, 247, 0.4);
  }
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);

  &:hover {
    background: linear-gradient(90deg, var(--primary-color), #764ba2);
    color: var(--light-color);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 15px 30px rgba(74, 108, 247, 0.4);
  }
}

/* Social Media Colors */
.social-link {
  transition: all var(--transition-speed) ease;

  &:hover {
    background-color: var(--social-color) !important;
    color: var(--light-color) !important;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 1s ease-in-out;
}

.fade-in-up {
  animation: fadeInUp 1s ease-in-out;
}

.fade-in-right {
  animation: fadeInRight 1s ease-in-out;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Media Queries */
@media (max-width: 768px) {
  .section {
    padding: 80px 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .btn {
    padding: 10px 25px;
  }
}

// Accessibility: Respect user's motion preferences
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .btn {
    &:hover {
      transform: none;
    }

    &::before {
      display: none;
    }
  }
}
