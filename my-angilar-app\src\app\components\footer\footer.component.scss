// Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$gray-color: #666;
$light-gray: #f8f9fa;
$transition-speed: 0.3s;

.footer {
  background: linear-gradient(135deg, $secondary-color, $dark-color);
  color: $light-color;
  padding: 60px 20px 20px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-logo {
  h3 {
    font-size: 2rem;
    font-weight: 700;
    color: $primary-color;
    margin-bottom: 10px;
  }

  p {
    color: rgba($light-color, 0.8);
    font-size: 1.1rem;
  }
}

.footer-links {
  h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: $light-color;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      margin-bottom: 10px;

      a {
        color: rgba($light-color, 0.8);
        text-decoration: none;
        transition: color $transition-speed ease;

        &:hover {
          color: $primary-color;
        }
      }
    }
  }
}

.footer-social {
  h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: $light-color;
  }

  .social-icons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;

    a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 45px;
      height: 45px;
      background: rgba($light-color, 0.1);
      border-radius: 50%;
      color: $light-color;
      font-size: 1.3rem;
      transition: all $transition-speed ease;
      text-decoration: none;

      &:hover {
        background: var(--social-color, $primary-color);
        color: $light-color;
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
      }
    }
  }
}

.footer-bottom {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 30px;
  border-top: 1px solid rgba($light-color, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;

  p {
    color: rgba($light-color, 0.8);
    margin: 0;
  }

  .scroll-top {
    background: $primary-color;
    color: $light-color;
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    transition: all $transition-speed ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: darken($primary-color, 10%);
      transform: translateY(-3px);
      box-shadow: 0 10px 20px rgba($primary-color, 0.3);
    }

    i {
      font-size: 1.2rem;
    }
  }
}

// Media Queries
@media (max-width: 768px) {
  .footer {
    padding: 40px 15px 15px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .footer-social .social-icons {
    justify-content: center;
  }
}