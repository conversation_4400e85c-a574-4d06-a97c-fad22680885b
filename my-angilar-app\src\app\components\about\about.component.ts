import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PortfolioService, PersonalInfo, Education, WorkExperience, Language, Hobby, Leadership, OtherQualification } from '../../services/portfolio.service';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './about.component.html',
  styleUrl: './about.component.scss'
})
export class AboutComponent {
  personalInfo: PersonalInfo;
  education: Education[];
  workExperience: WorkExperience[];
  fitnessJourney: WorkExperience[];
  languages: Language[];
  hobbies: Hobby[];
  leadership: Leadership[];
  otherQualifications: OtherQualification[];

  constructor(private portfolioService: PortfolioService) {
    this.personalInfo = this.portfolioService.getPersonalInfo();
    this.education = this.portfolioService.getEducation();
    this.workExperience = this.portfolioService.getWorkExperience();
    this.fitnessJourney = this.portfolioService.getFitnessJourney();
    this.languages = this.portfolioService.getLanguages();
    this.hobbies = this.portfolioService.getHobbies();
    this.leadership = this.portfolioService.getLeadership();
    this.otherQualifications = this.portfolioService.getOtherQualifications();
  }
}
