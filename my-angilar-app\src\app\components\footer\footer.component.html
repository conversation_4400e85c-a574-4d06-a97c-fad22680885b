<footer class="footer">
  <div class="footer-content">
    <div class="footer-logo">
      <h3>{{ personalInfo.name.split(' ')[0] }}</h3>
      <p>{{ personalInfo.title }}</p>
    </div>

    <div class="footer-links">
      <h4>Quick Links</h4>
      <ul>
        <li><a href="#" (click)="scrollToTop()">Home</a></li>
        <li><a href="#about">About</a></li>
        <li><a href="#skills">Skills</a></li>
        <li><a href="#projects">Projects</a></li>
        <li><a href="#contact">Contact</a></li>
      </ul>
    </div>

    <div class="footer-social">
      <h4>Connect</h4>
      <div class="social-icons">
        <a *ngFor="let social of socialLinks"
           [href]="social.url"
           target="_blank"
           [attr.aria-label]="social.name"
           [style.--social-color]="social.color">
          <i [class]="social.icon"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="footer-bottom">
    <p>&copy; {{ currentYear }} {{ personalInfo.name }}. All Rights Reserved.</p>
    <button class="scroll-top" (click)="scrollToTop()">
      <i class="fas fa-arrow-up"></i>
    </button>
  </div>
</footer>
