// Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$transition-speed: 0.3s;
$header-height: 70px;

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: $header-height;
  background-color: rgba($light-color, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all $transition-speed ease;

  &.scrolled {
    background-color: rgba($light-color, 0.98);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}

.navbar {
  height: 100%;
}

.nav-container {
  max-width: 1200px;
  height: 100%;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  .brand-link {
    text-decoration: none;
    font-size: 1.8rem;
    font-weight: 700;
    color: $primary-color;
    transition: color $transition-speed ease;

    &:hover {
      color: darken($primary-color, 10%);
    }
  }
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;

  .nav-item {
    margin-left: 30px;

    .nav-link {
      text-decoration: none;
      color: $text-color;
      font-size: 1rem;
      font-weight: 500;
      padding: 8px 0;
      position: relative;
      transition: color $transition-speed ease;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background-color: $primary-color;
        transition: width $transition-speed ease;
      }

      &:hover {
        color: $primary-color;

        &::after {
          width: 100%;
        }
      }
    }
  }
}

.nav-toggle {
  display: none;
  cursor: pointer;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;

  .bar {
    display: block;
    width: 100%;
    height: 3px;
    background-color: $text-color;
    border-radius: 10px;
    transition: all $transition-speed ease;
  }

  &.active {
    .bar:nth-child(1) {
      transform: translateY(9px) rotate(45deg);
    }

    .bar:nth-child(2) {
      opacity: 0;
    }

    .bar:nth-child(3) {
      transform: translateY(-9px) rotate(-45deg);
    }
  }
}

// Media Queries for Responsive Design
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: $header-height;
    left: -100%;
    flex-direction: column;
    background-color: $light-color;
    width: 100%;
    text-align: center;
    transition: left $transition-speed ease;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    padding: 20px 0;

    &.active {
      left: 0;
    }

    .nav-item {
      margin: 15px 0;
    }
  }

  .nav-toggle {
    display: flex;
  }
}