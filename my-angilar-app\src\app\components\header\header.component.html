<header class="header" [class.scrolled]="isScrolled">
  <nav class="navbar">
    <div class="nav-container">
      <!-- Logo/Brand -->
      <div class="nav-brand">
        <a href="#" (click)="scrollToSection('hero')" class="brand-link">
          <span class="brand-text">{{ personalInfo.name.split(' ')[0] }}</span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <ul class="nav-menu" [class.active]="isMenuOpen">
        <li class="nav-item">
          <a href="#" (click)="scrollToSection('hero')" class="nav-link">Home</a>
        </li>
        <li class="nav-item">
          <a href="#" (click)="scrollToSection('about')" class="nav-link">About</a>
        </li>
        <li class="nav-item">
          <a href="#" (click)="scrollToSection('skills')" class="nav-link">Skills</a>
        </li>
        <li class="nav-item">
          <a href="#" (click)="scrollToSection('projects')" class="nav-link">Projects</a>
        </li>
        <li class="nav-item">
          <a href="#" (click)="scrollToSection('contact')" class="nav-link">Contact</a>
        </li>
      </ul>

      <!-- Mobile Menu Toggle -->
      <div class="nav-toggle" (click)="toggleMenu()" [class.active]="isMenuOpen">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </div>
    </div>
  </nav>
</header>
