// Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$gray-color: #666;
$light-gray: #f8f9fa;
$transition-speed: 0.3s;

.about-container {
  padding: 100px 20px;
  background-color: $light-gray;
  min-height: 100vh;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    color: $secondary-color;
    margin-bottom: 20px;
    position: relative;
  }

  .section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, $primary-color, #764ba2);
    margin: 0 auto;
    border-radius: 2px;
  }
}

.about-content {
  max-width: 1200px;
  margin: 0 auto 80px;

  .about-text {
    background: $light-color;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    h3 {
      font-size: 2rem;
      color: $secondary-color;
      margin-bottom: 20px;
      font-weight: 600;
    }

    p {
      font-size: 1.1rem;
      line-height: 1.8;
      color: $gray-color;
      margin-bottom: 30px;
    }
  }
}

.personal-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;

  .info-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: $light-gray;
    border-radius: 10px;
    transition: transform $transition-speed ease;

    &:hover {
      transform: translateY(-2px);
    }

    .info-label {
      font-weight: 600;
      color: $secondary-color;
      margin-right: 10px;
      min-width: 80px;
    }

    .info-value {
      color: $gray-color;
      font-weight: 500;
    }
  }
}

.timeline-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 60px;
}

.timeline-section {
  .timeline-title {
    font-size: 2rem;
    color: $secondary-color;
    margin-bottom: 40px;
    font-weight: 600;
    text-align: center;
  }
}

.timeline {
  position: relative;
  padding-left: 30px;

  &::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, $primary-color, #764ba2);
  }

  .timeline-item {
    position: relative;
    margin-bottom: 40px;
    background: $light-color;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all $transition-speed ease;

    &:hover {
      transform: translateX(10px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .timeline-dot {
      position: absolute;
      left: -37px;
      top: 30px;
      width: 12px;
      height: 12px;
      background: $primary-color;
      border-radius: 50%;
      border: 3px solid $light-color;
      box-shadow: 0 0 0 3px $primary-color;
    }

    .timeline-date {
      font-size: 0.9rem;
      color: $primary-color;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .timeline-content {
      h4 {
        font-size: 1.3rem;
        color: $secondary-color;
        margin-bottom: 8px;
        font-weight: 600;
      }

      p {
        color: $gray-color;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .responsibility-list {
        list-style: none;
        padding: 0;

        li {
          position: relative;
          padding-left: 20px;
          margin-bottom: 8px;
          color: $gray-color;
          line-height: 1.6;

          &::before {
            content: '▸';
            position: absolute;
            left: 0;
            color: $primary-color;
            font-weight: bold;
          }
        }
      }
    }
  }
}

// Additional Info Sections
.additional-info {
  max-width: 1200px;
  margin: 60px auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.info-section {
  background: $light-color;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  .info-title {
    font-size: 1.8rem;
    color: $secondary-color;
    margin-bottom: 25px;
    font-weight: 600;
    position: relative;
    padding-bottom: 15px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 3px;
      background: linear-gradient(90deg, $primary-color, #764ba2);
      border-radius: 2px;
    }
  }
}

.languages-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;

  .language-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: $light-gray;
    border-radius: 10px;
    transition: transform $transition-speed ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .language-name {
      font-weight: 600;
      color: $secondary-color;
    }

    .language-level {
      color: $primary-color;
      font-weight: 500;
      padding: 5px 10px;
      background: rgba($primary-color, 0.1);
      border-radius: 20px;
      font-size: 0.9rem;
    }
  }
}

.hobbies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;

  .hobby-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px 10px;
    background: $light-gray;
    border-radius: 10px;
    transition: all $transition-speed ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      background: linear-gradient(135deg, $primary-color, #764ba2);
      color: $light-color;
    }

    i {
      font-size: 2rem;
      margin-bottom: 10px;
      color: $primary-color;
    }

    &:hover i {
      color: $light-color;
    }

    span {
      font-weight: 500;
      font-size: 0.9rem;
    }
  }
}

.qualifications-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;

  .qualification-category {
    background: $light-color;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all $transition-speed ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .category-name {
      font-size: 1.3rem;
      color: $primary-color;
      margin-bottom: 15px;
      font-weight: 600;
    }

    .qualification-list {
      list-style: none;
      padding: 0;

      li {
        position: relative;
        padding-left: 25px;
        margin-bottom: 10px;
        color: $gray-color;
        line-height: 1.6;

        &::before {
          content: '✓';
          position: absolute;
          left: 0;
          color: $primary-color;
          font-weight: bold;
        }
      }
    }
  }
}

// Media Queries
@media (max-width: 768px) {
  .about-container {
    padding: 80px 15px;
  }

  .section-header .section-title {
    font-size: 2.5rem;
  }

  .about-content .about-text {
    padding: 30px 20px;

    h3 {
      font-size: 1.8rem;
    }

    p {
      font-size: 1rem;
    }
  }

  .personal-info {
    grid-template-columns: 1fr;
  }

  .timeline-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .timeline-section .timeline-title {
    font-size: 1.8rem;
  }

  .timeline {
    padding-left: 25px;

    .timeline-item {
      padding: 20px;

      .timeline-dot {
        left: -32px;
      }

      &:hover {
        transform: translateX(5px);
      }
    }
  }

  .additional-info {
    grid-template-columns: 1fr;
    gap: 30px;
    margin: 40px auto;
  }

  .info-section {
    padding: 25px 20px;

    .info-title {
      font-size: 1.6rem;
    }
  }

  .hobbies-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}