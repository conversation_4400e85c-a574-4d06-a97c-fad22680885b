@import '../../../app/styles/shared';

.about-container {
  @include section-container;
}

.section-header {
  @include section-header;
  margin-bottom: 80px;

  .section-title {
    @include section-title;
  }

  .section-divider {
    @include section-divider;
    height: 4px;
    background: linear-gradient(90deg, $primary-color, #764ba2);
    margin: 0 auto;
    border-radius: 2px;
  }
}

.about-content {
  max-width: 1200px;
  margin: 0 auto 80px;

  .about-text {
    background: $light-color;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    h3 {
      font-size: 2rem;
      color: $secondary-color;
      margin-bottom: 20px;
      font-weight: 600;
    }

    p {
      font-size: 1.1rem;
      line-height: 1.8;
      color: $gray-color;
      margin-bottom: 30px;
    }
  }
}

.personal-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;

  .info-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: $light-gray;
    border-radius: 10px;
    transition: transform $transition-speed ease;

    &:hover {
      transform: translateY(-2px);
    }

    .info-label {
      font-weight: 600;
      color: $secondary-color;
      margin-right: 10px;
      min-width: 80px;
    }

    .info-value {
      color: $gray-color;
      font-weight: 500;
    }
  }
}

.timeline-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 60px;
}

.timeline-section {
  .timeline-title {
    font-size: 2rem;
    color: $secondary-color;
    margin-bottom: 40px;
    font-weight: 600;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;

    .fitness-icon {
      color: $primary-color;
      font-size: 1.8rem;
    }
  }
//macami impress  the powerd by nashnix
  &.fitness-section {
    .timeline-title {
      color: $primary-color;
      background: linear-gradient(135deg, rgba($primary-color, 0.1), rgba(#764ba2, 0.1));
      padding: 20px;
      border-radius: 15px;
      margin-bottom: 50px;
    }
  }
}

.timeline {
  position: relative;
  padding-left: 30px;

  &::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, $primary-color, #764ba2);
  }

  .timeline-item {
    position: relative;
    margin-bottom: 40px;
    background: $light-color;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all $transition-speed ease;

    &:hover {
      transform: translateX(10px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .timeline-dot {
      position: absolute;
      left: -37px;
      top: 30px;
      width: 12px;
      height: 12px;
      background: $primary-color;
      border-radius: 50%;
      border: 3px solid $light-color;
      box-shadow: 0 0 0 3px $primary-color;

      &.fitness-dot {
        background: linear-gradient(135deg, $primary-color, #764ba2);
        box-shadow: 0 0 0 3px $primary-color, 0 0 20px rgba($primary-color, 0.3);
        animation: pulse-fitness 2s infinite;
      }
    }

    .timeline-date {
      font-size: 0.9rem;
      color: $primary-color;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .timeline-content {
      h4 {
        font-size: 1.3rem;
        color: $secondary-color;
        margin-bottom: 8px;
        font-weight: 600;
      }

      p {
        color: $gray-color;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .responsibility-list {
        list-style: none;
        padding: 0;

        li {
          position: relative;
          padding-left: 20px;
          margin-bottom: 8px;
          color: $gray-color;
          line-height: 1.6;

          &::before {
            content: '▸';
            position: absolute;
            left: 0;
            color: $primary-color;
            font-weight: bold;
          }
        }
      }
    }
  }
}

// Additional Info Sections
.additional-info {
  max-width: 1200px;
  margin: 60px auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.info-section {
  background: $light-color;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  .info-title {
    font-size: 1.8rem;
    color: $secondary-color;
    margin-bottom: 25px;
    font-weight: 600;
    position: relative;
    padding-bottom: 15px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 3px;
      background: linear-gradient(90deg, $primary-color, #764ba2);
      border-radius: 2px;
    }
  }
}

.languages-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;

  .language-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: $light-gray;
    border-radius: 10px;
    transition: transform $transition-speed ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .language-name {
      font-weight: 600;
      color: $secondary-color;
    }

    .language-level {
      color: $primary-color;
      font-weight: 500;
      padding: 5px 10px;
      background: rgba($primary-color, 0.1);
      border-radius: 20px;
      font-size: 0.9rem;
    }
  }
}

.hobbies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;

  .hobby-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px 10px;
    background: $light-gray;
    border-radius: 10px;
    transition: all $transition-speed ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      background: linear-gradient(135deg, $primary-color, #764ba2);
      color: $light-color;
    }

    i {
      font-size: 2rem;
      margin-bottom: 10px;
      color: $primary-color;
    }

    &:hover i {
      color: $light-color;
    }

    span {
      font-weight: 500;
      font-size: 0.9rem;
    }
  }
}

.qualifications-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;

  .qualification-category {
    background: $light-color;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all $transition-speed ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .category-name {
      font-size: 1.3rem;
      color: $primary-color;
      margin-bottom: 15px;
      font-weight: 600;
    }

    .qualification-list {
      list-style: none;
      padding: 0;

      li {
        position: relative;
        padding-left: 25px;
        margin-bottom: 10px;
        color: $gray-color;
        line-height: 1.6;

        &::before {
          content: '✓';
          position: absolute;
          left: 0;
          color: $primary-color;
          font-weight: bold;
        }
      }
    }

    &.fitness-item {
      background: linear-gradient(135deg, rgba($primary-color, 0.05), rgba(#764ba2, 0.05));
      border-left: 4px solid $primary-color;

      .fitness-subtitle {
        color: $primary-color;
        font-weight: 600;
        font-style: italic;
      }

      .fitness-highlights {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;

        .highlight-card {
          background: linear-gradient(135deg, $primary-color, #764ba2);
          color: $light-color;
          padding: 15px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          gap: 10px;
          font-weight: 500;
          transition: transform $transition-speed ease;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba($primary-color, 0.3);
          }

          i {
            font-size: 1.2rem;
          }
        }
      }

      .fitness-achievements {
        background: rgba($light-color, 0.8);
        padding: 20px;
        border-radius: 10px;
        margin: 20px 0;

        li {
          &::before {
            content: '💪';
            color: $primary-color;
          }
        }
      }

      .fitness-connection {
        background: linear-gradient(135deg, rgba($primary-color, 0.1), rgba(#764ba2, 0.1));
        padding: 20px;
        border-radius: 10px;
        margin-top: 20px;
        border-left: 4px solid $primary-color;

        h5 {
          color: $primary-color;
          font-size: 1.1rem;
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            font-size: 1rem;
          }
        }

        p {
          color: $gray-color;
          line-height: 1.7;
          font-style: italic;
        }
      }
    }
  }
}

// Fitness Animation
@keyframes pulse-fitness {
  0% {
    box-shadow: 0 0 0 3px $primary-color, 0 0 20px rgba($primary-color, 0.3);
  }
  50% {
    box-shadow: 0 0 0 3px $primary-color, 0 0 30px rgba($primary-color, 0.6);
  }
  100% {
    box-shadow: 0 0 0 3px $primary-color, 0 0 20px rgba($primary-color, 0.3);
  }
}

// Media Queries
@media (max-width: 768px) {
  .about-container {
    padding: 80px 15px;
    min-height: auto;
  }

  .section-header {
    margin-bottom: 60px;

    .section-title {
      font-size: 2.5rem;
      line-height: 1.2;
    }
  }

  .about-content {
    margin-bottom: 60px;

    .about-text {
      padding: 25px 20px;

      h3 {
        font-size: 1.8rem;
        line-height: 1.3;
      }

      p {
        font-size: 1rem;
        line-height: 1.7;
      }
    }
  }

  .personal-info {
    grid-template-columns: 1fr;
    gap: 15px;

    .info-item {
      padding: 12px;
      font-size: 0.95rem;

      .info-label {
        min-width: 70px;
        font-size: 0.9rem;
      }

      .info-value {
        font-size: 0.9rem;
        word-break: break-word;
      }
    }
  }

  .additional-info {
    grid-template-columns: 1fr;
    gap: 25px;
    margin: 40px auto;
  }

  .info-section {
    padding: 20px 15px;

    .info-title {
      font-size: 1.6rem;
      margin-bottom: 20px;
    }
  }

  .languages-grid {
    .language-item {
      padding: 12px;
      font-size: 0.9rem;

      .language-level {
        font-size: 0.8rem;
        padding: 4px 8px;
      }
    }
  }

  .hobbies-grid {
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 12px;

    .hobby-item {
      padding: 15px 8px;

      i {
        font-size: 1.5rem;
        margin-bottom: 8px;
      }

      span {
        font-size: 0.8rem;
        line-height: 1.2;
      }
    }
  }

  .timeline-container {
    grid-template-columns: 1fr;
    gap: 35px;
  }

  .timeline-section {
    .timeline-title {
      font-size: 1.8rem;
      margin-bottom: 30px;
    }

    &.fitness-section {
      .timeline-title {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
        font-size: 1.7rem;
        margin-bottom: 35px;

        .fitness-icon {
          font-size: 1.5rem;
        }
      }
    }
  }

  .timeline {
    padding-left: 20px;

    &::before {
      left: 10px;
    }

    .timeline-item {
      padding: 18px 15px;
      margin-bottom: 25px;

      .timeline-dot {
        left: -27px;
        top: 25px;
        width: 10px;
        height: 10px;

        &.fitness-dot {
          width: 12px;
          height: 12px;
        }
      }

      .timeline-date {
        font-size: 0.85rem;
        margin-bottom: 8px;
      }

      .timeline-content {
        h4 {
          font-size: 1.2rem;
          line-height: 1.3;
          margin-bottom: 6px;
        }

        p {
          font-size: 0.95rem;
          margin-bottom: 12px;
        }

        .responsibility-list {
          li {
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 6px;
            padding-left: 18px;
          }
        }
      }

      &:hover {
        transform: translateX(3px);
      }

      &.fitness-item {
        .fitness-subtitle {
          font-size: 0.95rem;
        }

        .fitness-highlights {
          grid-template-columns: 1fr;
          gap: 12px;
          margin: 15px 0;

          .highlight-card {
            padding: 12px;
            font-size: 0.9rem;

            i {
              font-size: 1rem;
            }
          }
        }

        .fitness-achievements {
          padding: 15px;
          margin: 15px 0;

          li {
            font-size: 0.85rem;
            line-height: 1.4;
          }
        }

        .fitness-connection {
          padding: 15px;
          margin-top: 15px;

          h5 {
            font-size: 1rem;
            margin-bottom: 8px;

            i {
              font-size: 0.9rem;
            }
          }

          p {
            font-size: 0.9rem;
            line-height: 1.6;
          }
        }
      }
    }
  }

  .qualifications-grid {
    .qualification-category {
      padding: 20px;

      .category-name {
        font-size: 1.2rem;
        margin-bottom: 12px;
      }

      .qualification-list {
        li {
          font-size: 0.9rem;
          line-height: 1.5;
          margin-bottom: 8px;
          padding-left: 20px;
        }
      }
    }
  }
}

// Extra small screens
@media (max-width: 480px) {
  .about-container {
    padding: 60px 10px;
  }

  .section-header {
    .section-title {
      font-size: 2rem;
    }
  }

  .about-content .about-text {
    padding: 20px 15px;

    h3 {
      font-size: 1.6rem;
    }

    p {
      font-size: 0.95rem;
    }
  }

  .personal-info .info-item {
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;

    .info-label {
      min-width: auto;
      font-weight: 700;
    }
  }

  .hobbies-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));

    .hobby-item {
      padding: 12px 6px;

      i {
        font-size: 1.3rem;
      }

      span {
        font-size: 0.75rem;
      }
    }
  }

  .timeline {
    padding-left: 15px;

    &::before {
      left: 7px;
    }

    .timeline-item {
      padding: 15px 12px;

      .timeline-dot {
        left: -22px;
        width: 8px;
        height: 8px;

        &.fitness-dot {
          width: 10px;
          height: 10px;
        }
      }

      .timeline-content {
        h4 {
          font-size: 1.1rem;
        }

        .responsibility-list li {
          font-size: 0.85rem;
          padding-left: 15px;
        }
      }
    }
  }
}