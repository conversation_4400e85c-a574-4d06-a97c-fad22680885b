import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PortfolioService, PersonalInfo, SocialLink } from '../../services/portfolio.service';

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss'
})
export class ContactComponent {
  personalInfo: PersonalInfo;
  socialLinks: SocialLink[];
  contactForm: FormGroup;
  isSubmitted = false;
  isSuccess = false;
  errorMessage = '';

  constructor(
    private portfolioService: PortfolioService,
    private fb: FormBuilder
  ) {
    this.personalInfo = this.portfolioService.getPersonalInfo();
    this.socialLinks = this.portfolioService.getSocialLinks();
    this.contactForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      subject: ['', Validators.required],
      message: ['', [Validators.required, Validators.minLength(10)]]
    });
  }

  onSubmit() {
    this.isSubmitted = true;

    if (this.contactForm.valid) {
      // In a real application, you would send this data to a backend service
      console.log('Form submitted:', this.contactForm.value);
      this.isSuccess = true;
      this.contactForm.reset();
      this.isSubmitted = false;

      // Reset success message after 5 seconds
      setTimeout(() => {
        this.isSuccess = false;
      }, 5000);
    } else {
      this.errorMessage = 'Please fill all required fields correctly.';
    }
  }
}
