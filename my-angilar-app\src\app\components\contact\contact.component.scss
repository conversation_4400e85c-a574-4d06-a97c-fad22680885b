// Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$gray-color: #666;
$light-gray: #f8f9fa;
$error-color: #e74c3c;
$success-color: #2ecc71;
$transition-speed: 0.3s;

.contact-container {
  padding: 100px 20px;
  background-color: $light-color;
  min-height: 100vh;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    color: $secondary-color;
    margin-bottom: 20px;
    position: relative;
  }

  .section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, $primary-color, #764ba2);
    margin: 0 auto 20px;
    border-radius: 2px;
  }

  .section-description {
    max-width: 700px;
    margin: 0 auto;
    color: $gray-color;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.contact-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 50px;
}

.contact-info {
  background: linear-gradient(135deg, $primary-color, #764ba2);
  padding: 40px;
  border-radius: 15px;
  color: $light-color;
  height: fit-content;

  h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 30px;
    position: relative;
    padding-bottom: 15px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 3px;
      background: $light-color;
      border-radius: 2px;
    }
  }
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;

  i {
    font-size: 1.5rem;
    margin-right: 15px;
    margin-top: 5px;
  }

  .contact-details {
    display: flex;
    flex-direction: column;

    .contact-label {
      font-size: 0.9rem;
      opacity: 0.8;
      margin-bottom: 5px;
    }

    .contact-value {
      font-size: 1.1rem;
      font-weight: 500;
      color: $light-color;
      text-decoration: none;
      transition: opacity $transition-speed ease;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 40px;

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba($light-color, 0.2);
    border-radius: 50%;
    color: $light-color;
    font-size: 1.3rem;
    transition: all $transition-speed ease;
    text-decoration: none;

    &:hover {
      background: $light-color;
      color: $primary-color;
      transform: translateY(-3px);
    }
  }
}

.contact-form-container {
  background: $light-color;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: $secondary-color;
    margin-bottom: 30px;
    position: relative;
    padding-bottom: 15px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 3px;
      background: $primary-color;
      border-radius: 2px;
    }
  }
}

.success-message {
  display: flex;
  align-items: center;
  background: rgba($success-color, 0.1);
  color: $success-color;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;

  i {
    font-size: 1.5rem;
    margin-right: 10px;
  }
}

.contact-form {
  .form-group {
    margin-bottom: 20px;

    label {
      display: block;
      font-weight: 500;
      margin-bottom: 8px;
      color: $secondary-color;
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 1rem;
      transition: all $transition-speed ease;

      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
      }

      &.error {
        border-color: $error-color;
      }
    }

    textarea.form-control {
      resize: vertical;
    }

    .error-message {
      color: $error-color;
      font-size: 0.9rem;
      margin-top: 5px;
    }
  }

  .btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 25px;
    font-size: 1rem;

    i {
      font-size: 1.1rem;
    }
  }
}

// Media Queries
@media (max-width: 768px) {
  .contact-container {
    padding: 80px 15px;
  }

  .section-header {
    margin-bottom: 40px;

    .section-title {
      font-size: 2.5rem;
    }

    .section-description {
      font-size: 1rem;
    }
  }

  .contact-content {
    grid-template-columns: 1fr;
  }

  .contact-info, .contact-form-container {
    padding: 30px;
  }
}