@import '../../../app/styles/shared';
$dark-color: #222;
$gray-color: #666;
$light-gray: #f8f9fa;
$error-color: #e74c3c;
$success-color: #2ecc71;
$transition-speed: 0.3s;

.contact-container {
  padding: 100px 20px;
  background-color: $light-color;
  min-height: 100vh;
}
//powerd by nashnix 
.section-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    color: $secondary-color;
    margin-bottom: 20px;
    position: relative;
  }

  .section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, $primary-color, #764ba2);
    margin: 0 auto 20px;
    border-radius: 2px;
  }

  .section-description {
    max-width: 700px;
    margin: 0 auto;
    color: $gray-color;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.contact-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 50px;
}

.contact-info {
  background: linear-gradient(135deg, $primary-color, #764ba2);
  padding: 40px;
  border-radius: 15px;
  color: $light-color;
  height: fit-content;

  h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 30px;
    position: relative;
    padding-bottom: 15px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 3px;
      background: $light-color;
      border-radius: 2px;
    }
  }
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;

  i {
    font-size: 1.5rem;
    margin-right: 15px;
    margin-top: 5px;
  }

  .contact-details {
    display: flex;
    flex-direction: column;

    .contact-label {
      font-size: 0.9rem;
      opacity: 0.8;
      margin-bottom: 5px;
    }

    .contact-value {
      font-size: 1.1rem;
      font-weight: 500;
      color: $light-color;
      text-decoration: none;
      transition: opacity $transition-speed ease;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 40px;

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba($light-color, 0.2);
    border-radius: 50%;
    color: $light-color;
    font-size: 1.3rem;
    transition: all $transition-speed ease;
    text-decoration: none;

    &:hover {
      background: $light-color;
      color: $primary-color;
      transform: translateY(-3px);
    }
  }
}

.contact-form-container {
  background: $light-color;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: $secondary-color;
    margin-bottom: 30px;
    position: relative;
    padding-bottom: 15px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 3px;
      background: $primary-color;
      border-radius: 2px;
    }
  }
}

.success-message {
  display: flex;
  align-items: center;
  background: rgba($success-color, 0.1);
  color: $success-color;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;

  i {
    font-size: 1.5rem;
    margin-right: 10px;
  }
}

.contact-form {
  .form-group {
    margin-bottom: 20px;

    label {
      display: block;
      font-weight: 500;
      margin-bottom: 8px;
      color: $secondary-color;
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 1rem;
      transition: all $transition-speed ease;

      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
      }

      &.error {
        border-color: $error-color;
      }
    }

    textarea.form-control {
      resize: vertical;
    }

    .error-message {
      color: $error-color;
      font-size: 0.9rem;
      margin-top: 5px;
    }
  }

  .btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 25px;
    font-size: 1rem;

    i {
      font-size: 1.1rem;
    }
  }
}

// Media Queries
@media (max-width: 768px) {
  .contact-container {
    padding: 80px 15px;
  }

  .section-header {
    margin-bottom: 40px;

    .section-title {
      font-size: 2.5rem;
      line-height: 1.2;
    }

    .section-description {
      font-size: 1rem;
      line-height: 1.6;
    }
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .contact-info {
    padding: 25px 20px;

    h3 {
      font-size: 1.6rem;
      margin-bottom: 25px;
    }

    .contact-item {
      margin-bottom: 20px;

      i {
        font-size: 1.3rem;
      }

      .contact-details {
        .contact-label {
          font-size: 0.85rem;
        }

        .contact-value {
          font-size: 1rem;
          word-break: break-word;
        }
      }
    }

    .social-links {
      margin-top: 30px;
      justify-content: center;
      flex-wrap: wrap;

      .social-link {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
      }
    }
  }

  .contact-form-container {
    padding: 25px 20px;

    h3 {
      font-size: 1.6rem;
      margin-bottom: 25px;
    }

    .contact-form {
      .form-group {
        margin-bottom: 18px;

        label {
          font-size: 0.95rem;
          margin-bottom: 6px;
        }

        .form-control {
          padding: 10px 12px;
          font-size: 0.95rem;
        }

        .error-message {
          font-size: 0.85rem;
        }
      }

      .btn {
        padding: 10px 20px;
        font-size: 0.95rem;
        width: 100%;
        justify-content: center;
      }
    }
  }

  .success-message {
    padding: 12px;
    margin-bottom: 15px;

    i {
      font-size: 1.3rem;
    }

    span {
      font-size: 0.95rem;
    }
  }
}

@media (max-width: 480px) {
  .contact-container {
    padding: 60px 10px;
  }

  .section-header {
    .section-title {
      font-size: 2rem;
    }

    .section-description {
      font-size: 0.95rem;
    }
  }

  .contact-info {
    padding: 20px 15px;

    h3 {
      font-size: 1.5rem;
    }

    .contact-item {
      margin-bottom: 18px;

      i {
        font-size: 1.2rem;
        margin-right: 12px;
      }

      .contact-details {
        .contact-value {
          font-size: 0.95rem;
        }
      }
    }

    .social-links {
      gap: 12px;

      .social-link {
        width: 38px;
        height: 38px;
        font-size: 1.1rem;
      }
    }
  }

  .contact-form-container {
    padding: 20px 15px;

    h3 {
      font-size: 1.5rem;
    }

    .contact-form {
      .form-group {
        .form-control {
          padding: 9px 10px;
          font-size: 0.9rem;
        }
      }

      .btn {
        padding: 9px 18px;
        font-size: 0.9rem;
      }
    }
  }
}