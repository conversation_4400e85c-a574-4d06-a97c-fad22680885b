import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PortfolioService, Project } from '../../services/portfolio.service';

@Component({
  selector: 'app-projects',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './projects.component.html',
  styleUrl: './projects.component.scss'
})
export class ProjectsComponent implements OnInit, OnDestroy {
  projects: Project[] = [];
  featuredProjects: Project[] = [];
  allProjects: Project[] = [];
  filteredProjects: Project[] = [];
  isLoading = true;
  hasError = false;
  errorMessage = '';
  showFeaturedOnly = false;

  // Filtering and sorting
  selectedFilter = 'all';
  selectedSort = 'featured';
  availableFilters: { value: string; label: string; count: number }[] = [];
  availableSorts = [
    { value: 'featured', label: 'Featured First' },
    { value: 'stars', label: 'Most Stars' },
    { value: 'updated', label: 'Recently Updated' },
    { value: 'name', label: 'Name A-Z' }
  ];

  // View options
  viewMode: 'grid' | 'masonry' = 'masonry';

  private destroy$ = new Subject<void>();

  constructor(private portfolioService: PortfolioService) {}

  ngOnInit(): void {
    this.loadProjects();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadProjects(): void {
    this.isLoading = true;
    this.hasError = false;

    this.portfolioService.getAllProjects()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projects) => {
          this.allProjects = projects;
          this.featuredProjects = projects.filter(p => p.featured);
          this.setupFilters();
          this.applyFiltersAndSort();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading projects:', error);
          this.hasError = true;
          this.errorMessage = 'Failed to load projects. Please try again later.';
          this.isLoading = false;

          // Fallback to static projects
          const staticProjects = this.portfolioService.getProjects();
          this.allProjects = staticProjects;
          this.featuredProjects = this.portfolioService.getFeaturedProjects();
          this.setupFilters();
          this.applyFiltersAndSort();
        }
      });
  }

  private setupFilters(): void {
    const technologies = new Set<string>();

    this.allProjects.forEach(project => {
      project.technologies.forEach(tech => technologies.add(tech));
      if (project.language) technologies.add(project.language);
    });

    this.availableFilters = [
      { value: 'all', label: 'All Projects', count: this.allProjects.length },
      { value: 'featured', label: 'Featured', count: this.featuredProjects.length },
      ...Array.from(technologies).map(tech => ({
        value: tech.toLowerCase(),
        label: tech,
        count: this.allProjects.filter(p =>
          p.technologies.some(t => t.toLowerCase() === tech.toLowerCase()) ||
          p.language?.toLowerCase() === tech.toLowerCase()
        ).length
      }))
    ].sort((a, b) => {
      if (a.value === 'all') return -1;
      if (b.value === 'all') return 1;
      if (a.value === 'featured') return -1;
      if (b.value === 'featured') return 1;
      return b.count - a.count;
    });
  }

  private applyFiltersAndSort(): void {
    let filtered = [...this.allProjects];

    // Apply filter
    if (this.selectedFilter === 'featured') {
      filtered = filtered.filter(p => p.featured);
    } else if (this.selectedFilter !== 'all') {
      filtered = filtered.filter(p =>
        p.technologies.some(t => t.toLowerCase() === this.selectedFilter) ||
        p.language?.toLowerCase() === this.selectedFilter
      );
    }

    // Apply sort
    switch (this.selectedSort) {
      case 'featured':
        filtered.sort((a, b) => {
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          if (a.stars && b.stars) return b.stars - a.stars;
          return 0;
        });
        break;
      case 'stars':
        filtered.sort((a, b) => (b.stars || 0) - (a.stars || 0));
        break;
      case 'updated':
        filtered.sort((a, b) => {
          if (!a.lastUpdated && !b.lastUpdated) return 0;
          if (!a.lastUpdated) return 1;
          if (!b.lastUpdated) return -1;
          return b.lastUpdated.getTime() - a.lastUpdated.getTime();
        });
        break;
      case 'name':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
    }

    this.filteredProjects = filtered;
    this.projects = this.showFeaturedOnly ? this.featuredProjects : filtered;
  }

  toggleFeaturedProjects(): void {
    this.showFeaturedOnly = !this.showFeaturedOnly;
    this.projects = this.showFeaturedOnly ? this.featuredProjects : this.filteredProjects;
  }

  onFilterChange(filter: string): void {
    this.selectedFilter = filter;
    this.applyFiltersAndSort();
  }

  onSortChange(sort: string): void {
    this.selectedSort = sort;
    this.applyFiltersAndSort();
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'grid' ? 'masonry' : 'grid';
  }

  retryLoading(): void {
    this.loadProjects();
  }

  getProjectImageAlt(project: Project): string {
    return `${project.title} - ${project.language || 'Project'} screenshot`;
  }

  formatLastUpdated(date: Date | undefined): string {
    if (!date) return '';

    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 30) return `${diffDays} days ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  }

  hasGitHubProjects(): boolean {
    return this.allProjects.some(p => p.isFromGitHub === true);
  }

  getGitHubProjectsCount(): number {
    return this.allProjects.filter(p => p.isFromGitHub === true).length;
  }

  getSelectedFilterLabel(): string {
    const filter = this.availableFilters.find(f => f.value === this.selectedFilter);
    return filter?.label || this.selectedFilter;
  }
}
