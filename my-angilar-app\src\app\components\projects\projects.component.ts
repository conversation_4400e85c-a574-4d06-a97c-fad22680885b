import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PortfolioService, Project } from '../../services/portfolio.service';

@Component({
  selector: 'app-projects',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './projects.component.html',
  styleUrl: './projects.component.scss'
})
export class ProjectsComponent implements OnInit, OnDestroy {
  projects: Project[] = [];
  featuredProjects: Project[] = [];
  allProjects: Project[] = [];
  isLoading = true;
  hasError = false;
  errorMessage = '';
  showFeaturedOnly = false;

  private destroy$ = new Subject<void>();

  constructor(private portfolioService: PortfolioService) {}

  ngOnInit(): void {
    this.loadProjects();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadProjects(): void {
    this.isLoading = true;
    this.hasError = false;

    this.portfolioService.getAllProjects()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projects) => {
          this.allProjects = projects;
          this.featuredProjects = projects.filter(p => p.featured);
          this.projects = this.showFeaturedOnly ? this.featuredProjects : projects;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading projects:', error);
          this.hasError = true;
          this.errorMessage = 'Failed to load projects. Please try again later.';
          this.isLoading = false;

          // Fallback to static projects
          this.projects = this.portfolioService.getProjects();
          this.featuredProjects = this.portfolioService.getFeaturedProjects();
        }
      });
  }

  toggleFeaturedProjects(): void {
    this.showFeaturedOnly = !this.showFeaturedOnly;
    this.projects = this.showFeaturedOnly ? this.featuredProjects : this.allProjects;
  }

  retryLoading(): void {
    this.loadProjects();
  }

  getProjectImageAlt(project: Project): string {
    return `${project.title} - ${project.language || 'Project'} screenshot`;
  }

  formatLastUpdated(date: Date | undefined): string {
    if (!date) return '';

    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 30) return `${diffDays} days ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  }
}
