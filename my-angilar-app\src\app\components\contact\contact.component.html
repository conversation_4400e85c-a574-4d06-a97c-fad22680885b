<div class="contact-container">
  <div class="section-header">
    <h2 class="section-title">Get In Touch</h2>
    <div class="section-divider"></div>
    <p class="section-description">Let's discuss your next project or opportunity</p>
  </div>

  <div class="contact-content">
    <div class="contact-info">
      <h3>Contact Information</h3>

      <div class="contact-item">
        <i class="fas fa-envelope"></i>
        <div class="contact-details">
          <span class="contact-label">Email</span>
          <a [href]="'mailto:' + personalInfo.email" class="contact-value">{{ personalInfo.email }}</a>
        </div>
      </div>

      <div class="contact-item">
        <i class="fas fa-phone"></i>
        <div class="contact-details">
          <span class="contact-label">Phone</span>
          <a [href]="'tel:' + personalInfo.phone" class="contact-value">{{ personalInfo.phone }}</a>
        </div>
      </div>

      <div class="contact-item">
        <i class="fas fa-map-marker-alt"></i>
        <div class="contact-details">
          <span class="contact-label">Location</span>
          <span class="contact-value">{{ personalInfo.location }}</span>
        </div>
      </div>

      <div class="social-links">
        <a [href]="personalInfo.github" target="_blank" class="social-link">
          <i class="fab fa-github"></i>
        </a>
        <a [href]="personalInfo.linkedin" target="_blank" class="social-link">
          <i class="fab fa-linkedin"></i>
        </a>
      </div>
    </div>

    <div class="contact-form-container">
      <h3>Send Message</h3>

      <div class="success-message" *ngIf="isSuccess">
        <i class="fas fa-check-circle"></i>
        <span>Thank you! Your message has been sent successfully.</span>
      </div>

      <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="contact-form">
        <div class="form-group">
          <label for="name">Name *</label>
          <input
            type="text"
            id="name"
            formControlName="name"
            class="form-control"
            [class.error]="isSubmitted && contactForm.get('name')?.invalid"
          >
          <div class="error-message" *ngIf="isSubmitted && contactForm.get('name')?.invalid">
            Name is required (minimum 2 characters)
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email *</label>
          <input
            type="email"
            id="email"
            formControlName="email"
            class="form-control"
            [class.error]="isSubmitted && contactForm.get('email')?.invalid"
          >
          <div class="error-message" *ngIf="isSubmitted && contactForm.get('email')?.invalid">
            Please enter a valid email address
          </div>
        </div>

        <div class="form-group">
          <label for="subject">Subject *</label>
          <input
            type="text"
            id="subject"
            formControlName="subject"
            class="form-control"
            [class.error]="isSubmitted && contactForm.get('subject')?.invalid"
          >
          <div class="error-message" *ngIf="isSubmitted && contactForm.get('subject')?.invalid">
            Subject is required
          </div>
        </div>

        <div class="form-group">
          <label for="message">Message *</label>
          <textarea
            id="message"
            formControlName="message"
            rows="5"
            class="form-control"
            [class.error]="isSubmitted && contactForm.get('message')?.invalid"
          ></textarea>
          <div class="error-message" *ngIf="isSubmitted && contactForm.get('message')?.invalid">
            Message is required (minimum 10 characters)
          </div>
        </div>

        <button type="submit" class="btn btn-primary">
          <i class="fas fa-paper-plane"></i>
          Send Message
        </button>
      </form>
    </div>
  </div>
</div>
