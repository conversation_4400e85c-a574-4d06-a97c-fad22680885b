import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PortfolioService, PersonalInfo, SocialLink } from '../../services/portfolio.service';
import { BackgroundAnimationComponent } from '../background-animation/background-animation.component';

@Component({
  selector: 'app-hero',
  standalone: true,
  imports: [CommonModule, BackgroundAnimationComponent],
  templateUrl: './hero.component.html',
  styleUrl: './hero.component.scss'
})
export class HeroComponent implements OnInit {
  personalInfo: PersonalInfo;
  socialLinks: SocialLink[];
  displayText = '';
  fullText = '';
  currentIndex = 0;
  isTyping = true;

  constructor(private portfolioService: PortfolioService) {
    this.personalInfo = this.portfolioService.getPersonalInfo();
    this.socialLinks = this.portfolioService.getSocialLinks();
  }

  ngOnInit() {
    this.fullText = this.personalInfo.title;
    this.typeWriter();
  }

  typeWriter() {
    if (this.currentIndex < this.fullText.length) {
      this.displayText += this.fullText.charAt(this.currentIndex);
      this.currentIndex++;
      setTimeout(() => this.typeWriter(), 100);
    } else {
      this.isTyping = false;
    }
  }

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  downloadCV() {
    // Placeholder for CV download functionality
    console.log('Download CV clicked');
  }
}
