@import '../../../app/styles/shared';

.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: -1;
}

.particles-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  will-change: transform;
}

// Geometric shapes animation
.geometric-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  .shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba($primary-color, 0.1), rgba(#764ba2, 0.05));
    will-change: transform;

    &.shape-1 {
      width: 100px;
      height: 100px;
      top: 10%;
      left: 10%;
      animation: float-1 20s ease-in-out infinite;
    }

    &.shape-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 15%;
      border-radius: 0;
      transform: rotate(45deg);
      animation: float-2 25s ease-in-out infinite;
    }

    &.shape-3 {
      width: 80px;
      height: 80px;
      top: 30%;
      right: 30%;
      clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
      animation: float-3 18s ease-in-out infinite;
    }

    &.shape-4 {
      width: 120px;
      height: 120px;
      bottom: 20%;
      left: 20%;
      clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
      animation: float-4 22s ease-in-out infinite;
    }

    &.shape-5 {
      width: 60px;
      height: 60px;
      top: 80%;
      left: 60%;
      animation: float-5 16s ease-in-out infinite;
    }

    &.shape-6 {
      width: 200px;
      height: 200px;
      top: 5%;
      right: 5%;
      border-radius: 0;
      background: linear-gradient(135deg, rgba(#667eea, 0.08), rgba(#764ba2, 0.03));
      animation: float-6 30s ease-in-out infinite;
    }
  }
}

// Gradient overlay animation
.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba($primary-color, 0.05) 0%,
    rgba(#667eea, 0.03) 25%,
    rgba(#764ba2, 0.05) 50%,
    rgba($primary-color, 0.02) 75%,
    rgba(#667eea, 0.04) 100%
  );
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
  will-change: background-position;
}

// Floating elements
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  .floating-element {
    position: absolute;
    will-change: transform;

    &.floating-element-1 {
      top: 15%;
      right: 20%;
      animation: gentle-float-1 12s ease-in-out infinite;
    }

    &.floating-element-2 {
      bottom: 25%;
      left: 15%;
      animation: gentle-float-2 15s ease-in-out infinite;
    }

    &.floating-element-3 {
      top: 50%;
      right: 10%;
      animation: gentle-float-3 18s ease-in-out infinite;
    }

    svg {
      filter: drop-shadow(0 0 10px rgba($primary-color, 0.2));
    }
  }
}

// Keyframe animations
@keyframes float-1 {
  0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  25% { transform: translateY(-20px) translateX(10px) rotate(90deg); }
  50% { transform: translateY(-10px) translateX(-15px) rotate(180deg); }
  75% { transform: translateY(-30px) translateX(5px) rotate(270deg); }
}

@keyframes float-2 {
  0%, 100% { transform: translateY(0px) translateX(0px) rotate(45deg); }
  33% { transform: translateY(-25px) translateX(-10px) rotate(135deg); }
  66% { transform: translateY(-15px) translateX(20px) rotate(225deg); }
}

@keyframes float-3 {
  0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
  50% { transform: translateY(-35px) translateX(-20px) scale(1.1); }
}

@keyframes float-4 {
  0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  25% { transform: translateY(15px) translateX(-25px) rotate(60deg); }
  50% { transform: translateY(-20px) translateX(10px) rotate(120deg); }
  75% { transform: translateY(10px) translateX(15px) rotate(180deg); }
}

@keyframes float-5 {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-40px) scale(1.2); }
}

@keyframes float-6 {
  0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  50% { transform: translateY(-10px) translateX(-10px) rotate(180deg); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes gentle-float-1 {
  0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  33% { transform: translateY(-15px) translateX(8px) rotate(120deg); }
  66% { transform: translateY(-8px) translateX(-12px) rotate(240deg); }
}

@keyframes gentle-float-2 {
  0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
  50% { transform: translateY(-20px) translateX(15px) scale(1.05); }
}

@keyframes gentle-float-3 {
  0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
  25% { transform: translateY(-10px) translateX(-8px) rotate(90deg); }
  50% { transform: translateY(-18px) translateX(5px) rotate(180deg); }
  75% { transform: translateY(-5px) translateX(10px) rotate(270deg); }
}

// Parallax effect
.animation-particles {
  transform: translateZ(0);
  backface-visibility: hidden;
}

// Performance optimizations
.background-animation * {
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

// Accessibility: Respect reduced motion preference
@media (prefers-reduced-motion: reduce) {
  .background-animation {
    display: none !important;
  }

  .geometric-shapes .shape,
  .floating-elements .floating-element,
  .gradient-overlay {
    animation: none !important;
    transform: none !important;
  }
}

// Mobile optimizations
@media (max-width: 768px) {
  .geometric-shapes .shape {
    &.shape-2,
    &.shape-6 {
      display: none; // Hide larger shapes on mobile for performance
    }
  }

  .floating-elements .floating-element {
    &.floating-element-2 {
      display: none; // Reduce elements on mobile
    }
  }
}