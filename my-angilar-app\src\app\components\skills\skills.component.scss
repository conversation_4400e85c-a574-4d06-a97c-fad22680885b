// Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$gray-color: #666;
$light-gray: #f8f9fa;
$transition-speed: 0.3s;

.skills-container {
  padding: 100px 20px;
  background-color: $light-color;
  min-height: 100vh;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    color: $secondary-color;
    margin-bottom: 20px;
    position: relative;
  }

  .section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, $primary-color, #764ba2);
    margin: 0 auto;
    border-radius: 2px;
  }
}

.skills-content {
  max-width: 1200px;
  margin: 0 auto;
}

.skills-categories {
  margin-bottom: 60px;

  .category-title {
    font-size: 2rem;
    color: $secondary-color;
    margin-bottom: 30px;
    font-weight: 600;
    position: relative;
    padding-left: 20px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 30px;
      background: $primary-color;
      border-radius: 4px;
    }
  }
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 30px;
}

.skill-item {
  background: $light-gray;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all $transition-speed ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  .skill-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    .skill-name {
      font-weight: 600;
      color: $secondary-color;
    }

    .skill-percentage {
      color: $primary-color;
      font-weight: 600;
    }
  }

  .skill-bar {
    height: 10px;
    background: rgba($gray-color, 0.2);
    border-radius: 5px;
    overflow: hidden;

    .skill-progress {
      height: 100%;
      background: linear-gradient(90deg, $primary-color, #764ba2);
      border-radius: 5px;
      transition: width 1s ease-in-out;
    }
  }
}

// Media Queries
@media (max-width: 768px) {
  .skills-container {
    padding: 80px 15px;
  }

  .section-header {
    margin-bottom: 60px;

    .section-title {
      font-size: 2.5rem;
      line-height: 1.2;
    }
  }

  .skills-categories {
    margin-bottom: 40px;

    .category-title {
      font-size: 1.8rem;
      margin-bottom: 25px;
      padding-left: 15px;

      &::before {
        width: 6px;
        height: 25px;
      }
    }
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .skill-item {
    padding: 18px;

    .skill-info {
      margin-bottom: 8px;

      .skill-name {
        font-size: 0.95rem;
      }

      .skill-percentage {
        font-size: 0.9rem;
      }
    }

    .skill-bar {
      height: 8px;
    }
  }
}

@media (max-width: 480px) {
  .skills-container {
    padding: 60px 10px;
  }

  .section-header .section-title {
    font-size: 2rem;
  }

  .skills-categories .category-title {
    font-size: 1.6rem;
    padding-left: 12px;
  }

  .skill-item {
    padding: 15px;

    .skill-info {
      .skill-name {
        font-size: 0.9rem;
      }

      .skill-percentage {
        font-size: 0.85rem;
      }
    }

    .skill-bar {
      height: 6px;
    }
  }
}