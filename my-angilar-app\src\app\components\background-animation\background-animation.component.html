<div class="background-animation" [class]="'animation-' + animationType">
  <!-- Canvas for particle animations -->
  <canvas
    class="particles-canvas"
    [style.display]="prefersReducedMotion ? 'none' : 'block'">
  </canvas>

  <!-- CSS-based geometric shapes -->
  <div class="geometric-shapes" *ngIf="animationType === 'geometric' && !prefersReducedMotion">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
    <div class="shape shape-4"></div>
    <div class="shape shape-5"></div>
    <div class="shape shape-6"></div>
  </div>

  <!-- Animated gradient overlay -->
  <div class="gradient-overlay" *ngIf="animationType === 'gradient' && !prefersReducedMotion"></div>

  <!-- Floating elements -->
  <div class="floating-elements" *ngIf="!prefersReducedMotion">
    <div class="floating-element floating-element-1">
      <svg width="60" height="60" viewBox="0 0 60 60">
        <circle cx="30" cy="30" r="25" fill="none" stroke="url(#gradient1)" stroke-width="2" opacity="0.3"/>
      </svg>
    </div>
    <div class="floating-element floating-element-2">
      <svg width="80" height="80" viewBox="0 0 80 80">
        <polygon points="40,10 70,60 10,60" fill="none" stroke="url(#gradient2)" stroke-width="2" opacity="0.2"/>
      </svg>
    </div>
    <div class="floating-element floating-element-3">
      <svg width="50" height="50" viewBox="0 0 50 50">
        <rect x="10" y="10" width="30" height="30" fill="none" stroke="url(#gradient3)" stroke-width="2" opacity="0.25"/>
      </svg>
    </div>
  </div>

  <!-- SVG Gradients -->
  <svg width="0" height="0" style="position: absolute;">
    <defs>
      <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#4a6cf7;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
      </linearGradient>
      <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
      </linearGradient>
      <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#4a6cf7;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
      </linearGradient>
    </defs>
  </svg>
</div>
