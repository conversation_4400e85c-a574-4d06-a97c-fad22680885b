import {
  FetchBackend,
  HTTP_INTERCEPTORS,
  HTTP_ROOT_INTERCEPTOR_FNS,
  HTTP_TRANSFER_CACHE_ORIGIN_MAP,
  HttpBackend,
  HttpClient,
  HttpClientJsonpModule,
  HttpClientModule,
  HttpClientXsrfModule,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpEventType,
  HttpFeatureKind,
  HttpHandler,
  HttpHeaderResponse,
  HttpHeaders,
  HttpInterceptorHandler,
  HttpParams,
  HttpRequest,
  HttpResponse,
  HttpResponseBase,
  HttpStatusCode,
  HttpUrlEncodingCodec,
  HttpXhrBackend,
  HttpXsrfTokenExtractor,
  JsonpClientBackend,
  JsonpInterceptor,
  REQUESTS_CONTRIBUTE_TO_STABILITY,
  httpResource,
  provideHttpClient,
  withFet<PERSON>,
  withHttpTransferCache,
  withInterceptors,
  withInterceptorsFromDi,
  withJsonpSupport,
  withNoXsrfProtection,
  withRequestsMadeViaParent,
  withXsrfConfiguration
} from "./chunk-VKVFCHZJ.js";
import "./chunk-PHO26LGY.js";
import "./chunk-6SZJNWHU.js";
import "./chunk-FFZIAYYX.js";
import "./chunk-6Q4RANH6.js";
import "./chunk-CXCX2JKZ.js";
export {
  FetchBackend,
  HTTP_INTERCEPTORS,
  HTTP_TRANSFER_CACHE_ORIGIN_MAP,
  HttpBackend,
  HttpClient,
  HttpClientJsonpModule,
  HttpClientModule,
  HttpClientXsrfModule,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpEventType,
  HttpFeatureKind,
  HttpHandler,
  HttpHeaderResponse,
  HttpHeaders,
  HttpParams,
  HttpRequest,
  HttpResponse,
  HttpResponseBase,
  HttpStatusCode,
  HttpUrlEncodingCodec,
  HttpXhrBackend,
  HttpXsrfTokenExtractor,
  JsonpClientBackend,
  JsonpInterceptor,
  httpResource,
  provideHttpClient,
  withFetch,
  withInterceptors,
  withInterceptorsFromDi,
  withJsonpSupport,
  withNoXsrfProtection,
  withRequestsMadeViaParent,
  withXsrfConfiguration,
  HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS,
  HttpInterceptorHandler as ɵHttpInterceptingHandler,
  HttpInterceptorHandler as ɵHttpInterceptorHandler,
  REQUESTS_CONTRIBUTE_TO_STABILITY as ɵREQUESTS_CONTRIBUTE_TO_STABILITY,
  withHttpTransferCache as ɵwithHttpTransferCache
};
