<div class="projects-container">
  <div class="section-header">
    <h2 class="section-title">My Projects</h2>
    <div class="section-divider"></div>
    <p class="section-description">Here are some of my recent projects that showcase my skills and experience</p>

    <!-- Enhanced Controls -->
    <div class="projects-controls">
      <!-- Filter Controls -->
      <div class="filter-controls">
        <div class="filter-group">
          <label class="filter-label">Filter by:</label>
          <select
            class="filter-select"
            [(ngModel)]="selectedFilter"
            (change)="onFilterChange(selectedFilter)"
            [disabled]="isLoading">
            <option *ngFor="let filter of availableFilters" [value]="filter.value">
              {{ filter.label }} ({{ filter.count }})
            </option>
          </select>
        </div>

        <div class="filter-group">
          <label class="filter-label">Sort by:</label>
          <select
            class="filter-select"
            [(ngModel)]="selectedSort"
            (change)="onSortChange(selectedSort)"
            [disabled]="isLoading">
            <option *ngFor="let sort of availableSorts" [value]="sort.value">
              {{ sort.label }}
            </option>
          </select>
        </div>
      </div>

      <!-- View Controls -->
      <div class="view-controls">
        <button
          class="view-toggle-btn"
          (click)="toggleViewMode()"
          [disabled]="isLoading"
          [title]="viewMode === 'grid' ? 'Switch to Masonry View' : 'Switch to Grid View'">
          <i class="fas" [class.fa-th]="viewMode === 'masonry'" [class.fa-th-large]="viewMode === 'grid'"></i>
        </button>

        <button
          class="featured-toggle-btn"
          [class.active]="showFeaturedOnly"
          (click)="toggleFeaturedProjects()"
          [disabled]="isLoading">
          <i class="fas fa-star"></i>
          {{ showFeaturedOnly ? 'Show All' : 'Featured Only' }}
        </button>
      </div>
    </div>

    <!-- Project Statistics -->
    <div class="project-stats" *ngIf="!isLoading && !hasError">
      <div class="stat-item">
        <span class="stat-number">{{ projects.length }}</span>
        <span class="stat-label">{{ projects.length === 1 ? 'Project' : 'Projects' }}</span>
      </div>
      <div class="stat-item" *ngIf="featuredProjects.length > 0">
        <span class="stat-number">{{ featuredProjects.length }}</span>
        <span class="stat-label">Featured</span>
      </div>
      <div class="stat-item" *ngIf="hasGitHubProjects()">
        <span class="stat-number">{{ getGitHubProjectsCount() }}</span>
        <span class="stat-label">From GitHub</span>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-skeleton">
      <div class="skeleton-card" *ngFor="let item of [1,2,3,4,5,6]">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-description"></div>
          <div class="skeleton-tags">
            <div class="skeleton-tag" *ngFor="let tag of [1,2,3]"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="hasError && !isLoading">
    <div class="error-content">
      <i class="fas fa-exclamation-triangle"></i>
      <h3>Oops! Something went wrong</h3>
      <p>{{ errorMessage }}</p>
      <button class="btn btn-primary" (click)="retryLoading()">
        <i class="fas fa-redo"></i> Try Again
      </button>
    </div>
  </div>

  <!-- Projects Content -->
  <div class="projects-content" *ngIf="!isLoading && !hasError">
    <!-- Featured Projects Showcase -->
    <div class="featured-showcase" *ngIf="featuredProjects.length > 0 && !showFeaturedOnly && selectedFilter === 'all'">
      <h3 class="showcase-title">
        <i class="fas fa-star"></i>
        Featured Projects
      </h3>
      <div class="featured-grid">
        <div
          class="featured-project-card"
          *ngFor="let project of featuredProjects.slice(0, 2); let i = index"
          [style.animation-delay]="(i * 0.2) + 's'">

          <div class="featured-project-image">
            <img
              [src]="project.imageUrl || 'assets/images/project-placeholder.jpg'"
              [alt]="getProjectImageAlt(project)"
              class="project-img"
              loading="lazy">
            <div class="featured-overlay">
              <div class="featured-info">
                <h4>{{ project.title }}</h4>
                <p>{{ project.description }}</p>
                <div class="featured-actions">
                  <a [href]="project.githubUrl" target="_blank" class="featured-link" *ngIf="project.githubUrl">
                    <i class="fab fa-github"></i>
                  </a>
                  <a [href]="project.liveUrl" target="_blank" class="featured-link" *ngIf="project.liveUrl">
                    <i class="fas fa-external-link-alt"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Projects Grid -->
    <div class="projects-grid" [class]="'view-' + viewMode">
      <div
        class="project-card"
        *ngFor="let project of projects; let i = index"
        [class.featured]="project.featured"
        [class.github-project]="project.isFromGitHub"
        [style.animation-delay]="(i * 0.1) + 's'">

        <div class="project-image">
          <img
            [src]="project.imageUrl || 'assets/images/project-placeholder.jpg'"
            [alt]="getProjectImageAlt(project)"
            class="project-img"
            loading="lazy">
          <div class="project-overlay">
            <div class="project-links">
              <a [href]="project.githubUrl" target="_blank" class="project-link" *ngIf="project.githubUrl">
                <i class="fab fa-github"></i>
              </a>
              <a [href]="project.liveUrl" target="_blank" class="project-link" *ngIf="project.liveUrl">
                <i class="fas fa-external-link-alt"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="project-content">
          <div class="project-header">
            <h3 class="project-title">{{ project.title }}</h3>
            <div class="project-badges">
              <span class="featured-badge" *ngIf="project.featured">
                <i class="fas fa-star"></i>
                Featured
              </span>
              <span class="github-badge" *ngIf="project.isFromGitHub">
                <i class="fab fa-github"></i>
                GitHub
              </span>
            </div>
          </div>

          <p class="project-description">{{ project.description }}</p>

          <!-- Enhanced Project Stats -->
          <div class="project-stats" *ngIf="project.stars !== undefined || project.lastUpdated || project.language">
            <div class="stat-item" *ngIf="project.stars !== undefined">
              <i class="fas fa-star"></i>
              <span>{{ project.stars }}</span>
              <small>stars</small>
            </div>
            <div class="stat-item" *ngIf="project.language">
              <i class="fas fa-code"></i>
              <span>{{ project.language }}</span>
            </div>
            <div class="stat-item" *ngIf="project.lastUpdated">
              <i class="fas fa-clock"></i>
              <span>{{ formatLastUpdated(project.lastUpdated) }}</span>
            </div>
          </div>

          <div class="project-technologies">
            <span class="tech-tag" *ngFor="let tech of project.technologies.slice(0, 4)">{{ tech }}</span>
            <span class="tech-tag more" *ngIf="project.technologies.length > 4">
              +{{ project.technologies.length - 4 }}
            </span>
          </div>

          <div class="project-actions">
            <a [href]="project.githubUrl" target="_blank" class="btn btn-primary" *ngIf="project.githubUrl">
              <i class="fab fa-github"></i>
              <span>Code</span>
            </a>
            <a [href]="project.liveUrl" target="_blank" class="btn btn-secondary" *ngIf="project.liveUrl">
              <i class="fas fa-external-link-alt"></i>
              <span>Demo</span>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="projects.length === 0">
      <i class="fas fa-search"></i>
      <h3>No projects found</h3>
      <p *ngIf="selectedFilter !== 'all'">
        No projects found for "{{ getSelectedFilterLabel() }}".
        <button class="link-btn" (click)="onFilterChange('all')">Show all projects</button>
      </p>
      <p *ngIf="selectedFilter === 'all' && showFeaturedOnly">
        No featured projects available.
        <button class="link-btn" (click)="toggleFeaturedProjects()">Show all projects</button>
      </p>
      <p *ngIf="selectedFilter === 'all' && !showFeaturedOnly">
        No projects available at the moment.
      </p>
    </div>
  </div>
</div>
