<div class="projects-container">
  <div class="section-header">
    <h2 class="section-title">My Projects</h2>
    <div class="section-divider"></div>
    <p class="section-description">Here are some of my recent projects that showcase my skills and experience</p>

    <!-- Featured Toggle -->
    <div class="projects-controls">
      <button
        class="toggle-btn"
        [class.active]="showFeaturedOnly"
        (click)="toggleFeaturedProjects()"
        [disabled]="isLoading">
        <i class="fas fa-star"></i>
        {{ showFeaturedOnly ? 'Show All Projects' : 'Show Featured Only' }}
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-skeleton">
      <div class="skeleton-card" *ngFor="let item of [1,2,3,4,5,6]">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-description"></div>
          <div class="skeleton-tags">
            <div class="skeleton-tag" *ngFor="let tag of [1,2,3]"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="hasError && !isLoading">
    <div class="error-content">
      <i class="fas fa-exclamation-triangle"></i>
      <h3>Oops! Something went wrong</h3>
      <p>{{ errorMessage }}</p>
      <button class="btn btn-primary" (click)="retryLoading()">
        <i class="fas fa-redo"></i> Try Again
      </button>
    </div>
  </div>

  <!-- Projects Content -->
  <div class="projects-content" *ngIf="!isLoading && !hasError">
    <div class="projects-grid">
      <div
        class="project-card"
        *ngFor="let project of projects; let i = index"
        [class.featured]="project.featured"
        [style.animation-delay]="(i * 0.1) + 's'">

        <div class="project-image">
          <img
            [src]="project.imageUrl || 'assets/images/project-placeholder.jpg'"
            [alt]="getProjectImageAlt(project)"
            class="project-img"
            loading="lazy">
          <div class="project-overlay">
            <div class="project-links">
              <a [href]="project.githubUrl" target="_blank" class="project-link" *ngIf="project.githubUrl">
                <i class="fab fa-github"></i>
              </a>
              <a [href]="project.liveUrl" target="_blank" class="project-link" *ngIf="project.liveUrl">
                <i class="fas fa-external-link-alt"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="project-content">
          <div class="project-header">
            <h3 class="project-title">{{ project.title }}</h3>
            <div class="project-badges">
              <span class="featured-badge" *ngIf="project.featured">Featured</span>
              <span class="github-badge" *ngIf="project.isFromGitHub">
                <i class="fab fa-github"></i>
              </span>
            </div>
          </div>

          <p class="project-description">{{ project.description }}</p>

          <!-- Project Stats -->
          <div class="project-stats" *ngIf="project.stars !== undefined || project.lastUpdated">
            <div class="stat-item" *ngIf="project.stars !== undefined">
              <i class="fas fa-star"></i>
              <span>{{ project.stars }}</span>
            </div>
            <div class="stat-item" *ngIf="project.language">
              <i class="fas fa-code"></i>
              <span>{{ project.language }}</span>
            </div>
            <div class="stat-item" *ngIf="project.lastUpdated">
              <i class="fas fa-clock"></i>
              <span>{{ formatLastUpdated(project.lastUpdated) }}</span>
            </div>
          </div>

          <div class="project-technologies">
            <span class="tech-tag" *ngFor="let tech of project.technologies">{{ tech }}</span>
          </div>

          <div class="project-actions">
            <a [href]="project.githubUrl" target="_blank" class="btn btn-primary" *ngIf="project.githubUrl">
              <i class="fab fa-github"></i> View Code
            </a>
            <a [href]="project.liveUrl" target="_blank" class="btn btn-secondary" *ngIf="project.liveUrl">
              <i class="fas fa-external-link-alt"></i> Live Demo
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="projects.length === 0">
      <i class="fas fa-folder-open"></i>
      <h3>No projects found</h3>
      <p>{{ showFeaturedOnly ? 'No featured projects available.' : 'No projects available at the moment.' }}</p>
    </div>
  </div>
</div>
