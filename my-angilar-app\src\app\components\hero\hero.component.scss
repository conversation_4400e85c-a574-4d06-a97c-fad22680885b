// Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$transition-speed: 0.3s;

.hero-container {
  min-height: 100vh;
  background: $gradient-bg;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 100px 20px 50px;
  color: $light-color;
  overflow: hidden;
  isolation: isolate; // Create stacking context for background animation
}

.hero-content {
  max-width: 1200px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text {
  .greeting {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 20px;
    opacity: 0;
    animation: fadeInUp 1s ease forwards;

    .name {
      color: #ffd700;
      font-weight: 700;
    }
  }

  .title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 30px;
    min-height: 80px;
    opacity: 0;
    animation: fadeInUp 1s ease 0.3s forwards;

    .typed-text {
      color: $light-color;
    }

    .cursor {
      color: #ffd700;
      animation: blink 1s infinite;

      &.typing {
        animation: none;
      }
    }
  }

  .description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 0.9;
    opacity: 0;
    animation: fadeInUp 1s ease 0.6s forwards;
  }
}

.hero-buttons {
  display: flex;
  gap: 20px;
  margin-bottom: 40px;
  opacity: 0;
  animation: fadeInUp 1s ease 0.9s forwards;

  .btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all $transition-speed ease;
    text-decoration: none;
    display: inline-block;

    &.btn-primary {
      background-color: #ffd700;
      color: $dark-color;

      &:hover {
        background-color: darken(#ffd700, 10%);
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(255, 215, 0, 0.3);
      }
    }

    &.btn-secondary {
      background-color: transparent;
      color: $light-color;
      border: 2px solid $light-color;

      &:hover {
        background-color: $light-color;
        color: $dark-color;
        transform: translateY(-2px);
      }
    }
  }
}

.social-links {
  display: flex;
  gap: 20px;
  opacity: 0;
  animation: fadeInUp 1s ease 1.2s forwards;

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: rgba($light-color, 0.1);
    border-radius: 50%;
    color: $light-color;
    font-size: 1.5rem;
    transition: all $transition-speed ease;
    text-decoration: none;

    &:hover {
      background-color: #ffd700;
      color: $dark-color;
      transform: translateY(-3px);
    }
  }
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: fadeInRight 1s ease 0.6s forwards;

  .image-container {
    position: relative;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    overflow: hidden;
    border: 5px solid rgba($light-color, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);

    .profile-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform $transition-speed ease;
    }

    &:hover .profile-image {
      transform: scale(1.1);
    }
  }
}

.scroll-down {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  color: $light-color;
  opacity: 0.8;
  transition: opacity $transition-speed ease;
  opacity: 0;
  animation: fadeIn 1s ease 1.5s forwards;

  &:hover {
    opacity: 1;
  }

  .scroll-text {
    font-size: 0.9rem;
    margin-bottom: 10px;
  }

  i {
    font-size: 1.2rem;
    animation: bounce 2s infinite;
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// Media Queries
@media (max-width: 768px) {
  .hero-container {
    padding: 80px 15px 40px;
    min-height: 100vh;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .hero-text {
    .greeting {
      font-size: 1.8rem;
      line-height: 1.3;
      margin-bottom: 15px;
    }

    .title {
      font-size: 2rem;
      line-height: 1.2;
      min-height: 60px;
      margin-bottom: 25px;
    }

    .description {
      font-size: 1rem;
      line-height: 1.6;
      margin-bottom: 30px;
      padding: 0 10px;
    }
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;

    .btn {
      width: 200px;
      padding: 12px 25px;
      font-size: 0.95rem;
    }
  }

  .social-links {
    gap: 15px;
    justify-content: center;

    .social-link {
      width: 45px;
      height: 45px;
      font-size: 1.3rem;
    }
  }

  .hero-image {
    order: -1;

    .image-container {
      width: 280px;
      height: 280px;
      margin: 0 auto;
    }
  }

  .scroll-down {
    bottom: 20px;

    .scroll-text {
      font-size: 0.8rem;
    }

    i {
      font-size: 1rem;
    }
  }
}

@media (max-width: 480px) {
  .hero-container {
    padding: 70px 10px 30px;
  }

  .hero-text {
    .greeting {
      font-size: 1.6rem;
    }

    .title {
      font-size: 1.8rem;
      min-height: 50px;
    }

    .description {
      font-size: 0.95rem;
      padding: 0 5px;
    }
  }

  .hero-buttons {
    .btn {
      width: 180px;
      padding: 10px 20px;
      font-size: 0.9rem;
    }
  }

  .social-links {
    gap: 12px;

    .social-link {
      width: 40px;
      height: 40px;
      font-size: 1.2rem;
    }
  }

  .hero-image .image-container {
    width: 240px;
    height: 240px;
  }
}