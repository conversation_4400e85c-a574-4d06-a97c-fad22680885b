<div class="skills-container">
  <div class="section-header">
    <h2 class="section-title">Skills & Technologies</h2>
    <div class="section-divider"></div>
  </div>

  <div class="skills-content">
    <div class="skills-categories" *ngFor="let category of skillCategories">
      <h3 class="category-title">{{ category }}</h3>
      <div class="skills-grid">
        <div class="skill-item" *ngFor="let skill of getSkillsByCategory(category)">
          <div class="skill-info">
            <span class="skill-name">{{ skill.name }}</span>
            <span class="skill-percentage">{{ skill.level }}%</span>
          </div>
          <div class="skill-bar">
            <div class="skill-progress" [style.width.%]="skill.level"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
