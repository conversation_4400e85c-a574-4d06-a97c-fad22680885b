<div class="hero-container">
  <!-- Background Animation -->
  <app-background-animation
    [particleCount]="30"
    animationType="particles"
    intensity="medium">
  </app-background-animation>

  <div class="hero-content">
    <div class="hero-text">
      <h1 class="greeting">Hello, I'm <span class="name">{{ personalInfo.name }}</span></h1>
      <h2 class="title">
        <span class="typed-text">{{ displayText }}</span>
        <span class="cursor" [class.typing]="isTyping">|</span>
      </h2>
      <p class="description">{{ personalInfo.bio }}</p>

      <div class="hero-buttons">
        <button class="btn btn-primary" (click)="scrollToSection('contact')">Contact Me</button>
        <button class="btn btn-secondary" (click)="downloadCV()">Download CV</button>
      </div>

      <div class="social-links">
        <a *ngFor="let social of socialLinks.slice(0, 4)"
           [href]="social.url"
           target="_blank"
           class="social-link"
           [attr.aria-label]="social.name"
           [style.--social-color]="social.color">
          <i [class]="social.icon"></i>
        </a>
      </div>
    </div>

    <div class="hero-image">
      <div class="image-container">
        <img src="Polish_20250702_023502090.jpg" alt="{{ personalInfo.name }}" class="profile-image">
      </div>
    </div>
  </div>

  <div class="scroll-down" (click)="scrollToSection('about')">
    <span class="scroll-text">Scroll Down</span>
    <i class="fas fa-chevron-down"></i>
  </div>
</div>
