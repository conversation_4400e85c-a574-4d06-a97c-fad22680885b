// Shared Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$gray-color: #666;
$light-gray: #f8f9fa;
$transition-speed: 0.3s;

// Shared Mixins
@mixin section-container {
  padding: 100px 20px;
  background-color: $light-gray;
  min-height: 100vh;
}

@mixin section-header {
  text-align: center;
  margin-bottom: 60px;
}

@mixin section-title {
  font-size: 3rem;
  font-weight: 700;
  color: $secondary-color;
  margin-bottom: 20px;
  position: relative;
}

@mixin section-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, $primary-color, #764ba2);
  margin: 0 auto 20px;
  border-radius: 2px;
}

@mixin card-style {
  background: $light-color;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all $transition-speed ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}

@mixin button-primary {
  background: linear-gradient(135deg, $primary-color, #764ba2);
  color: $light-color;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all $transition-speed ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba($primary-color, 0.3);
  }
}

// Responsive Breakpoints
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}
