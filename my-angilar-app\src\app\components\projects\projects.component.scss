// Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$gray-color: #666;
$light-gray: #f8f9fa;
$transition-speed: 0.3s;

.projects-container {
  padding: 100px 20px;
  background-color: $light-gray;
  min-height: 100vh;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    color: $secondary-color;
    margin-bottom: 20px;
    position: relative;
  }

  .section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, $primary-color, #764ba2);
    margin: 0 auto 20px;
    border-radius: 2px;
  }

  .section-description {
    max-width: 700px;
    margin: 0 auto 30px;
    color: $gray-color;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.projects-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba($light-color, 0.8);
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);

  .filter-controls {
    display: flex;
    gap: 20px;
    align-items: center;

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: 5px;

      .filter-label {
        font-size: 0.9rem;
        font-weight: 600;
        color: $secondary-color;
      }

      .filter-select {
        padding: 8px 12px;
        border: 2px solid rgba($primary-color, 0.2);
        border-radius: 8px;
        background: $light-color;
        color: $secondary-color;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all $transition-speed ease;
        min-width: 150px;

        &:focus {
          outline: none;
          border-color: $primary-color;
          box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }

  .view-controls {
    display: flex;
    gap: 10px;
    align-items: center;

    .view-toggle-btn {
      background: rgba($primary-color, 0.1);
      color: $primary-color;
      border: 2px solid rgba($primary-color, 0.2);
      padding: 10px;
      border-radius: 8px;
      cursor: pointer;
      transition: all $transition-speed ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;

      &:hover:not(:disabled) {
        background: $primary-color;
        color: $light-color;
        transform: scale(1.05);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      i {
        font-size: 1.1rem;
      }
    }

    .featured-toggle-btn {
      background: rgba($primary-color, 0.1);
      color: $primary-color;
      border: 2px solid rgba($primary-color, 0.2);
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all $transition-speed ease;
      display: flex;
      align-items: center;
      gap: 6px;

      &:hover:not(:disabled) {
        background: $primary-color;
        color: $light-color;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba($primary-color, 0.3);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      &.active {
        background: linear-gradient(90deg, $primary-color, #764ba2);
        color: $light-color;
        border-color: transparent;
      }

      i {
        font-size: 0.9rem;
      }
    }
  }
}

// Project Statistics
.project-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 40px;
  padding: 20px;
  background: linear-gradient(135deg, rgba($primary-color, 0.1), rgba(#764ba2, 0.05));
  border-radius: 15px;

  .stat-item {
    text-align: center;

    .stat-number {
      display: block;
      font-size: 2rem;
      font-weight: 700;
      color: $primary-color;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.9rem;
      color: $gray-color;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

// Loading States
.loading-container {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
}

.skeleton-card {
  background: $light-color;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s ease-in-out infinite alternate;

  .skeleton-image {
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .skeleton-content {
    padding: 25px;

    .skeleton-title {
      height: 24px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
      border-radius: 4px;
      margin-bottom: 15px;
    }

    .skeleton-description {
      height: 60px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .skeleton-tags {
      display: flex;
      gap: 10px;

      .skeleton-tag {
        height: 24px;
        width: 60px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
        border-radius: 12px;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

// Error States
.error-container {
  max-width: 600px;
  margin: 60px auto;
  text-align: center;
}

.error-content {
  background: $light-color;
  padding: 60px 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  i {
    font-size: 4rem;
    color: #ff6b6b;
    margin-bottom: 20px;
  }

  h3 {
    font-size: 1.8rem;
    color: $secondary-color;
    margin-bottom: 15px;
    font-weight: 600;
  }

  p {
    color: $gray-color;
    font-size: 1.1rem;
    margin-bottom: 30px;
    line-height: 1.6;
  }

  .btn {
    padding: 12px 24px;
    font-size: 1rem;
  }
}

// Featured Projects Showcase
.featured-showcase {
  margin-bottom: 60px;

  .showcase-title {
    font-size: 1.8rem;
    color: $secondary-color;
    margin-bottom: 30px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    i {
      color: $primary-color;
      font-size: 1.6rem;
    }
  }

  .featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 30px;
  }

  .featured-project-card {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    transition: all $transition-speed ease;
    animation: fadeInUp 0.8s ease forwards;
    opacity: 0;

    &:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25);

      .featured-overlay {
        opacity: 1;
      }

      .project-img {
        transform: scale(1.1);
      }
    }

    .featured-project-image {
      position: relative;
      height: 300px;
      overflow: hidden;

      .project-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform $transition-speed ease;
      }

      .featured-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba($primary-color, 0.9), rgba(#764ba2, 0.9));
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity $transition-speed ease;

        .featured-info {
          text-align: center;
          color: $light-color;
          padding: 20px;

          h4 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 15px;
          }

          p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 25px;
            opacity: 0.9;
          }

          .featured-actions {
            display: flex;
            justify-content: center;
            gap: 15px;

            .featured-link {
              width: 50px;
              height: 50px;
              background: rgba($light-color, 0.2);
              border: 2px solid rgba($light-color, 0.3);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: $light-color;
              font-size: 1.3rem;
              transition: all $transition-speed ease;
              text-decoration: none;

              &:hover {
                background: $light-color;
                color: $primary-color;
                transform: scale(1.1) rotate(5deg);
              }
            }
          }
        }
      }
    }
  }
}

.projects-content {
  max-width: 1200px;
  margin: 0 auto;
}

.projects-grid {
  display: grid;
  gap: 30px;

  &.view-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }

  &.view-masonry {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    grid-auto-rows: masonry; // Future CSS feature

    // Fallback for browsers that don't support masonry
    @supports not (grid-auto-rows: masonry) {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;

      .project-card {
        width: calc(33.333% - 20px);
        margin: 10px;

        @media (max-width: 1024px) {
          width: calc(50% - 20px);
        }

        @media (max-width: 768px) {
          width: calc(100% - 20px);
        }
      }
    }
  }
}

.project-card {
  background: $light-color;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  transition: all $transition-speed ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  position: relative;
  will-change: transform;

  &:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);

    .project-image .project-overlay {
      opacity: 1;
    }

    .project-img {
      transform: scale(1.15);
    }

    .project-content {
      background: linear-gradient(135deg, rgba($primary-color, 0.02), rgba(#764ba2, 0.01));
    }
  }

  &.featured {
    border: 3px solid $primary-color;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      background: linear-gradient(135deg, $primary-color, #764ba2);
      border-radius: 23px;
      z-index: -1;
      opacity: 0.1;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, $primary-color, #764ba2);
      border-radius: 20px 20px 0 0;
    }

    .project-content {
      padding: 30px 25px;
    }
  }

  &.github-project {
    border-left: 4px solid #333;

    .project-image::after {
      content: '';
      position: absolute;
      top: 15px;
      right: 15px;
      width: 30px;
      height: 30px;
      background: rgba(#333, 0.8);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .project-image::before {
      content: '\f09b';
      font-family: 'Font Awesome 6 Brands';
      position: absolute;
      top: 15px;
      right: 15px;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
      z-index: 1;
    }
  }
}

.project-image {
  height: 200px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, $primary-color, #764ba2);

  .project-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform $transition-speed ease;
  }

  .project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($dark-color, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity $transition-speed ease;
  }

  .project-links {
    display: flex;
    gap: 20px;

    .project-link {
      width: 50px;
      height: 50px;
      background: $light-color;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $dark-color;
      font-size: 1.5rem;
      transition: all $transition-speed ease;
      text-decoration: none;

      &:hover {
        background: $primary-color;
        color: $light-color;
        transform: scale(1.1) rotate(5deg);
      }
    }
  }
}

.project-content {
  padding: 25px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;

  .project-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: $secondary-color;
    flex: 1;
    margin-right: 10px;
  }

  .project-badges {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
    flex-wrap: wrap;

    .featured-badge {
      background: linear-gradient(135deg, $primary-color, #764ba2);
      color: $light-color;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 700;
      display: flex;
      align-items: center;
      gap: 4px;
      box-shadow: 0 2px 8px rgba($primary-color, 0.3);
      animation: pulse-badge 2s infinite;

      i {
        font-size: 0.7rem;
      }
    }

    .github-badge {
      background: linear-gradient(135deg, #333, #555);
      color: $light-color;
      padding: 6px 10px;
      border-radius: 15px;
      font-size: 0.75rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 4px;
      box-shadow: 0 2px 8px rgba(#333, 0.2);

      i {
        font-size: 0.8rem;
      }
    }
  }
}

.project-description {
  color: $gray-color;
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.project-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 18px;
  flex-wrap: wrap;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba($primary-color, 0.08);
    padding: 6px 10px;
    border-radius: 12px;
    color: $secondary-color;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all $transition-speed ease;

    &:hover {
      background: rgba($primary-color, 0.15);
      transform: translateY(-1px);
    }

    i {
      color: $primary-color;
      font-size: 0.8rem;
    }

    span {
      font-weight: 600;
    }

    small {
      font-size: 0.75rem;
      opacity: 0.8;
      margin-left: 2px;
    }
  }
}

.project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 20px;

  .tech-tag {
    background: linear-gradient(135deg, rgba($primary-color, 0.1), rgba(#764ba2, 0.05));
    color: $primary-color;
    padding: 6px 12px;
    border-radius: 18px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba($primary-color, 0.2);
    transition: all $transition-speed ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba($primary-color, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover {
      background: linear-gradient(135deg, $primary-color, #764ba2);
      color: $light-color;
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 5px 15px rgba($primary-color, 0.3);

      &::before {
        left: 100%;
      }
    }

    &.more {
      background: rgba($gray-color, 0.1);
      color: $gray-color;
      border-color: rgba($gray-color, 0.2);
      font-weight: 500;

      &:hover {
        background: $gray-color;
        color: $light-color;
      }
    }
  }
}

.project-actions {
  display: flex;
  gap: 15px;

  .btn {
    padding: 8px 15px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 1rem;
    }
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 100px 20px;
  color: $gray-color;
  background: linear-gradient(135deg, rgba($primary-color, 0.02), rgba(#764ba2, 0.01));
  border-radius: 20px;
  margin: 40px 0;

  i {
    font-size: 5rem;
    color: $primary-color;
    margin-bottom: 30px;
    opacity: 0.6;
    animation: gentle-bounce 2s ease-in-out infinite;
  }

  h3 {
    font-size: 2rem;
    color: $secondary-color;
    margin-bottom: 20px;
    font-weight: 700;
  }

  p {
    font-size: 1.1rem;
    line-height: 1.7;
    max-width: 500px;
    margin: 0 auto 20px;
  }

  .link-btn {
    background: none;
    border: none;
    color: $primary-color;
    font-weight: 600;
    cursor: pointer;
    text-decoration: underline;
    transition: all $transition-speed ease;

    &:hover {
      color: #764ba2;
      transform: translateY(-1px);
    }
  }
}

// Additional Animations
@keyframes pulse-badge {
  0%, 100% {
    box-shadow: 0 2px 8px rgba($primary-color, 0.3);
  }
  50% {
    box-shadow: 0 4px 16px rgba($primary-color, 0.5);
  }
}

@keyframes gentle-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .project-card {
    animation: none;

    &:hover {
      transform: none;
    }
  }

  .skeleton-card {
    animation: none;
  }

  .skeleton-image,
  .skeleton-title,
  .skeleton-description,
  .skeleton-tag {
    animation: none;
  }
}

// Media Queries
@media (max-width: 768px) {
  .projects-container {
    padding: 80px 15px;
  }

  .section-header {
    margin-bottom: 50px;

    .section-title {
      font-size: 2.5rem;
      line-height: 1.2;
    }

    .section-description {
      font-size: 1rem;
      line-height: 1.6;
      margin-bottom: 25px;
    }
  }

  .projects-controls {
    margin-bottom: 30px;

    .toggle-btn {
      padding: 10px 20px;
      font-size: 0.95rem;
      gap: 6px;
    }
  }

  .loading-skeleton {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .skeleton-card {
    .skeleton-image {
      height: 180px;
    }

    .skeleton-content {
      padding: 20px;
    }
  }

  .error-container {
    margin: 40px auto;

    .error-content {
      padding: 40px 25px;

      i {
        font-size: 3rem;
      }

      h3 {
        font-size: 1.6rem;
      }

      p {
        font-size: 1rem;
      }
    }
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 25px;

    .project-card {
      &.featured {
        grid-column: span 1;
        border-width: 3px;
      }
    }
  }

  .project-card {
    .project-image {
      height: 180px;
    }

    .project-content {
      padding: 20px;
    }

    .project-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;

      .project-title {
        font-size: 1.3rem;
        margin-right: 0;
      }

      .project-badges {
        align-self: flex-end;
      }
    }

    .project-description {
      font-size: 0.95rem;
      line-height: 1.6;
      margin-bottom: 15px;
    }

    .project-stats {
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 12px;

      .stat-item {
        font-size: 0.85rem;

        i {
          font-size: 0.75rem;
        }
      }
    }

    .project-technologies {
      margin-bottom: 15px;

      .tech-tag {
        font-size: 0.8rem;
        padding: 4px 8px;
        margin: 2px 4px 2px 0;
      }
    }

    .project-actions {
      flex-direction: column;
      gap: 10px;

      .btn {
        width: 100%;
        justify-content: center;
        padding: 10px 15px;
        font-size: 0.9rem;
      }
    }

    &.featured .project-content {
      padding: 25px 20px;
    }
  }

  .empty-state {
    padding: 60px 20px;

    i {
      font-size: 3rem;
    }

    h3 {
      font-size: 1.6rem;
    }

    p {
      font-size: 1rem;
    }
  }
}

@media (max-width: 480px) {
  .projects-container {
    padding: 60px 10px;
  }

  .section-header {
    .section-title {
      font-size: 2rem;
    }

    .section-description {
      font-size: 0.95rem;
    }
  }

  .projects-controls {
    .toggle-btn {
      padding: 9px 18px;
      font-size: 0.9rem;
    }
  }

  .projects-grid {
    gap: 20px;
  }

  .project-card {
    .project-image {
      height: 160px;
    }

    .project-content {
      padding: 18px 15px;
    }

    .project-header {
      .project-title {
        font-size: 1.2rem;
      }

      .project-badges {
        .featured-badge,
        .github-badge {
          font-size: 0.75rem;
          padding: 4px 8px;
        }
      }
    }

    .project-description {
      font-size: 0.9rem;
    }

    .project-stats {
      .stat-item {
        font-size: 0.8rem;
      }
    }

    .project-technologies {
      .tech-tag {
        font-size: 0.75rem;
        padding: 3px 6px;
      }
    }

    .project-actions {
      .btn {
        padding: 9px 12px;
        font-size: 0.85rem;
      }
    }

    &.featured .project-content {
      padding: 20px 15px;
    }
  }
}