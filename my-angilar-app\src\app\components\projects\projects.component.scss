// Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$gray-color: #666;
$light-gray: #f8f9fa;
$transition-speed: 0.3s;

.projects-container {
  padding: 100px 20px;
  background-color: $light-gray;
  min-height: 100vh;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    color: $secondary-color;
    margin-bottom: 20px;
    position: relative;
  }

  .section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, $primary-color, #764ba2);
    margin: 0 auto 20px;
    border-radius: 2px;
  }

  .section-description {
    max-width: 700px;
    margin: 0 auto;
    color: $gray-color;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.projects-content {
  max-width: 1200px;
  margin: 0 auto;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
}

.project-card {
  background: $light-color;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all $transition-speed ease;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);

    .project-image .project-overlay {
      opacity: 1;
    }
  }

  &.featured {
    grid-column: span 2;

    .project-content {
      padding: 30px;
    }
  }
}

.project-image {
  height: 200px;
  background-color: $primary-color;
  position: relative;
  overflow: hidden;

  .project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($dark-color, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity $transition-speed ease;
  }

  .project-links {
    display: flex;
    gap: 20px;

    .project-link {
      width: 50px;
      height: 50px;
      background: $light-color;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $dark-color;
      font-size: 1.5rem;
      transition: all $transition-speed ease;

      &:hover {
        background: $primary-color;
        color: $light-color;
        transform: scale(1.1);
      }
    }
  }
}

.project-content {
  padding: 25px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .project-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: $secondary-color;
  }

  .featured-badge {
    background: linear-gradient(90deg, $primary-color, #764ba2);
    color: $light-color;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
  }
}

.project-description {
  color: $gray-color;
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;

  .tech-tag {
    background: $light-gray;
    color: $primary-color;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
  }
}

.project-actions {
  display: flex;
  gap: 15px;

  .btn {
    padding: 8px 15px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 1rem;
    }
  }
}

// Media Queries
@media (max-width: 768px) {
  .projects-container {
    padding: 80px 15px;
  }

  .section-header {
    margin-bottom: 40px;

    .section-title {
      font-size: 2.5rem;
    }

    .section-description {
      font-size: 1rem;
    }
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .project-card.featured {
    grid-column: span 1;
  }
}