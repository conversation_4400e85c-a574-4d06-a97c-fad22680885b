// Variables
$primary-color: #4a6cf7;
$secondary-color: #2c3e50;
$text-color: #333;
$light-color: #fff;
$dark-color: #222;
$gray-color: #666;
$light-gray: #f8f9fa;
$transition-speed: 0.3s;

.projects-container {
  padding: 100px 20px;
  background-color: $light-gray;
  min-height: 100vh;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    color: $secondary-color;
    margin-bottom: 20px;
    position: relative;
  }

  .section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, $primary-color, #764ba2);
    margin: 0 auto 20px;
    border-radius: 2px;
  }

  .section-description {
    max-width: 700px;
    margin: 0 auto 30px;
    color: $gray-color;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.projects-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;

  .toggle-btn {
    background: linear-gradient(90deg, $primary-color, #764ba2);
    color: $light-color;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all $transition-speed ease;
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba($primary-color, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.active {
      background: linear-gradient(90deg, #764ba2, $primary-color);
    }

    i {
      font-size: 1rem;
    }
  }
}

// Loading States
.loading-container {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
}

.skeleton-card {
  background: $light-color;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s ease-in-out infinite alternate;

  .skeleton-image {
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .skeleton-content {
    padding: 25px;

    .skeleton-title {
      height: 24px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
      border-radius: 4px;
      margin-bottom: 15px;
    }

    .skeleton-description {
      height: 60px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .skeleton-tags {
      display: flex;
      gap: 10px;

      .skeleton-tag {
        height: 24px;
        width: 60px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
        border-radius: 12px;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

// Error States
.error-container {
  max-width: 600px;
  margin: 60px auto;
  text-align: center;
}

.error-content {
  background: $light-color;
  padding: 60px 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  i {
    font-size: 4rem;
    color: #ff6b6b;
    margin-bottom: 20px;
  }

  h3 {
    font-size: 1.8rem;
    color: $secondary-color;
    margin-bottom: 15px;
    font-weight: 600;
  }

  p {
    color: $gray-color;
    font-size: 1.1rem;
    margin-bottom: 30px;
    line-height: 1.6;
  }

  .btn {
    padding: 12px 24px;
    font-size: 1rem;
  }
}

.projects-content {
  max-width: 1200px;
  margin: 0 auto;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
}

.project-card {
  background: $light-color;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all $transition-speed ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;

  &:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);

    .project-image .project-overlay {
      opacity: 1;
    }

    .project-img {
      transform: scale(1.1);
    }
  }

  &.featured {
    grid-column: span 2;
    border: 2px solid $primary-color;

    .project-content {
      padding: 30px;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $primary-color, #764ba2);
    }
  }
}

.project-image {
  height: 200px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, $primary-color, #764ba2);

  .project-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform $transition-speed ease;
  }

  .project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba($dark-color, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity $transition-speed ease;
  }

  .project-links {
    display: flex;
    gap: 20px;

    .project-link {
      width: 50px;
      height: 50px;
      background: $light-color;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $dark-color;
      font-size: 1.5rem;
      transition: all $transition-speed ease;
      text-decoration: none;

      &:hover {
        background: $primary-color;
        color: $light-color;
        transform: scale(1.1) rotate(5deg);
      }
    }
  }
}

.project-content {
  padding: 25px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;

  .project-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: $secondary-color;
    flex: 1;
    margin-right: 10px;
  }

  .project-badges {
    display: flex;
    gap: 8px;
    flex-shrink: 0;

    .featured-badge {
      background: linear-gradient(90deg, $primary-color, #764ba2);
      color: $light-color;
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .github-badge {
      background: rgba($secondary-color, 0.1);
      color: $secondary-color;
      padding: 5px 8px;
      border-radius: 15px;
      font-size: 0.8rem;
      display: flex;
      align-items: center;

      i {
        font-size: 0.9rem;
      }
    }
  }
}

.project-description {
  color: $gray-color;
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.project-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: $gray-color;
    font-size: 0.9rem;

    i {
      color: $primary-color;
      font-size: 0.8rem;
    }

    span {
      font-weight: 500;
    }
  }
}

.project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;

  .tech-tag {
    background: $light-gray;
    color: $primary-color;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
  }
}

.project-actions {
  display: flex;
  gap: 15px;

  .btn {
    padding: 8px 15px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 1rem;
    }
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: $gray-color;

  i {
    font-size: 4rem;
    color: $primary-color;
    margin-bottom: 20px;
    opacity: 0.7;
  }

  h3 {
    font-size: 1.8rem;
    color: $secondary-color;
    margin-bottom: 15px;
    font-weight: 600;
  }

  p {
    font-size: 1.1rem;
    line-height: 1.6;
    max-width: 400px;
    margin: 0 auto;
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .project-card {
    animation: none;

    &:hover {
      transform: none;
    }
  }

  .skeleton-card {
    animation: none;
  }

  .skeleton-image,
  .skeleton-title,
  .skeleton-description,
  .skeleton-tag {
    animation: none;
  }
}

// Media Queries
@media (max-width: 768px) {
  .projects-container {
    padding: 80px 15px;
  }

  .section-header {
    margin-bottom: 50px;

    .section-title {
      font-size: 2.5rem;
      line-height: 1.2;
    }

    .section-description {
      font-size: 1rem;
      line-height: 1.6;
      margin-bottom: 25px;
    }
  }

  .projects-controls {
    margin-bottom: 30px;

    .toggle-btn {
      padding: 10px 20px;
      font-size: 0.95rem;
      gap: 6px;
    }
  }

  .loading-skeleton {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .skeleton-card {
    .skeleton-image {
      height: 180px;
    }

    .skeleton-content {
      padding: 20px;
    }
  }

  .error-container {
    margin: 40px auto;

    .error-content {
      padding: 40px 25px;

      i {
        font-size: 3rem;
      }

      h3 {
        font-size: 1.6rem;
      }

      p {
        font-size: 1rem;
      }
    }
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 25px;

    .project-card {
      &.featured {
        grid-column: span 1;
        border-width: 3px;
      }
    }
  }

  .project-card {
    .project-image {
      height: 180px;
    }

    .project-content {
      padding: 20px;
    }

    .project-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;

      .project-title {
        font-size: 1.3rem;
        margin-right: 0;
      }

      .project-badges {
        align-self: flex-end;
      }
    }

    .project-description {
      font-size: 0.95rem;
      line-height: 1.6;
      margin-bottom: 15px;
    }

    .project-stats {
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 12px;

      .stat-item {
        font-size: 0.85rem;

        i {
          font-size: 0.75rem;
        }
      }
    }

    .project-technologies {
      margin-bottom: 15px;

      .tech-tag {
        font-size: 0.8rem;
        padding: 4px 8px;
        margin: 2px 4px 2px 0;
      }
    }

    .project-actions {
      flex-direction: column;
      gap: 10px;

      .btn {
        width: 100%;
        justify-content: center;
        padding: 10px 15px;
        font-size: 0.9rem;
      }
    }

    &.featured .project-content {
      padding: 25px 20px;
    }
  }

  .empty-state {
    padding: 60px 20px;

    i {
      font-size: 3rem;
    }

    h3 {
      font-size: 1.6rem;
    }

    p {
      font-size: 1rem;
    }
  }
}

@media (max-width: 480px) {
  .projects-container {
    padding: 60px 10px;
  }

  .section-header {
    .section-title {
      font-size: 2rem;
    }

    .section-description {
      font-size: 0.95rem;
    }
  }

  .projects-controls {
    .toggle-btn {
      padding: 9px 18px;
      font-size: 0.9rem;
    }
  }

  .projects-grid {
    gap: 20px;
  }

  .project-card {
    .project-image {
      height: 160px;
    }

    .project-content {
      padding: 18px 15px;
    }

    .project-header {
      .project-title {
        font-size: 1.2rem;
      }

      .project-badges {
        .featured-badge,
        .github-badge {
          font-size: 0.75rem;
          padding: 4px 8px;
        }
      }
    }

    .project-description {
      font-size: 0.9rem;
    }

    .project-stats {
      .stat-item {
        font-size: 0.8rem;
      }
    }

    .project-technologies {
      .tech-tag {
        font-size: 0.75rem;
        padding: 3px 6px;
      }
    }

    .project-actions {
      .btn {
        padding: 9px 12px;
        font-size: 0.85rem;
      }
    }

    &.featured .project-content {
      padding: 20px 15px;
    }
  }
}